/**
 * This package contains codecs for numbers of different sizes and endianness.
 * It can be used standalone, but it is also exported as part of Kit
 * [`@solana/kit`](https://github.com/anza-xyz/kit/tree/main/packages/kit).
 *
 * This package is also part of the [`@solana/codecs` package](https://github.com/anza-xyz/kit/tree/main/packages/codecs)
 * which acts as an entry point for all codec packages as well as for their documentation.
 *
 * @packageDocumentation
 */
export * from './assertions';
export * from './common';
export * from './f32';
export * from './f64';
export * from './i128';
export * from './i16';
export * from './i32';
export * from './i64';
export * from './i8';
export * from './short-u16';
export * from './u128';
export * from './u16';
export * from './u32';
export * from './u64';
export * from './u8';
//# sourceMappingURL=index.d.ts.map