{"version": 3, "sources": ["../src/assertions.ts", "../src/common.ts", "../src/utils.ts", "../src/f32.ts", "../src/f64.ts", "../src/i128.ts", "../src/i16.ts", "../src/i32.ts", "../src/i64.ts", "../src/i8.ts", "../src/short-u16.ts", "../src/u128.ts", "../src/u16.ts", "../src/u32.ts", "../src/u64.ts", "../src/u8.ts"], "names": ["<PERSON><PERSON>", "combineCodec", "createEncoder", "createDecoder"], "mappings": ";;;;AA2BO,SAAS,6BACZ,CAAA,gBAAA,EACA,GACA,EAAA,GAAA,EACA,KACF,EAAA;AACE,EAAI,IAAA,KAAA,GAAQ,GAAO,IAAA,KAAA,GAAQ,GAAK,EAAA;AAC5B,IAAM,MAAA,IAAI,YAAY,yCAA2C,EAAA;AAAA,MAC7D,gBAAA;AAAA,MACA,GAAA;AAAA,MACA,GAAA;AAAA,MACA;AAAA,KACH,CAAA;AAAA;AAET;;;ACiDY,IAAA,MAAA,qBAAAA,OAAL,KAAA;AACH,EAAAA,OAAA,CAAA,OAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAA;AACA,EAAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA;AAFQ,EAAAA,OAAAA,OAAAA;AAAA,CAAA,EAAA,MAAA,IAAA,EAAA;AC7DZ,SAAS,eAAe,MAAqC,EAAA;AACzD,EAAO,OAAA,MAAA,EAAQ,yBAAwB,KAAQ,GAAA,IAAA;AACnD;AAEO,SAAS,qBACZ,KAC8B,EAAA;AAC9B,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,WAAW,KAAM,CAAA,IAAA;AAAA,IACjB,KAAA,CAAM,KAAc,EAAA,KAAA,EAAmB,MAAwB,EAAA;AAC3D,MAAA,IAAI,MAAM,KAAO,EAAA;AACb,QAA8B,6BAAA,CAAA,KAAA,CAAM,IAAM,EAAA,KAAA,CAAM,KAAM,CAAA,CAAC,GAAG,KAAM,CAAA,KAAA,CAAM,CAAC,CAAA,EAAG,KAAK,CAAA;AAAA;AAEnF,MAAA,MAAM,WAAc,GAAA,IAAI,WAAY,CAAA,KAAA,CAAM,IAAI,CAAA;AAC9C,MAAM,KAAA,CAAA,GAAA,CAAI,IAAI,QAAS,CAAA,WAAW,GAAG,KAAO,EAAA,cAAA,CAAe,KAAM,CAAA,MAAM,CAAC,CAAA;AACxE,MAAA,KAAA,CAAM,GAAI,CAAA,IAAI,UAAW,CAAA,WAAW,GAAG,MAAM,CAAA;AAC7C,MAAA,OAAO,SAAS,KAAM,CAAA,IAAA;AAAA;AAC1B,GACH,CAAA;AACL;AAEO,SAAS,qBACZ,KAC4B,EAAA;AAC5B,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,WAAW,KAAM,CAAA,IAAA;AAAA,IACjB,IAAA,CAAK,KAAO,EAAA,MAAA,GAAS,CAAkB,EAAA;AACnC,MAAkC,iCAAA,CAAA,KAAA,CAAM,IAAM,EAAA,KAAA,EAAO,MAAM,CAAA;AAC3D,MAAA,qCAAA,CAAsC,KAAM,CAAA,IAAA,EAAM,KAAM,CAAA,IAAA,EAAM,OAAO,MAAM,CAAA;AAC3E,MAAM,MAAA,IAAA,GAAO,IAAI,QAAS,CAAA,aAAA,CAAc,OAAO,MAAQ,EAAA,KAAA,CAAM,IAAI,CAAC,CAAA;AAClE,MAAO,OAAA,CAAC,KAAM,CAAA,GAAA,CAAI,IAAM,EAAA,cAAA,CAAe,KAAM,CAAA,MAAM,CAAC,CAAA,EAAG,MAAS,GAAA,KAAA,CAAM,IAAI,CAAA;AAAA;AAC9E,GACH,CAAA;AACL;AAMA,SAAS,aAAA,CAAc,KAAwC,EAAA,MAAA,EAAiB,MAA8B,EAAA;AAC1G,EAAM,MAAA,WAAA,GAAc,KAAM,CAAA,UAAA,IAAc,MAAU,IAAA,CAAA,CAAA;AAClD,EAAM,MAAA,WAAA,GAAc,UAAU,KAAM,CAAA,UAAA;AACpC,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,KAAM,CAAA,WAAA,EAAa,cAAc,WAAW,CAAA;AACpE;;;AC/CO,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,UAAA,CAAW,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC9D,IAAM,EAAA;AACV,CAAC;AAsBE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,UAAA,CAAW,GAAG,EAAE,CAAA;AAAA,EACxC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AA2CQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrD,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC;AC9EtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,UAAA,CAAW,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC9D,IAAM,EAAA;AACV,CAAC;AAsBE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,UAAA,CAAW,GAAG,EAAE,CAAA;AAAA,EACxC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AA2CQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDC,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC;AC9EtD,IAAM,cAAiB,GAAA,CAAC,MAA4B,GAAA,OACvD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,MAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,oCAAoC,CAAI,GAAA,EAAA,EAAI,MAAO,CAAA,oCAAoC,CAAC,CAAA;AAAA,EACxG,GAAK,EAAA,CAAC,IAAM,EAAA,KAAA,EAAO,EAAO,KAAA;AACtB,IAAM,MAAA,UAAA,GAAa,KAAK,CAAI,GAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAc,KAAK,CAAI,GAAA,CAAA;AAC7B,IAAA,MAAM,SAAY,GAAA,mBAAA;AAClB,IAAA,IAAA,CAAK,YAAY,UAAY,EAAA,MAAA,CAAO,KAAK,CAAA,IAAK,KAAK,EAAE,CAAA;AACrD,IAAA,IAAA,CAAK,aAAa,WAAa,EAAA,MAAA,CAAO,KAAK,CAAA,GAAI,WAAW,EAAE,CAAA;AAAA,GAChE;AAAA,EACA,IAAM,EAAA;AACV,CAAC;AAyBE,IAAM,cAAiB,GAAA,CAAC,MAA4B,GAAA,OACvD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,GAAA,EAAK,CAAC,IAAA,EAAM,EAAO,KAAA;AACf,IAAM,MAAA,UAAA,GAAa,KAAK,CAAI,GAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAc,KAAK,CAAI,GAAA,CAAA;AAC7B,IAAA,MAAM,IAAO,GAAA,IAAA,CAAK,WAAY,CAAA,UAAA,EAAY,EAAE,CAAA;AAC5C,IAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,YAAa,CAAA,WAAA,EAAa,EAAE,CAAA;AAC/C,IAAA,OAAA,CAAQ,QAAQ,GAAO,IAAA,KAAA;AAAA,GAC3B;AAAA,EACA,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AA4CQ,IAAA,YAAA,GAAe,CAAC,MAAA,GAA4B,EAAC,KACtDA,YAAa,CAAA,cAAA,CAAe,MAAM,CAAA,EAAG,cAAe,CAAA,MAAM,CAAC;AC/FxD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,QAAQ,CAAI,GAAA,CAAA,EAAG,MAAO,CAAA,QAAQ,CAAC,CAAA;AAAA,EAC/C,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,QAAA,CAAS,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC5D,IAAM,EAAA;AACV,CAAC;AAsBE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,QAAA,CAAS,GAAG,EAAE,CAAA;AAAA,EACtC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AA2CQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC;AC/EtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,YAAY,CAAI,GAAA,CAAA,EAAG,MAAO,CAAA,YAAY,CAAC,CAAA;AAAA,EACvD,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,QAAA,CAAS,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC5D,IAAM,EAAA;AACV,CAAC;AAsBE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,QAAA,CAAS,GAAG,EAAE,CAAA;AAAA,EACtC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AA2CQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC;AC/EtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,oBAAoB,CAAI,GAAA,EAAA,EAAI,MAAO,CAAA,oBAAoB,CAAC,CAAA;AAAA,EACxE,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,WAAA,CAAY,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC/D,IAAM,EAAA;AACV,CAAC;AAwBE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,WAAA,CAAY,GAAG,EAAE,CAAA;AAAA,EACzC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AA4CQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC;ACpFhD,IAAA,YAAA,GAAe,MACxB,oBAAqB,CAAA;AAAA,EACjB,IAAM,EAAA,IAAA;AAAA,EACN,KAAA,EAAO,CAAC,CAAC,MAAA,CAAO,MAAM,CAAI,GAAA,CAAA,EAAG,MAAO,CAAA,MAAM,CAAC,CAAA;AAAA,EAC3C,GAAA,EAAK,CAAC,IAAM,EAAA,KAAA,KAAU,KAAK,OAAQ,CAAA,CAAA,EAAG,MAAO,CAAA,KAAK,CAAC,CAAA;AAAA,EACnD,IAAM,EAAA;AACV,CAAC;AAqBQ,IAAA,YAAA,GAAe,MACxB,oBAAqB,CAAA;AAAA,EACjB,GAAK,EAAA,CAAA,IAAA,KAAQ,IAAK,CAAA,OAAA,CAAQ,CAAC,CAAA;AAAA,EAC3B,IAAM,EAAA,IAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AAkCE,IAAM,aAAa,MACtBA,YAAAA,CAAa,YAAa,EAAA,EAAG,cAAc;ACxDlC,IAAA,kBAAA,GAAqB,MAC9BC,aAAc,CAAA;AAAA,EACV,gBAAA,EAAkB,CAAC,KAAmC,KAAA;AAClD,IAAI,IAAA,KAAA,IAAS,KAAmB,OAAA,CAAA;AAChC,IAAI,IAAA,KAAA,IAAS,OAA2B,OAAA,CAAA;AACxC,IAAO,OAAA,CAAA;AAAA,GACX;AAAA,EACA,OAAS,EAAA,CAAA;AAAA,EACT,KAAO,EAAA,CAAC,KAAwB,EAAA,KAAA,EAAmB,MAA2B,KAAA;AAC1E,IAA8B,6BAAA,CAAA,UAAA,EAAY,CAAG,EAAA,KAAA,EAAO,KAAK,CAAA;AACzD,IAAM,MAAA,aAAA,GAAgB,CAAC,CAAC,CAAA;AACxB,IAAS,KAAA,IAAA,EAAA,GAAK,CAAK,IAAA,EAAA,IAAM,CAAG,EAAA;AAExB,MAAA,MAAM,YAAe,GAAA,MAAA,CAAO,KAAK,CAAA,IAAM,EAAK,GAAA,CAAA;AAC5C,MAAA,IAAI,iBAAiB,CAAG,EAAA;AAEpB,QAAA;AAAA;AAGJ,MAAA,MAAM,gBAAgB,GAAY,GAAA,YAAA;AAClC,MAAA,aAAA,CAAc,EAAE,CAAI,GAAA,aAAA;AACpB,MAAA,IAAI,KAAK,CAAG,EAAA;AAER,QAAc,aAAA,CAAA,EAAA,GAAK,CAAC,CAAK,IAAA,GAAA;AAAA;AAC7B;AAEJ,IAAM,KAAA,CAAA,GAAA,CAAI,eAAe,MAAM,CAAA;AAC/B,IAAA,OAAO,SAAS,aAAc,CAAA,MAAA;AAAA;AAEtC,CAAC;AAuBQ,IAAA,kBAAA,GAAqB,MAC9BC,aAAc,CAAA;AAAA,EACV,OAAS,EAAA,CAAA;AAAA,EACT,IAAA,EAAM,CAAC,KAAA,EAAwC,MAA6B,KAAA;AACxE,IAAA,IAAI,KAAQ,GAAA,CAAA;AACZ,IAAA,IAAI,SAAY,GAAA,CAAA;AAChB,IAAA,OAAO,EAAE,SAAW,EAAA;AAChB,MAAA,MAAM,YAAY,SAAY,GAAA,CAAA;AAC9B,MAAM,MAAA,WAAA,GAAc,KAAM,CAAA,MAAA,GAAS,SAAS,CAAA;AAC5C,MAAA,MAAM,gBAAgB,GAAY,GAAA,WAAA;AAElC,MAAA,KAAA,IAAS,iBAAkB,SAAY,GAAA,CAAA;AACvC,MAAK,IAAA,CAAA,WAAA,GAAc,SAAgB,CAAG,EAAA;AAElC,QAAA;AAAA;AACJ;AAEJ,IAAO,OAAA,CAAC,KAAO,EAAA,MAAA,GAAS,SAAS,CAAA;AAAA;AAEzC,CAAC;AAmDE,IAAM,mBAAmB,MAC5BF,YAAAA,CAAa,kBAAmB,EAAA,EAAG,oBAAoB;ACpIpD,IAAM,cAAiB,GAAA,CAAC,MAA4B,GAAA,OACvD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,CAAC,EAAI,EAAA,MAAA,CAAO,oCAAoC,CAAC,CAAA;AAAA,EACxD,GAAK,EAAA,CAAC,IAAM,EAAA,KAAA,EAAO,EAAO,KAAA;AACtB,IAAM,MAAA,UAAA,GAAa,KAAK,CAAI,GAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAc,KAAK,CAAI,GAAA,CAAA;AAC7B,IAAA,MAAM,SAAY,GAAA,mBAAA;AAClB,IAAA,IAAA,CAAK,aAAa,UAAY,EAAA,MAAA,CAAO,KAAK,CAAA,IAAK,KAAK,EAAE,CAAA;AACtD,IAAA,IAAA,CAAK,aAAa,WAAa,EAAA,MAAA,CAAO,KAAK,CAAA,GAAI,WAAW,EAAE,CAAA;AAAA,GAChE;AAAA,EACA,IAAM,EAAA;AACV,CAAC;AAsBE,IAAM,cAAiB,GAAA,CAAC,MAA4B,GAAA,OACvD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,GAAA,EAAK,CAAC,IAAA,EAAM,EAAO,KAAA;AACf,IAAM,MAAA,UAAA,GAAa,KAAK,CAAI,GAAA,CAAA;AAC5B,IAAM,MAAA,WAAA,GAAc,KAAK,CAAI,GAAA,CAAA;AAC7B,IAAA,MAAM,IAAO,GAAA,IAAA,CAAK,YAAa,CAAA,UAAA,EAAY,EAAE,CAAA;AAC7C,IAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,YAAa,CAAA,WAAA,EAAa,EAAE,CAAA;AAC/C,IAAA,OAAA,CAAQ,QAAQ,GAAO,IAAA,KAAA;AAAA,GAC3B;AAAA,EACA,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AA2CQ,IAAA,YAAA,GAAe,CAAC,MAAA,GAA4B,EAAC,KACtDA,YAAa,CAAA,cAAA,CAAe,MAAM,CAAA,EAAG,cAAe,CAAA,MAAM,CAAC;AC3FxD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAO,EAAA,CAAC,CAAG,EAAA,MAAA,CAAO,QAAQ,CAAC,CAAA;AAAA,EAC3B,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,SAAA,CAAU,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC7D,IAAM,EAAA;AACV,CAAC;AAsBE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,SAAA,CAAU,GAAG,EAAE,CAAA;AAAA,EACvC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AAyCQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC;AC7EtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAO,EAAA,CAAC,CAAG,EAAA,MAAA,CAAO,YAAY,CAAC,CAAA;AAAA,EAC/B,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,SAAA,CAAU,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAC7D,IAAM,EAAA;AACV,CAAC;AAsBE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,SAAA,CAAU,GAAG,EAAE,CAAA;AAAA,EACvC,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AAyCQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC;AC7EtD,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,IAAM,EAAA,KAAA;AAAA,EACN,KAAO,EAAA,CAAC,EAAI,EAAA,MAAA,CAAO,oBAAoB,CAAC,CAAA;AAAA,EACxC,GAAA,EAAK,CAAC,IAAA,EAAM,KAAO,EAAA,EAAA,KAAO,IAAK,CAAA,YAAA,CAAa,CAAG,EAAA,MAAA,CAAO,KAAK,CAAA,EAAG,EAAE,CAAA;AAAA,EAChE,IAAM,EAAA;AACV,CAAC;AAsBE,IAAM,aAAgB,GAAA,CAAC,MAA4B,GAAA,OACtD,oBAAqB,CAAA;AAAA,EACjB,MAAA;AAAA,EACA,KAAK,CAAC,IAAA,EAAM,OAAO,IAAK,CAAA,YAAA,CAAa,GAAG,EAAE,CAAA;AAAA,EAC1C,IAAM,EAAA,KAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AA4CQ,IAAA,WAAA,GAAc,CAAC,MAAA,GAA4B,EAAC,KACrDA,YAAa,CAAA,aAAA,CAAc,MAAM,CAAA,EAAG,aAAc,CAAA,MAAM,CAAC;ACnFhD,IAAA,YAAA,GAAe,MACxB,oBAAqB,CAAA;AAAA,EACjB,IAAM,EAAA,IAAA;AAAA,EACN,KAAO,EAAA,CAAC,CAAG,EAAA,MAAA,CAAO,MAAM,CAAC,CAAA;AAAA,EACzB,GAAA,EAAK,CAAC,IAAM,EAAA,KAAA,KAAU,KAAK,QAAS,CAAA,CAAA,EAAG,MAAO,CAAA,KAAK,CAAC,CAAA;AAAA,EACpD,IAAM,EAAA;AACV,CAAC;AAoBQ,IAAA,YAAA,GAAe,MACxB,oBAAqB,CAAA;AAAA,EACjB,GAAK,EAAA,CAAA,IAAA,KAAQ,IAAK,CAAA,QAAA,CAAS,CAAC,CAAA;AAAA,EAC5B,IAAM,EAAA,IAAA;AAAA,EACN,IAAM,EAAA;AACV,CAAC;AAgCE,IAAM,aAAa,MACtBA,YAAAA,CAAa,YAAa,EAAA,EAAG,cAAc", "file": "index.node.mjs", "sourcesContent": ["import { SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE, SolanaError } from '@solana/errors';\n\n/**\n * Ensures that a given number falls within a specified range.\n *\n * If the number is outside the allowed range, an error is thrown.\n * This function is primarily used to validate values before encoding them in a codec.\n *\n * @param codecDescription - A string describing the codec that is performing the validation.\n * @param min - The minimum allowed value (inclusive).\n * @param max - The maximum allowed value (inclusive).\n * @param value - The number to validate.\n *\n * @throws {@link SolanaError} if the value is out of range.\n *\n * @example\n * Validating a number within range.\n * ```ts\n * assertNumberIsBetweenForCodec('u8', 0, 255, 42); // Passes\n * ```\n *\n * @example\n * Throwing an error for an out-of-range value.\n * ```ts\n * assertNumberIsBetweenForCodec('u8', 0, 255, 300); // Throws\n * ```\n */\nexport function assertNumberIsBetweenForCodec(\n    codecDescription: string,\n    min: bigint | number,\n    max: bigint | number,\n    value: bigint | number,\n) {\n    if (value < min || value > max) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE, {\n            codecDescription,\n            max,\n            min,\n            value,\n        });\n    }\n}\n", "import { Codec, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n/**\n * Represents an encoder for numbers and bigints.\n *\n * This type allows encoding values that are either `number` or `bigint`.\n * Depending on the specific implementation, the encoded output may have a fixed or variable size.\n *\n * @see {@link FixedSizeNumberEncoder}\n */\nexport type NumberEncoder = Encoder<bigint | number>;\n\n/**\n * Represents a fixed-size encoder for numbers and bigints.\n *\n * This encoder serializes values using an exact number of bytes, defined by `TSize`.\n *\n * @typeParam TSize - The number of bytes used for encoding.\n *\n * @see {@link NumberEncoder}\n */\nexport type FixedSizeNumberEncoder<TSize extends number = number> = FixedSizeEncoder<bigint | number, TSize>;\n\n/**\n * Represents a decoder for numbers and bigints.\n *\n * This type supports decoding values as either `number` or `bigint`, depending on the implementation.\n *\n * @see {@link FixedSizeNumberDecoder}\n */\nexport type NumberDecoder = Decoder<bigint> | Decoder<number>;\n\n/**\n * Represents a fixed-size decoder for numbers and bigints.\n *\n * This decoder reads a fixed number of bytes (`TSize`) and converts them into a `number` or `bigint`.\n *\n * @typeParam TSize - The number of bytes expected for decoding.\n *\n * @see {@link NumberDecoder}\n */\nexport type FixedSizeNumberDecoder<TSize extends number = number> =\n    | FixedSizeDecoder<bigint, TSize>\n    | FixedSizeDecoder<number, TSize>;\n\n/**\n * Represents a codec for encoding and decoding numbers and bigints.\n *\n * - The encoded value can be either a `number` or a `bigint`.\n * - The decoded value will always be either a `number` or `bigint`, depending on the implementation.\n *\n * @see {@link FixedSizeNumberCodec}\n */\nexport type NumberCodec = Codec<bigint | number, bigint> | Codec<bigint | number, number>;\n\n/**\n * Represents a fixed-size codec for encoding and decoding numbers and bigints.\n *\n * This codec uses a specific number of bytes (`TSize`) for serialization.\n * The encoded value can be either a `number` or `bigint`, but the decoded value will always be a `number` or `bigint`,\n * depending on the implementation.\n *\n * @typeParam TSize - The number of bytes used for encoding and decoding.\n *\n * @see {@link NumberCodec}\n */\nexport type FixedSizeNumberCodec<TSize extends number = number> =\n    | FixedSizeCodec<bigint | number, bigint, TSize>\n    | FixedSizeCodec<bigint | number, number, TSize>;\n\n/**\n * Configuration options for number codecs that use more than one byte.\n *\n * This configuration applies to all number codecs except `u8` and `i8`,\n * allowing the user to specify the endianness of serialization.\n */\nexport type NumberCodecConfig = {\n    /**\n     * Specifies whether numbers should be encoded in little-endian or big-endian format.\n     *\n     * @defaultValue `Endian.Little`\n     */\n    endian?: Endian;\n};\n\n/**\n * Defines the byte order used for number serialization.\n *\n * - `Little`: The least significant byte is stored first.\n * - `Big`: The most significant byte is stored first.\n */\nexport enum Endian {\n    Little,\n    Big,\n}\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    assertByteArrayIsNotEmptyForCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    Offset,\n    ReadonlyUint8Array,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\nimport { Endian, NumberCodecConfig } from './common';\n\ntype NumberFactorySharedInput<TSize extends number> = {\n    config?: NumberCodecConfig;\n    name: string;\n    size: TSize;\n};\n\ntype NumberFactoryEncoderInput<TFrom, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    range?: [bigint | number, bigint | number];\n    set: (view: DataView, value: TFrom, littleEndian?: boolean) => void;\n};\n\ntype NumberFactoryDecoderInput<TTo, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    get: (view: DataView, littleEndian?: boolean) => TTo;\n};\n\nfunction isLittleEndian(config?: NumberCodecConfig): boolean {\n    return config?.endian === Endian.Big ? false : true;\n}\n\nexport function numberEncoderFactory<TFrom extends bigint | number, TSize extends number>(\n    input: NumberFactoryEncoderInput<TFrom, TSize>,\n): FixedSizeEncoder<TFrom, TSize> {\n    return createEncoder({\n        fixedSize: input.size,\n        write(value: TFrom, bytes: Uint8Array, offset: Offset): Offset {\n            if (input.range) {\n                assertNumberIsBetweenForCodec(input.name, input.range[0], input.range[1], value);\n            }\n            const arrayBuffer = new ArrayBuffer(input.size);\n            input.set(new DataView(arrayBuffer), value, isLittleEndian(input.config));\n            bytes.set(new Uint8Array(arrayBuffer), offset);\n            return offset + input.size;\n        },\n    });\n}\n\nexport function numberDecoderFactory<TTo extends bigint | number, TSize extends number>(\n    input: NumberFactoryDecoderInput<TTo, TSize>,\n): FixedSizeDecoder<TTo, TSize> {\n    return createDecoder({\n        fixedSize: input.size,\n        read(bytes, offset = 0): [TTo, number] {\n            assertByteArrayIsNotEmptyForCodec(input.name, bytes, offset);\n            assertByteArrayHasEnoughBytesForCodec(input.name, input.size, bytes, offset);\n            const view = new DataView(toArrayBuffer(bytes, offset, input.size));\n            return [input.get(view, isLittleEndian(input.config)), offset + input.size];\n        },\n    });\n}\n\n/**\n * Helper function to ensure that the ArrayBuffer is converted properly from a Uint8Array\n * Source: https://stackoverflow.com/questions/37228285/uint8array-to-arraybuffer\n */\nfunction toArrayBuffer(bytes: ReadonlyUint8Array | Uint8Array, offset?: number, length?: number): ArrayBuffer {\n    const bytesOffset = bytes.byteOffset + (offset ?? 0);\n    const bytesLength = length ?? bytes.byteLength;\n    return bytes.buffer.slice(bytesOffset, bytesOffset + bytesLength);\n}\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 32-bit floating-point numbers (`f32`).\n *\n * This encoder serializes `f32` values using 4 bytes.\n * Floating-point values may lose precision when encoded.\n *\n * For more details, see {@link getF32Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeEncoder<number, 4>` for encoding `f32` values.\n *\n * @example\n * Encoding an `f32` value.\n * ```ts\n * const encoder = getF32Encoder();\n * const bytes = encoder.encode(-1.5); // 0x0000c0bf\n * ```\n *\n * @see {@link getF32Codec}\n */\nexport const getF32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'f32',\n        set: (view, value, le) => view.setFloat32(0, Number(value), le),\n        size: 4,\n    });\n\n/**\n * Returns a decoder for 32-bit floating-point numbers (`f32`).\n *\n * This decoder deserializes `f32` values from 4 bytes.\n * Some precision may be lost during decoding due to floating-point representation.\n *\n * For more details, see {@link getF32Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeDecoder<number, 4>` for decoding `f32` values.\n *\n * @example\n * Decoding an `f32` value.\n * ```ts\n * const decoder = getF32Decoder();\n * const value = decoder.decode(new Uint8Array([0x00, 0x00, 0xc0, 0xbf])); // -1.5\n * ```\n *\n * @see {@link getF32Codec}\n */\nexport const getF32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat32(0, le),\n        name: 'f32',\n        size: 4,\n    });\n\n/**\n * Returns a codec for encoding and decoding 32-bit floating-point numbers (`f32`).\n *\n * This codec serializes `f32` values using 4 bytes.\n * Due to the IEEE 754 floating-point representation, some precision loss may occur.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeCodec<number, number, 4>` for encoding and decoding `f32` values.\n *\n * @example\n * Encoding and decoding an `f32` value.\n * ```ts\n * const codec = getF32Codec();\n * const bytes = codec.encode(-1.5); // 0x0000c0bf\n * const value = codec.decode(bytes); // -1.5\n * ```\n *\n * @example\n * Using big-endian encoding.\n * ```ts\n * const codec = getF32Codec({ endian: Endian.Big });\n * const bytes = codec.encode(-1.5); // 0xbfc00000\n * ```\n *\n * @remarks\n * `f32` values follow the IEEE 754 single-precision floating-point standard.\n * Precision loss may occur for certain values.\n *\n * - If you need higher precision, consider using {@link getF64Codec}.\n * - If you need integer values, consider using {@link getI32Codec} or {@link getU32Codec}.\n *\n * Separate {@link getF32Encoder} and {@link getF32Decoder} functions are available.\n *\n * ```ts\n * const bytes = getF32Encoder().encode(-1.5);\n * const value = getF32Decoder().decode(bytes);\n * ```\n *\n * @see {@link getF32Encoder}\n * @see {@link getF32Decoder}\n */\nexport const getF32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 4> =>\n    combineCodec(getF32Encoder(config), getF32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 64-bit floating-point numbers (`f64`).\n *\n * This encoder serializes `f64` values using 8 bytes.\n * Floating-point values may lose precision when encoded.\n *\n * For more details, see {@link getF64Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeEncoder<number, 8>` for encoding `f64` values.\n *\n * @example\n * Encoding an `f64` value.\n * ```ts\n * const encoder = getF64Encoder();\n * const bytes = encoder.encode(-1.5); // 0x000000000000f8bf\n * ```\n *\n * @see {@link getF64Codec}\n */\nexport const getF64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'f64',\n        set: (view, value, le) => view.setFloat64(0, Number(value), le),\n        size: 8,\n    });\n\n/**\n * Returns a decoder for 64-bit floating-point numbers (`f64`).\n *\n * This decoder deserializes `f64` values from 8 bytes.\n * Some precision may be lost during decoding due to floating-point representation.\n *\n * For more details, see {@link getF64Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeDecoder<number, 8>` for decoding `f64` values.\n *\n * @example\n * Decoding an `f64` value.\n * ```ts\n * const decoder = getF64Decoder();\n * const value = decoder.decode(new Uint8Array([0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0xbf])); // -1.5\n * ```\n *\n * @see {@link getF64Codec}\n */\nexport const getF64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat64(0, le),\n        name: 'f64',\n        size: 8,\n    });\n\n/**\n * Returns a codec for encoding and decoding 64-bit floating-point numbers (`f64`).\n *\n * This codec serializes `f64` values using 8 bytes.\n * Due to the IEEE 754 floating-point representation, some precision loss may occur.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeCodec<number, number, 8>` for encoding and decoding `f64` values.\n *\n * @example\n * Encoding and decoding an `f64` value.\n * ```ts\n * const codec = getF64Codec();\n * const bytes = codec.encode(-1.5); // 0x000000000000f8bf\n * const value = codec.decode(bytes); // -1.5\n * ```\n *\n * @example\n * Using big-endian encoding.\n * ```ts\n * const codec = getF64Codec({ endian: Endian.Big });\n * const bytes = codec.encode(-1.5); // 0xbff8000000000000\n * ```\n *\n * @remarks\n * `f64` values follow the IEEE 754 double-precision floating-point standard.\n * Precision loss may still occur but is significantly lower than `f32`.\n *\n * - If you need smaller floating-point values, consider using {@link getF32Codec}.\n * - If you need integer values, consider using {@link getI64Codec} or {@link getU64Codec}.\n *\n * Separate {@link getF64Encoder} and {@link getF64Decoder} functions are available.\n *\n * ```ts\n * const bytes = getF64Encoder().encode(-1.5);\n * const value = getF64Decoder().decode(bytes);\n * ```\n *\n * @see {@link getF64Encoder}\n * @see {@link getF64Decoder}\n */\nexport const getF64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 8> =>\n    combineCodec(getF64Encoder(config), getF64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 128-bit signed integers (`i128`).\n *\n * This encoder serializes `i128` values using 16 bytes.\n * Values can be provided as either `number` or `bigint`.\n *\n * For more details, see {@link getI128Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeEncoder<number | bigint, 16>` for encoding `i128` values.\n *\n * @example\n * Encoding an `i128` value.\n * ```ts\n * const encoder = getI128Encoder();\n * const bytes = encoder.encode(-42n); // 0xd6ffffffffffffffffffffffffffffff\n * ```\n *\n * @see {@link getI128Codec}\n */\nexport const getI128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'i128',\n        range: [-BigInt('0x7fffffffffffffffffffffffffffffff') - 1n, BigInt('0x7fffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigInt64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\n/**\n * Returns a decoder for 128-bit signed integers (`i128`).\n *\n * This decoder deserializes `i128` values from 16 bytes.\n * The decoded value is always a `bigint`.\n *\n * For more details, see {@link getI128Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeDecoder<bigint, 16>` for decoding `i128` values.\n *\n * @example\n * Decoding an `i128` value.\n * ```ts\n * const decoder = getI128Decoder();\n * const value = decoder.decode(new Uint8Array([\n *   0xd6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,\n *   0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff\n * ])); // -42n\n * ```\n *\n * @see {@link getI128Codec}\n */\nexport const getI128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigInt64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'i128',\n        size: 16,\n    });\n\n/**\n * Returns a codec for encoding and decoding 128-bit signed integers (`i128`).\n *\n * This codec serializes `i128` values using 16 bytes.\n * Values can be provided as either `number` or `bigint`, but the decoded value is always a `bigint`.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeCodec<number | bigint, bigint, 16>` for encoding and decoding `i128` values.\n *\n * @example\n * Encoding and decoding an `i128` value.\n * ```ts\n * const codec = getI128Codec();\n * const bytes = codec.encode(-42n); // 0xd6ffffffffffffffffffffffffffffff\n * const value = codec.decode(bytes); // -42n\n * ```\n *\n * @example\n * Using big-endian encoding.\n * ```ts\n * const codec = getI128Codec({ endian: Endian.Big });\n * const bytes = codec.encode(-42n); // 0xffffffffffffffffffffffffffffd6\n * ```\n *\n * @remarks\n * This codec supports values between `-2^127` and `2^127 - 1`.\n * Since JavaScript `number` cannot safely represent values beyond `2^53 - 1`, the decoded value is always a `bigint`.\n *\n * - If you need a smaller signed integer, consider using {@link getI64Codec} or {@link getI32Codec}.\n * - If you need a larger signed integer, consider using a custom codec.\n * - If you need unsigned integers, consider using {@link getU128Codec}.\n *\n * Separate {@link getI128Encoder} and {@link getI128Decoder} functions are available.\n *\n * ```ts\n * const bytes = getI128Encoder().encode(-42);\n * const value = getI128Decoder().decode(bytes);\n * ```\n *\n * @see {@link getI128Encoder}\n * @see {@link getI128Decoder}\n */\nexport const getI128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, bigint, 16> =>\n    combineCodec(getI128Encoder(config), getI128Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 16-bit signed integers (`i16`).\n *\n * This encoder serializes `i16` values using 2 bytes.\n * Values can be provided as either `number` or `bigint`.\n *\n * For more details, see {@link getI16Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeEncoder<number | bigint, 2>` for encoding `i16` values.\n *\n * @example\n * Encoding an `i16` value.\n * ```ts\n * const encoder = getI16Encoder();\n * const bytes = encoder.encode(-42); // 0xd6ff\n * ```\n *\n * @see {@link getI16Codec}\n */\nexport const getI16Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 2> =>\n    numberEncoderFactory({\n        config,\n        name: 'i16',\n        range: [-Number('0x7fff') - 1, Number('0x7fff')],\n        set: (view, value, le) => view.setInt16(0, Number(value), le),\n        size: 2,\n    });\n\n/**\n * Returns a decoder for 16-bit signed integers (`i16`).\n *\n * This decoder deserializes `i16` values from 2 bytes.\n * The decoded value is always a `number`.\n *\n * For more details, see {@link getI16Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeDecoder<number, 2>` for decoding `i16` values.\n *\n * @example\n * Decoding an `i16` value.\n * ```ts\n * const decoder = getI16Decoder();\n * const value = decoder.decode(new Uint8Array([0xd6, 0xff])); // -42\n * ```\n *\n * @see {@link getI16Codec}\n */\nexport const getI16Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 2> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getInt16(0, le),\n        name: 'i16',\n        size: 2,\n    });\n\n/**\n * Returns a codec for encoding and decoding 16-bit signed integers (`i16`).\n *\n * This codec serializes `i16` values using 2 bytes.\n * Values can be provided as either `number` or `bigint`, but the decoded value is always a `number`.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeCodec<number | bigint, number, 2>` for encoding and decoding `i16` values.\n *\n * @example\n * Encoding and decoding an `i16` value.\n * ```ts\n * const codec = getI16Codec();\n * const bytes = codec.encode(-42); // 0xd6ff\n * const value = codec.decode(bytes); // -42\n * ```\n *\n * @example\n * Using big-endian encoding.\n * ```ts\n * const codec = getI16Codec({ endian: Endian.Big });\n * const bytes = codec.encode(-42); // 0xffd6\n * ```\n *\n * @remarks\n * This codec supports values between `-2^15` (`-32,768`) and `2^15 - 1` (`32,767`).\n *\n * - If you need a smaller signed integer, consider using {@link getI8Codec}.\n * - If you need a larger signed integer, consider using {@link getI32Codec}.\n * - If you need unsigned integers, consider using {@link getU16Codec}.\n *\n * Separate {@link getI16Encoder} and {@link getI16Decoder} functions are available.\n *\n * ```ts\n * const bytes = getI16Encoder().encode(-42);\n * const value = getI16Decoder().decode(bytes);\n * ```\n *\n * @see {@link getI16Encoder}\n * @see {@link getI16Decoder}\n */\nexport const getI16Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 2> =>\n    combineCodec(getI16Encoder(config), getI16Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 32-bit signed integers (`i32`).\n *\n * This encoder serializes `i32` values using 4 bytes.\n * Values can be provided as either `number` or `bigint`.\n *\n * For more details, see {@link getI32Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeEncoder<number | bigint, 4>` for encoding `i32` values.\n *\n * @example\n * Encoding an `i32` value.\n * ```ts\n * const encoder = getI32Encoder();\n * const bytes = encoder.encode(-42); // 0xd6ffffff\n * ```\n *\n * @see {@link getI32Codec}\n */\nexport const getI32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'i32',\n        range: [-Number('0x7fffffff') - 1, Number('0x7fffffff')],\n        set: (view, value, le) => view.setInt32(0, Number(value), le),\n        size: 4,\n    });\n\n/**\n * Returns a decoder for 32-bit signed integers (`i32`).\n *\n * This decoder deserializes `i32` values from 4 bytes.\n * The decoded value is always a `number`.\n *\n * For more details, see {@link getI32Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeDecoder<number, 4>` for decoding `i32` values.\n *\n * @example\n * Decoding an `i32` value.\n * ```ts\n * const decoder = getI32Decoder();\n * const value = decoder.decode(new Uint8Array([0xd6, 0xff, 0xff, 0xff])); // -42\n * ```\n *\n * @see {@link getI32Codec}\n */\nexport const getI32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getInt32(0, le),\n        name: 'i32',\n        size: 4,\n    });\n\n/**\n * Returns a codec for encoding and decoding 32-bit signed integers (`i32`).\n *\n * This codec serializes `i32` values using 4 bytes.\n * Values can be provided as either `number` or `bigint`, but the decoded value is always a `number`.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeCodec<number | bigint, number, 4>` for encoding and decoding `i32` values.\n *\n * @example\n * Encoding and decoding an `i32` value.\n * ```ts\n * const codec = getI32Codec();\n * const bytes = codec.encode(-42); // 0xd6ffffff\n * const value = codec.decode(bytes); // -42\n * ```\n *\n * @example\n * Using big-endian encoding.\n * ```ts\n * const codec = getI32Codec({ endian: Endian.Big });\n * const bytes = codec.encode(-42); // 0xffffffd6\n * ```\n *\n * @remarks\n * This codec supports values between `-2^31` (`-2,147,483,648`) and `2^31 - 1` (`2,147,483,647`).\n *\n * - If you need a smaller signed integer, consider using {@link getI16Codec} or {@link getI8Codec}.\n * - If you need a larger signed integer, consider using {@link getI64Codec}.\n * - If you need unsigned integers, consider using {@link getU32Codec}.\n *\n * Separate {@link getI32Encoder} and {@link getI32Decoder} functions are available.\n *\n * ```ts\n * const bytes = getI32Encoder().encode(-42);\n * const value = getI32Decoder().decode(bytes);\n * ```\n *\n * @see {@link getI32Encoder}\n * @see {@link getI32Decoder}\n */\nexport const getI32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 4> =>\n    combineCodec(getI32Encoder(config), getI32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 64-bit signed integers (`i64`).\n *\n * This encoder serializes `i64` values using 8 bytes.\n * Values can be provided as either `number` or `bigint`.\n *\n * For more details, see {@link getI64Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeEncoder<number | bigint, 8>` for encoding `i64` values.\n *\n * @example\n * Encoding an `i64` value.\n * ```ts\n * const encoder = getI64Encoder();\n * const bytes = encoder.encode(-42n); // 0xd6ffffffffffffff\n * ```\n *\n * @see {@link getI64Codec}\n */\nexport const getI64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'i64',\n        range: [-BigInt('0x7fffffffffffffff') - 1n, BigInt('0x7fffffffffffffff')],\n        set: (view, value, le) => view.setBigInt64(0, BigInt(value), le),\n        size: 8,\n    });\n\n/**\n * Returns a decoder for 64-bit signed integers (`i64`).\n *\n * This decoder deserializes `i64` values from 8 bytes.\n * The decoded value is always a `bigint`.\n *\n * For more details, see {@link getI64Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeDecoder<bigint, 8>` for decoding `i64` values.\n *\n * @example\n * Decoding an `i64` value.\n * ```ts\n * const decoder = getI64Decoder();\n * const value = decoder.decode(new Uint8Array([\n *   0xd6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff\n * ])); // -42n\n * ```\n *\n * @see {@link getI64Codec}\n */\nexport const getI64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getBigInt64(0, le),\n        name: 'i64',\n        size: 8,\n    });\n\n/**\n * Returns a codec for encoding and decoding 64-bit signed integers (`i64`).\n *\n * This codec serializes `i64` values using 8 bytes.\n * Values can be provided as either `number` or `bigint`, but the decoded value is always a `bigint`.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeCodec<number | bigint, bigint, 8>` for encoding and decoding `i64` values.\n *\n * @example\n * Encoding and decoding an `i64` value.\n * ```ts\n * const codec = getI64Codec();\n * const bytes = codec.encode(-42n); // 0xd6ffffffffffffff\n * const value = codec.decode(bytes); // -42n\n * ```\n *\n * @example\n * Using big-endian encoding.\n * ```ts\n * const codec = getI64Codec({ endian: Endian.Big });\n * const bytes = codec.encode(-42n); // 0xffffffffffffffd6\n * ```\n *\n * @remarks\n * This codec supports values between `-2^63` and `2^63 - 1`.\n * Since JavaScript `number` cannot safely represent values beyond `2^53 - 1`, the decoded value is always a `bigint`.\n *\n * - If you need a smaller signed integer, consider using {@link getI32Codec} or {@link getI16Codec}.\n * - If you need a larger signed integer, consider using {@link getI128Codec}.\n * - If you need unsigned integers, consider using {@link getU64Codec}.\n *\n * Separate {@link getI64Encoder} and {@link getI64Decoder} functions are available.\n *\n * ```ts\n * const bytes = getI64Encoder().encode(-42);\n * const value = getI64Decoder().decode(bytes);\n * ```\n *\n * @see {@link getI64Encoder}\n * @see {@link getI64Decoder}\n */\nexport const getI64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, bigint, 8> =>\n    combineCodec(getI64Encoder(config), getI64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 8-bit signed integers (`i8`).\n *\n * This encoder serializes `i8` values using 1 byte.\n * Values can be provided as either `number` or `bigint`.\n *\n * For more details, see {@link getI8Codec}.\n *\n * @returns A `FixedSizeEncoder<number | bigint, 1>` for encoding `i8` values.\n *\n * @example\n * Encoding an `i8` value.\n * ```ts\n * const encoder = getI8Encoder();\n * const bytes = encoder.encode(-42); // 0xd6\n * ```\n *\n * @see {@link getI8Codec}\n */\nexport const getI8Encoder = (): FixedSizeEncoder<bigint | number, 1> =>\n    numberEncoderFactory({\n        name: 'i8',\n        range: [-Number('0x7f') - 1, Number('0x7f')],\n        set: (view, value) => view.setInt8(0, Number(value)),\n        size: 1,\n    });\n\n/**\n * Returns a decoder for 8-bit signed integers (`i8`).\n *\n * This decoder deserializes `i8` values from 1 byte.\n * The decoded value is always a `number`.\n *\n * For more details, see {@link getI8Codec}.\n *\n * @returns A `FixedSizeDecoder<number, 1>` for decoding `i8` values.\n *\n * @example\n * Decoding an `i8` value.\n * ```ts\n * const decoder = getI8Decoder();\n * const value = decoder.decode(new Uint8Array([0xd6])); // -42\n * ```\n *\n * @see {@link getI8Codec}\n */\nexport const getI8Decoder = (): FixedSizeDecoder<number, 1> =>\n    numberDecoderFactory({\n        get: view => view.getInt8(0),\n        name: 'i8',\n        size: 1,\n    });\n\n/**\n * Returns a codec for encoding and decoding 8-bit signed integers (`i8`).\n *\n * This codec serializes `i8` values using 1 byte.\n * Values can be provided as either `number` or `bigint`, but the decoded value is always a `number`.\n *\n * @returns A `FixedSizeCodec<number | bigint, number, 1>` for encoding and decoding `i8` values.\n *\n * @example\n * Encoding and decoding an `i8` value.\n * ```ts\n * const codec = getI8Codec();\n * const bytes = codec.encode(-42); // 0xd6\n * const value = codec.decode(bytes); // -42\n * ```\n *\n * @remarks\n * This codec supports values between `-2^7` (`-128`) and `2^7 - 1` (`127`).\n *\n * - If you need a larger signed integer, consider using {@link getI16Codec}.\n * - If you need an unsigned integer, consider using {@link getU8Codec}.\n *\n * Separate {@link getI8Encoder} and {@link getI8Decoder} functions are available.\n *\n * ```ts\n * const bytes = getI8Encoder().encode(-42);\n * const value = getI8Decoder().decode(bytes);\n * ```\n *\n * @see {@link getI8Encoder}\n * @see {@link getI8Decoder}\n */\nexport const getI8Codec = (): FixedSizeCodec<bigint | number, number, 1> =>\n    combineCodec(getI8Encoder(), getI8Decoder());\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Offset,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\n\n/**\n * Returns an encoder for `shortU16` values.\n *\n * This encoder serializes `shortU16` values using **1 to 3 bytes**.\n * Smaller values use fewer bytes, while larger values take up more space.\n *\n * For more details, see {@link getShortU16Codec}.\n *\n * @returns A `VariableSizeEncoder<number | bigint>` for encoding `shortU16` values.\n *\n * @example\n * Encoding a `shortU16` value.\n * ```ts\n * const encoder = getShortU16Encoder();\n * encoder.encode(42);    // 0x2a\n * encoder.encode(128);   // 0x8001\n * encoder.encode(16384); // 0x808001\n * ```\n *\n * @see {@link getShortU16Codec}\n */\nexport const getShortU16Encoder = (): VariableSizeEncoder<bigint | number> =>\n    createEncoder({\n        getSizeFromValue: (value: bigint | number): number => {\n            if (value <= 0b01111111) return 1;\n            if (value <= 0b0011111111111111) return 2;\n            return 3;\n        },\n        maxSize: 3,\n        write: (value: bigint | number, bytes: Uint8Array, offset: Offset): Offset => {\n            assertNumberIsBetweenForCodec('shortU16', 0, 65535, value);\n            const shortU16Bytes = [0];\n            for (let ii = 0; ; ii += 1) {\n                // Shift the bits of the value over such that the next 7 bits are at the right edge.\n                const alignedValue = Number(value) >> (ii * 7);\n                if (alignedValue === 0) {\n                    // No more bits to consume.\n                    break;\n                }\n                // Extract those 7 bits using a mask.\n                const nextSevenBits = 0b1111111 & alignedValue;\n                shortU16Bytes[ii] = nextSevenBits;\n                if (ii > 0) {\n                    // Set the continuation bit of the previous slice.\n                    shortU16Bytes[ii - 1] |= 0b10000000;\n                }\n            }\n            bytes.set(shortU16Bytes, offset);\n            return offset + shortU16Bytes.length;\n        },\n    });\n\n/**\n * Returns a decoder for `shortU16` values.\n *\n * This decoder deserializes `shortU16` values from **1 to 3 bytes**.\n * The number of bytes used depends on the encoded value.\n *\n * For more details, see {@link getShortU16Codec}.\n *\n * @returns A `VariableSizeDecoder<number>` for decoding `shortU16` values.\n *\n * @example\n * Decoding a `shortU16` value.\n * ```ts\n * const decoder = getShortU16Decoder();\n * decoder.decode(new Uint8Array([0x2a]));             // 42\n * decoder.decode(new Uint8Array([0x80, 0x01]));       // 128\n * decoder.decode(new Uint8Array([0x80, 0x80, 0x01])); // 16384\n * ```\n *\n * @see {@link getShortU16Codec}\n */\nexport const getShortU16Decoder = (): VariableSizeDecoder<number> =>\n    createDecoder({\n        maxSize: 3,\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset): [number, Offset] => {\n            let value = 0;\n            let byteCount = 0;\n            while (++byteCount) {\n                const byteIndex = byteCount - 1;\n                const currentByte = bytes[offset + byteIndex];\n                const nextSevenBits = 0b1111111 & currentByte;\n                // Insert the next group of seven bits into the correct slot of the output value.\n                value |= nextSevenBits << (byteIndex * 7);\n                if ((currentByte & 0b10000000) === 0) {\n                    // This byte does not have its continuation bit set. We're done.\n                    break;\n                }\n            }\n            return [value, offset + byteCount];\n        },\n    });\n\n/**\n * Returns a codec for encoding and decoding `shortU16` values.\n *\n * It serializes unsigned integers using **1 to 3 bytes** based on the encoded value.\n * The larger the value, the more bytes it uses.\n *\n * - If the value is `<= 0x7f` (127), it is stored in a **single byte**\n *   and the first bit is set to `0` to indicate the end of the value.\n * - Otherwise, the first bit is set to `1` to indicate that the value continues in the next byte, which follows the same pattern.\n * - This process repeats until the value is fully encoded in up to 3 bytes. The third and last byte, if needed, uses all 8 bits to store the remaining value.\n *\n * In other words, the encoding scheme follows this structure:\n *\n * ```txt\n * 0XXXXXXX                   <- Values 0 to 127 (1 byte)\n * 1XXXXXXX 0XXXXXXX          <- Values 128 to 16,383 (2 bytes)\n * 1XXXXXXX 1XXXXXXX XXXXXXXX <- Values 16,384 to 4,194,303 (3 bytes)\n * ```\n *\n * @returns A `VariableSizeCodec<number | bigint, number>` for encoding and decoding `shortU16` values.\n *\n * @example\n * Encoding and decoding `shortU16` values.\n * ```ts\n * const codec = getShortU16Codec();\n * const bytes1 = codec.encode(42);    // 0x2a\n * const bytes2 = codec.encode(128);   // 0x8001\n * const bytes3 = codec.encode(16384); // 0x808001\n *\n * codec.decode(bytes1); // 42\n * codec.decode(bytes2); // 128\n * codec.decode(bytes3); // 16384\n * ```\n *\n * @remarks\n * This codec efficiently stores small numbers, making it useful for transactions and compact representations.\n *\n * If you need a fixed-size `u16` codec, consider using {@link getU16Codec}.\n *\n * Separate {@link getShortU16Encoder} and {@link getShortU16Decoder} functions are available.\n *\n * ```ts\n * const bytes = getShortU16Encoder().encode(42);\n * const value = getShortU16Decoder().decode(bytes);\n * ```\n *\n * @see {@link getShortU16Encoder}\n * @see {@link getShortU16Decoder}\n */\nexport const getShortU16Codec = (): VariableSizeCodec<bigint | number, number> =>\n    combineCodec(getShortU16Encoder(), getShortU16Decoder());\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 128-bit unsigned integers (`u128`).\n *\n * This encoder serializes `u128` values using sixteen bytes in little-endian format by default.\n * You may specify big-endian storage using the `endian` option.\n *\n * For more details, see {@link getU128Codec}.\n *\n * @param config - Optional settings for endianness.\n * @returns A `FixedSizeEncoder<number | bigint, 16>` for encoding `u128` values.\n *\n * @example\n * Encoding a `u128` value.\n * ```ts\n * const encoder = getU128Encoder();\n * const bytes = encoder.encode(42n); // 0x2a000000000000000000000000000000\n * ```\n *\n * @see {@link getU128Codec}\n */\nexport const getU128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'u128',\n        range: [0n, BigInt('0xffffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigUint64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\n/**\n * Returns a decoder for 128-bit unsigned integers (`u128`).\n *\n * This decoder deserializes `u128` values from sixteen bytes in little-endian format by default.\n * You may specify big-endian storage using the `endian` option.\n *\n * For more details, see {@link getU128Codec}.\n *\n * @param config - Optional settings for endianness.\n * @returns A `FixedSizeDecoder<bigint, 16>` for decoding `u128` values.\n *\n * @example\n * Decoding a `u128` value.\n * ```ts\n * const decoder = getU128Decoder();\n * const value = decoder.decode(new Uint8Array([0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00])); // 42n\n * ```\n *\n * @see {@link getU128Codec}\n */\nexport const getU128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigUint64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'u128',\n        size: 16,\n    });\n\n/**\n * Returns a codec for encoding and decoding 128-bit unsigned integers (`u128`).\n *\n * This codec serializes `u128` values using 16 bytes.\n * Values can be provided as either `number` or `bigint`, but the decoded value is always a `bigint`.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeCodec<number | bigint, bigint, 16>` for encoding and decoding `u128` values.\n *\n * @example\n * Encoding and decoding a `u128` value.\n * ```ts\n * const codec = getU128Codec();\n * const bytes = codec.encode(42); // 0x2a000000000000000000000000000000\n * const value = codec.decode(bytes); // 42n\n * ```\n *\n * @example\n * Using big-endian encoding.\n * ```ts\n * const codec = getU128Codec({ endian: Endian.Big });\n * const bytes = codec.encode(42); // 0x0000000000000000000000000000002a\n * ```\n *\n * @remarks\n * This codec supports values between `0` and `2^128 - 1`.\n * Since JavaScript `number` cannot safely represent values beyond `2^53 - 1`, the decoded value is always a `bigint`.\n *\n * - If you need a smaller unsigned integer, consider using {@link getU64Codec} or {@link getU32Codec}.\n * - If you need signed integers, consider using {@link getI128Codec}.\n *\n * Separate {@link getU128Encoder} and {@link getU128Decoder} functions are available.\n *\n * ```ts\n * const bytes = getU128Encoder().encode(42);\n * const value = getU128Decoder().decode(bytes);\n * ```\n *\n * @see {@link getU128Encoder}\n * @see {@link getU128Decoder}\n */\nexport const getU128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, bigint, 16> =>\n    combineCodec(getU128Encoder(config), getU128Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 16-bit unsigned integers (`u16`).\n *\n * This encoder serializes `u16` values using two bytes in little-endian format by default.\n * You may specify big-endian storage using the `endian` option.\n *\n * For more details, see {@link getU16Codec}.\n *\n * @param config - Optional settings for endianness.\n * @returns A `FixedSizeEncoder<number | bigint, 2>` for encoding `u16` values.\n *\n * @example\n * Encoding a `u16` value.\n * ```ts\n * const encoder = getU16Encoder();\n * const bytes = encoder.encode(42); // 0x2a00\n * ```\n *\n * @see {@link getU16Codec}\n */\nexport const getU16Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 2> =>\n    numberEncoderFactory({\n        config,\n        name: 'u16',\n        range: [0, Number('0xffff')],\n        set: (view, value, le) => view.setUint16(0, Number(value), le),\n        size: 2,\n    });\n\n/**\n * Returns a decoder for 16-bit unsigned integers (`u16`).\n *\n * This decoder deserializes `u16` values from two bytes in little-endian format by default.\n * You may specify big-endian storage using the `endian` option.\n *\n * For more details, see {@link getU16Codec}.\n *\n * @param config - Optional settings for endianness.\n * @returns A `FixedSizeDecoder<number, 2>` for decoding `u16` values.\n *\n * @example\n * Decoding a `u16` value.\n * ```ts\n * const decoder = getU16Decoder();\n * const value = decoder.decode(new Uint8Array([0x2a, 0x00])); // 42\n * ```\n *\n * @see {@link getU16Codec}\n */\nexport const getU16Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 2> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getUint16(0, le),\n        name: 'u16',\n        size: 2,\n    });\n\n/**\n * Returns a codec for encoding and decoding 16-bit unsigned integers (`u16`).\n *\n * This codec serializes `u16` values using two bytes in little-endian format by default.\n * You may specify big-endian storage using the `endian` option.\n *\n * @param config - Optional settings for endianness.\n * @returns A `FixedSizeCodec<number | bigint, number, 2>` for encoding and decoding `u16` values.\n *\n * @example\n * Encoding and decoding a `u16` value.\n * ```ts\n * const codec = getU16Codec();\n * const bytes = codec.encode(42); // 0x2a00 (little-endian)\n * const value = codec.decode(bytes); // 42\n * ```\n *\n * @example\n * Storing values in big-endian format.\n * ```ts\n * const codec = getU16Codec({ endian: Endian.Big });\n * const bytes = codec.encode(42); // 0x002a\n * ```\n *\n * @remarks\n * This codec supports values between `0` and `2^16 - 1`.\n * If you need a larger range, consider using {@link getU32Codec} or {@link getU64Codec}.\n * For signed integers, use {@link getI16Codec}.\n *\n * Separate {@link getU16Encoder} and {@link getU16Decoder} functions are available.\n *\n * ```ts\n * const bytes = getU16Encoder().encode(42);\n * const value = getU16Decoder().decode(bytes);\n * ```\n *\n * @see {@link getU16Encoder}\n * @see {@link getU16Decoder}\n */\nexport const getU16Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 2> =>\n    combineCodec(getU16Encoder(config), getU16Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 32-bit unsigned integers (`u32`).\n *\n * This encoder serializes `u32` values using four bytes in little-endian format by default.\n * You may specify big-endian storage using the `endian` option.\n *\n * For more details, see {@link getU32Codec}.\n *\n * @param config - Optional settings for endianness.\n * @returns A `FixedSizeEncoder<bigint | number, 4>` for encoding `u32` values.\n *\n * @example\n * Encoding a `u32` value.\n * ```ts\n * const encoder = getU32Encoder();\n * const bytes = encoder.encode(42); // 0x2a000000\n * ```\n *\n * @see {@link getU32Codec}\n */\nexport const getU32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'u32',\n        range: [0, Number('0xffffffff')],\n        set: (view, value, le) => view.setUint32(0, Number(value), le),\n        size: 4,\n    });\n\n/**\n * Returns a decoder for 32-bit unsigned integers (`u32`).\n *\n * This decoder deserializes `u32` values from four bytes in little-endian format by default.\n * You may specify big-endian storage using the `endian` option.\n *\n * For more details, see {@link getU32Codec}.\n *\n * @param config - Optional settings for endianness.\n * @returns A `FixedSizeDecoder<number, 4>` for decoding `u32` values.\n *\n * @example\n * Decoding a `u32` value.\n * ```ts\n * const decoder = getU32Decoder();\n * const value = decoder.decode(new Uint8Array([0x2a, 0x00, 0x00, 0x00])); // 42\n * ```\n *\n * @see {@link getU32Codec}\n */\nexport const getU32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getUint32(0, le),\n        name: 'u32',\n        size: 4,\n    });\n\n/**\n * Returns a codec for encoding and decoding 32-bit unsigned integers (`u32`).\n *\n * This codec serializes `u32` values using four bytes in little-endian format by default.\n * You may specify big-endian storage using the `endian` option.\n *\n * @param config - Optional settings for endianness.\n * @returns A `FixedSizeCodec<bigint | number, number, 4>` for encoding and decoding `u32` values.\n *\n * @example\n * Encoding and decoding a `u32` value.\n * ```ts\n * const codec = getU32Codec();\n * const bytes = codec.encode(42); // 0x2a000000 (little-endian)\n * const value = codec.decode(bytes); // 42\n * ```\n *\n * @example\n * Storing values in big-endian format.\n * ```ts\n * const codec = getU32Codec({ endian: Endian.Big });\n * const bytes = codec.encode(42); // 0x0000002a\n * ```\n *\n * @remarks\n * This codec only supports values between `0` and `2^32 - 1`.\n * If you need a larger range, consider using {@link getU64Codec} or {@link getU128Codec}.\n * For signed integers, use {@link getI32Codec}.\n *\n * Separate {@link getU32Encoder} and {@link getU32Decoder} functions are available.\n *\n * ```ts\n * const bytes = getU32Encoder().encode(42);\n * const value = getU32Decoder().decode(bytes);\n * ```\n *\n * @see {@link getU32Encoder}\n * @see {@link getU32Decoder}\n */\nexport const getU32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, number, 4> =>\n    combineCodec(getU32Encoder(config), getU32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 64-bit unsigned integers (`u64`).\n *\n * This encoder serializes `u64` values using 8 bytes.\n * Values can be provided as either `number` or `bigint`.\n *\n * For more details, see {@link getU64Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeEncoder<number | bigint, 8>` for encoding `u64` values.\n *\n * @example\n * Encoding a `u64` value.\n * ```ts\n * const encoder = getU64Encoder();\n * const bytes = encoder.encode(42); // 0x2a00000000000000\n * ```\n *\n * @see {@link getU64Codec}\n */\nexport const getU64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<bigint | number, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'u64',\n        range: [0n, BigInt('0xffffffffffffffff')],\n        set: (view, value, le) => view.setBigUint64(0, BigInt(value), le),\n        size: 8,\n    });\n\n/**\n * Returns a decoder for 64-bit unsigned integers (`u64`).\n *\n * This decoder deserializes `u64` values from 8 bytes.\n * The decoded value is always a `bigint`.\n *\n * For more details, see {@link getU64Codec}.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeDecoder<bigint, 8>` for decoding `u64` values.\n *\n * @example\n * Decoding a `u64` value.\n * ```ts\n * const decoder = getU64Decoder();\n * const value = decoder.decode(new Uint8Array([0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00])); // 42n\n * ```\n *\n * @see {@link getU64Codec}\n */\nexport const getU64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getBigUint64(0, le),\n        name: 'u64',\n        size: 8,\n    });\n\n/**\n * Returns a codec for encoding and decoding 64-bit unsigned integers (`u64`).\n *\n * This codec serializes `u64` values using 8 bytes.\n * Values can be provided as either `number` or `bigint`, but the decoded value is always a `bigint`.\n *\n * @param config - Optional configuration to specify endianness (little by default).\n * @returns A `FixedSizeCodec<number | bigint, bigint, 8>` for encoding and decoding `u64` values.\n *\n * @example\n * Encoding and decoding a `u64` value.\n * ```ts\n * const codec = getU64Codec();\n * const bytes = codec.encode(42); // 0x2a00000000000000\n * const value = codec.decode(bytes); // 42n\n * ```\n *\n * @example\n * Using big-endian encoding.\n * ```ts\n * const codec = getU64Codec({ endian: Endian.Big });\n * const bytes = codec.encode(42); // 0x000000000000002a\n * ```\n *\n * @remarks\n * This codec supports values between `0` and `2^64 - 1`.\n * Since JavaScript `number` cannot safely represent values beyond `2^53 - 1`, the decoded value is always a `bigint`.\n *\n * - If you need a smaller unsigned integer, consider using {@link getU32Codec} or {@link getU16Codec}.\n * - If you need a larger unsigned integer, consider using {@link getU128Codec}.\n * - If you need signed integers, consider using {@link getI64Codec}.\n *\n * Separate {@link getU64Encoder} and {@link getU64Decoder} functions are available.\n *\n * ```ts\n * const bytes = getU64Encoder().encode(42);\n * const value = getU64Decoder().decode(bytes);\n * ```\n *\n * @see {@link getU64Encoder}\n * @see {@link getU64Decoder}\n */\nexport const getU64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<bigint | number, bigint, 8> =>\n    combineCodec(getU64Encoder(config), getU64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\n/**\n * Returns an encoder for 8-bit unsigned integers (`u8`).\n *\n * This encoder serializes `u8` values using a single byte.\n *\n * For more details, see {@link getU8Codec}.\n *\n * @returns A `FixedSizeEncoder<number | bigint, 1>` for encoding `u8` values.\n *\n * @example\n * Encoding a `u8` value.\n * ```ts\n * const encoder = getU8Encoder();\n * const bytes = encoder.encode(42); // 0x2a\n * ```\n *\n * @see {@link getU8Codec}\n */\nexport const getU8Encoder = (): FixedSizeEncoder<bigint | number, 1> =>\n    numberEncoderFactory({\n        name: 'u8',\n        range: [0, Number('0xff')],\n        set: (view, value) => view.setUint8(0, Number(value)),\n        size: 1,\n    });\n\n/**\n * Returns a decoder for 8-bit unsigned integers (`u8`).\n *\n * This decoder deserializes `u8` values from a single byte.\n *\n * For more details, see {@link getU8Codec}.\n *\n * @returns A `FixedSizeDecoder<number, 1>` for decoding `u8` values.\n *\n * @example\n * Decoding a `u8` value.\n * ```ts\n * const decoder = getU8Decoder();\n * const value = decoder.decode(new Uint8Array([0xff])); // 255\n * ```\n *\n * @see {@link getU8Codec}\n */\nexport const getU8Decoder = (): FixedSizeDecoder<number, 1> =>\n    numberDecoderFactory({\n        get: view => view.getUint8(0),\n        name: 'u8',\n        size: 1,\n    });\n\n/**\n * Returns a codec for encoding and decoding 8-bit unsigned integers (`u8`).\n *\n * This codec serializes `u8` values using a single byte.\n *\n * @returns A `FixedSizeCodec<number | bigint, number, 1>` for encoding and decoding `u8` values.\n *\n * @example\n * Encoding and decoding a `u8` value.\n * ```ts\n * const codec = getU8Codec();\n * const bytes = codec.encode(255); // 0xff\n * const value = codec.decode(bytes); // 255\n * ```\n *\n * @remarks\n * This codec supports values between `0` and `2^8 - 1` (0 to 255).\n * If you need larger integers, consider using {@link getU16Codec}, {@link getU32Codec}, or {@link getU64Codec}.\n * For signed integers, use {@link getI8Codec}.\n *\n * Separate {@link getU8Encoder} and {@link getU8Decoder} functions are available.\n *\n * ```ts\n * const bytes = getU8Encoder().encode(42);\n * const value = getU8Decoder().decode(bytes);\n * ```\n *\n * @see {@link getU8Encoder}\n * @see {@link getU8Decoder}\n */\nexport const getU8Codec = (): FixedSizeCodec<bigint | number, number, 1> =>\n    combineCodec(getU8Encoder(), getU8Decoder());\n"]}