{"version": 3, "sources": ["../src/codes.ts", "../src/context.ts", "../src/messages.ts", "../src/message-formatter.ts", "../src/error.ts", "../src/stack-trace.ts", "../src/rpc-enum-errors.ts", "../src/instruction-error.ts", "../src/transaction-error.ts", "../src/json-rpc-error.ts"], "names": ["isSolana<PERSON>rror", "ORDERED_ERROR_NAMES"], "mappings": ";AA2BO,IAAM,mCAAsC,GAAA;AAC5C,IAAM,2BAA8B,GAAA;AACpC,IAAM,qCAAwC,GAAA;AAC9C,IAAM,kDAAqD,GAAA;AAC3D,IAAM,2CAA8C,GAAA;AACpD,IAAM,mCAAsC,GAAA;AAC5C,IAAM,qCAAwC,GAAA;AAC9C,IAAM,qCAAwC,GAAA;AAC9C,IAAM,oCAAuC,GAAA;AAC7C,IAAM,sCAAyC,GAAA;AAK/C,IAAM,mCAAsC,GAAA;AAC5C,IAAM,sCAAyC,GAAA;AAC/C,IAAM,sCAAyC,GAAA;AAC/C,IAAM,wCAA2C,GAAA;AACjD,IAAM,uCAA0C,GAAA;AAChD,IAAM,iEAAoE,GAAA;AAC1E,IAAM,oEAAuE,GAAA;AAC7E,IAAM,mEAAsE,GAAA;AAC5E,IAAM,uEAA0E,GAAA;AAChF,IAAM,kCAAqC,GAAA;AAC3C,IAAM,sEAAyE,GAAA;AAC/E,IAAM,sEAAyE,GAAA;AAC/E,IAAM,mEAAsE,GAAA;AAC5E,IAAM,gDAAmD,GAAA;AACzD,IAAM,iDAAoD,GAAA;AAC1D,IAAM,gFAAmF,GAAA;AACzF,IAAM,mDAAsD,GAAA;AAC5D,IAAM,wDAA2D,GAAA;AACjE,IAAM,+EAAkF,GAAA;AACxF,IAAM,uEAA0E,GAAA;AAChF,IAAM,qDAAwD,GAAA;AAI9D,IAAM,4CAA+C,GAAA;AACrD,IAAM,mDAAsD,GAAA;AAC5D,IAAM,uDAA0D,GAAA;AAChE,IAAM,mDAAsD,GAAA;AAC5D,IAAM,sCAAyC,GAAA;AAC/C,IAAM,mDAAsD,GAAA;AAC5D,IAAM,yDAA4D,GAAA;AAClE,IAAM,qDAAwD,GAAA;AAC9D,IAAM,qDAAwD,GAAA;AAC9D,IAAM,4DAA+D,GAAA;AACrE,IAAM,iDAAoD,GAAA;AAI1D,IAAM,yCAA4C,GAAA;AAClD,IAAM,sDAAyD,GAAA;AAC/D,IAAM,gDAAmD,GAAA;AACzD,IAAM,gDAAmD,GAAA;AACzD,IAAM,2DAA8D,GAAA;AAIpE,IAAM,2DAA8D,GAAA;AACpE,IAAM,iDAAoD,GAAA;AAC1D,IAAM,4DAA+D,GAAA;AACrE,IAAM,0DAA6D,GAAA;AACnE,IAAM,4DAA+D,GAAA;AACrE,IAAM,wDAA2D,GAAA;AACjE,IAAM,0DAA6D,GAAA;AACnE,IAAM,8DAAiE,GAAA;AAIvE,IAAM,0DAA6D,GAAA;AAInE,IAAM,gDAAmD,GAAA;AACzD,IAAM,mDAAsD,GAAA;AAC5D,IAAM,iDAAoD,GAAA;AAC1D,IAAM,wDAA2D,GAAA;AACjE,IAAM,qDAAwD,GAAA;AAI9D,IAAM,oDAAuD,GAAA;AAC7D,IAAM,gDAAmD,GAAA;AACzD,IAAM,8CAAiD,GAAA;AAKvD,IAAM,wCAA2C,GAAA;AACjD,IAAM,8CAAiD,GAAA;AACvD,IAAM,iDAAoD,GAAA;AAC1D,IAAM,yDAA4D,GAAA;AAClE,IAAM,qDAAwD,GAAA;AAC9D,IAAM,uDAA0D,GAAA;AAChE,IAAM,mDAAsD,GAAA;AAC5D,IAAM,qDAAwD,GAAA;AAC9D,IAAM,2DAA8D,GAAA;AACpE,IAAM,4DAA+D,GAAA;AACrE,IAAM,sDAAyD,GAAA;AAC/D,IAAM,uDAA0D,GAAA;AAChE,IAAM,oDAAuD,GAAA;AAC7D,IAAM,+DAAkE,GAAA;AACxE,IAAM,+DAAkE,GAAA;AACxE,IAAM,wDAA2D,GAAA;AACjE,IAAM,uDAA0D,GAAA;AAChE,IAAM,wDAA2D,GAAA;AACjE,IAAM,oDAAuD,GAAA;AAC7D,IAAM,oDAAuD,GAAA;AAC7D,IAAM,wDAA2D,GAAA;AACjE,IAAM,0DAA6D,GAAA;AACnE,IAAM,uDAA0D,GAAA;AAChE,IAAM,sDAAyD,GAAA;AAC/D,IAAM,2DAA8D,GAAA;AACpE,IAAM,8DAAiE,GAAA;AACvE,IAAM,uCAA0C,GAAA;AAChD,IAAM,8CAAiD,GAAA;AACvD,IAAM,yDAA4D,GAAA;AAClE,IAAM,0DAA6D,GAAA;AACnE,IAAM,mEAAsE,GAAA;AAC5E,IAAM,uDAA0D,GAAA;AAChE,IAAM,2CAA8C,GAAA;AACpD,IAAM,gDAAmD,GAAA;AACzD,IAAM,uDAA0D,GAAA;AAChE,IAAM,yDAA4D,GAAA;AAClE,IAAM,8CAAiD,GAAA;AACvD,IAAM,gDAAmD,GAAA;AACzD,IAAM,8DAAiE,GAAA;AACvE,IAAM,qDAAwD,GAAA;AAC9D,IAAM,kEAAqE,GAAA;AAC3E,IAAM,2DAA8D,GAAA;AACpE,IAAM,0DAA6D,GAAA;AACnE,IAAM,0CAA6C,GAAA;AACnD,IAAM,oDAAuD,GAAA;AAC7D,IAAM,+CAAkD,GAAA;AACxD,IAAM,wDAA2D,GAAA;AACjE,IAAM,sDAAyD,GAAA;AAC/D,IAAM,oDAAuD,GAAA;AAC7D,IAAM,mDAAsD,GAAA;AAC5D,IAAM,8CAAiD,GAAA;AACvD,IAAM,uEAA0E,GAAA;AAChF,IAAM,sDAAyD,GAAA;AAC/D,IAAM,sEAAyE,GAAA;AAC/E,IAAM,4EAA+E,GAAA;AAIrF,IAAM,0DAA6D,GAAA;AACnE,IAAM,8CAAiD,GAAA;AACvD,IAAM,6CAAgD,GAAA;AACtD,IAAM,uDAA0D,GAAA;AAChE,IAAM,qDAAwD,GAAA;AAC9D,IAAM,iDAAoD,GAAA;AAC1D,IAAM,2DAA8D,GAAA;AACpE,IAAM,yDAA4D,GAAA;AAClE,IAAM,yDAA4D,GAAA;AAClE,IAAM,sEAAyE,GAAA;AAC/E,IAAM,wDAA2D,GAAA;AACjE,IAAM,oDAAuD,GAAA;AAI7D,IAAM,2DAA8D,GAAA;AACpE,IAAM,gEAAmE,GAAA;AACzE,IAAM,sDAAyD,GAAA;AAC/D,IAAM,kDAAqD,GAAA;AAC3D,IAAM,sDAAyD,GAAA;AAC/D,IAAM,oFAAuF,GAAA;AAC7F,IAAM,sFAAyF,GAAA;AAC/F,IAAM,oFAAuF,GAAA;AAC7F,IAAM,gEAAmE,GAAA;AACzE,IAAM,6CAAgD,GAAA;AACtD,IAAM,0CAA6C,GAAA;AACnD,IAAM,4CAA+C,GAAA;AACrD,IAAM,sDAAyD,GAAA;AAC/D,IAAM,yEAA4E,GAAA;AAClF,IAAM,4FAA+F,GAAA;AACrG,IAAM,4DAA+D,GAAA;AACrE,IAAM,8DAAiE,GAAA;AACvE,IAAM,sDAAyD,GAAA;AAC/D,IAAM,2DAA8D,GAAA;AACpE,IAAM,2EAA8E,GAAA;AAKpF,IAAM,wCAA2C,GAAA;AACjD,IAAM,+CAAkD,GAAA;AACxD,IAAM,qDAAwD,GAAA;AAC9D,IAAM,kDAAqD,GAAA;AAC3D,IAAM,0DAA6D,GAAA;AACnE,IAAM,2DAA8D,GAAA;AACpE,IAAM,wDAA2D,GAAA;AACjE,IAAM,kDAAqD,GAAA;AAC3D,IAAM,oDAAuD,GAAA;AAE7D,IAAM,oDAAuD,GAAA;AAC7D,IAAM,0DAA6D,GAAA;AACnE,IAAM,sDAAyD,GAAA;AAC/D,IAAM,kDAAqD,GAAA;AAC3D,IAAM,8DAAiE,GAAA;AACvE,IAAM,iDAAoD,GAAA;AAC1D,IAAM,oDAAuD,GAAA;AAC7D,IAAM,2DAA8D,GAAA;AACpE,IAAM,kEAAqE,GAAA;AAC3E,IAAM,oDAAuD,GAAA;AAC7D,IAAM,yDAA4D,GAAA;AAClE,IAAM,oEAAuE,GAAA;AAC7E,IAAM,sEAAyE,GAAA;AAC/E,IAAM,uDAA0D,GAAA;AAChE,IAAM,+DAAkE,GAAA;AACxE,IAAM,mEAAsE,GAAA;AAC5E,IAAM,kEAAqE,GAAA;AAC3E,IAAM,mEAAsE,GAAA;AAC5E,IAAM,4DAA+D,GAAA;AACrE,IAAM,iEAAoE,GAAA;AAC1E,IAAM,sEAAyE,GAAA;AAC/E,IAAM,sDAAyD,GAAA;AAC/D,IAAM,4DAA+D,GAAA;AACrE,IAAM,uEAA0E,GAAA;AAChF,IAAM,wEAA2E,GAAA;AACjF,IAAM,sDAAyD,GAAA;AAC/D,IAAM,yEAA4E,GAAA;AAClF,IAAM,uDAA0D,GAAA;AAIhE,IAAM,oDAAuD,GAAA;AAC7D,IAAM,yCAA4C,GAAA;AAClD,IAAM,2CAA8C,GAAA;AACpD,IAAM,8CAAiD,GAAA;AACvD,IAAM,iEAAoE,GAAA;AAC1E,IAAM,yDAA4D,GAAA;AAClE,IAAM,uDAA0D,GAAA;AAChE,IAAM,6CAAgD,GAAA;AACtD,IAAM,qDAAwD,GAAA;AAC9D,IAAM,yDAA4D,GAAA;AAClE,IAAM,0CAA6C,GAAA;AACnD,IAAM,yCAA4C,GAAA;AAClD,IAAM,6CAAgD,GAAA;AACtD,IAAM,mDAAsD,GAAA;AAC5D,IAAM,yCAA4C,GAAA;AAClD,IAAM,mDAAsD,GAAA;AAC5D,IAAM,8DAAiE,GAAA;AACvE,IAAM,gDAAmD,GAAA;AACzD,IAAM,sCAAyC,GAAA;AAC/C,IAAM,kEAAqE,GAAA;AAC3E,IAAM,6DAAgE,GAAA;AACtE,IAAM,uDAA0D,GAAA;AAChE,IAAM,sEAAyE,GAAA;AAI/E,IAAM,mCAAsC,GAAA;AAC5C,IAAM,kDAAqD,GAAA;AAC3D,IAAM,uCAA0C,GAAA;AAChD,IAAM,kDAAqD,GAAA;AAI3D,IAAM,gEAAmE,GAAA;AACzE,IAAM,gEAAmE,GAAA;AACzE,IAAM,uEAA0E,GAAA;AAChF,IAAM,0DAA6D,GAAA;AACnE,IAAM,0DAA6D,GAAA;AAMnE,IAAM,sEAAyE,GAAA;AAC/E,IAAM,gHAAmH,GAAA;AACzH,IAAM,gFAAmF,GAAA;AACzF,IAAM,4DAA+D,GAAA;AACrE,IAAM,uEAA0E,GAAA;;;AC0TvF,SAAS,YAAY,KAAwB,EAAA;AACzC,EAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACtB,IAAA,MAAM,oBAAuB,GAAA,KAAA,CAAM,GAAI,CAAA,WAAW,CAAE,CAAA,IAAA;AAAA,MAAK;AAAA;AAAA,KAAmB;AAC5E,IAAA,OAAO,KAAkB,GAAA,oBAAA;AAAA,IAAiC,KAAA;AAAA,GAC9D,MAAA,IAAW,OAAO,KAAA,KAAU,QAAU,EAAA;AAClC,IAAA,OAAO,GAAG,KAAK,CAAA,CAAA,CAAA;AAAA,GACZ,MAAA;AACH,IAAO,OAAA,kBAAA;AAAA,MACH,MAAA;AAAA,QACI,KAAS,IAAA,IAAA,IAAQ,MAAO,CAAA,cAAA,CAAe,KAAK,CAAM,KAAA,IAAA;AAAA;AAAA;AAAA,UAG5C,EAAE,GAAI,KAAiB;AAAA,YACvB;AAAA;AACV,KACJ;AAAA;AAER;AAEA,SAAS,wBAAyB,CAAA,CAAC,GAAK,EAAA,KAAK,CAAiD,EAAA;AAC1F,EAAA,OAAO,CAAG,EAAA,GAAG,CAAI,CAAA,EAAA,WAAA,CAAY,KAAK,CAAC,CAAA,CAAA;AACvC;AAEO,SAAS,oBAAoB,OAAyB,EAAA;AACzD,EAAM,MAAA,kBAAA,GAAqB,OAAO,OAAQ,CAAA,OAAO,EAAE,GAAI,CAAA,wBAAwB,CAAE,CAAA,IAAA,CAAK,GAAG,CAAA;AACzF,EAAO,OAAa,MAAO,CAAA,IAAA,CAAK,kBAAoB,EAAA,MAAM,EAAE,QAAS,CAAA,QAAQ,CAAI,CAAuB;AAC5G;;;AClZO,IAAM,mBAIR,GAAA;AAAA,EACD,CAAC,yCAAyC,GAAG,wCAAA;AAAA,EAC7C,CAAC,2DAA2D,GACxD,iFAAA;AAAA,EACJ,CAAC,gDAAgD,GAAG,+CAAA;AAAA,EACpD,CAAC,gDAAgD,GAAG,oDAAA;AAAA,EACpD,CAAC,sDAAsD,GAAG,6CAAA;AAAA,EAC1D,CAAC,4DAA4D,GACzD,oDAAA;AAAA,EACJ,CAAC,uDAAuD,GAAG,mDAAA;AAAA,EAC3D,CAAC,4CAA4C,GACzC,uGAAA;AAAA,EACJ,CAAC,mDAAmD,GAAG,kDAAA;AAAA,EACvD,CAAC,qDAAqD,GAAG,uDAAA;AAAA,EACzD,CAAC,sCAAsC,GACnC,4GAAA;AAAA,EACJ,CAAC,yDAAyD,GACtD,qHAAA;AAAA,EACJ,CAAC,qDAAqD,GAClD,kGAAA;AAAA,EACJ,CAAC,mDAAmD,GAChD,gFAAA;AAAA,EACJ,CAAC,iDAAiD,GAAG,6CAAA;AAAA,EACrD,CAAC,mDAAmD,GAChD,uGAAA;AAAA,EACJ,CAAC,kDAAkD,GAC/C,wGAAA;AAAA,EACJ,CAAC,mCAAmC,GAChC,sGAAA;AAAA,EACJ,CAAC,oDAAoD,GACjD,4DAAA;AAAA,EACJ,CAAC,sEAAsE,GACnE,yJAAA;AAAA,EACJ,CAAC,6DAA6D,GAC1D,kFAAA;AAAA,EACJ,CAAC,yDAAyD,GACtD,qGAAA;AAAA,EACJ,CAAC,uDAAuD,GACpD,+FAAA;AAAA,EACJ,CAAC,iEAAiE,GAC9D,sEAAA;AAAA,EACJ,CAAC,qDAAqD,GAClD,4GAAA;AAAA,EACJ,CAAC,2CAA2C,GAAG,uDAAA;AAAA,EAC/C,CAAC,mDAAmD,GAChD,8EAAA;AAAA,EACJ,CAAC,8CAA8C,GAAG,uDAAA;AAAA,EAClD,CAAC,kEAAkE,GAC/D,4IAAA;AAAA,EACJ,CAAC,yCAAyC,GACtC,uEAAA;AAAA,EACJ,CAAC,sCAAsC,GACnC,mGAAA;AAAA,EACJ,CAAC,yDAAyD,GACtD,+EAAA;AAAA,EACJ,CAAC,0CAA0C,GACvC,iHAAA;AAAA,EACJ,CAAC,mDAAmD,GAChD,yEAAA;AAAA,EACJ,CAAC,6CAA6C,GAC1C,oEAAA;AAAA,EACJ,CAAC,6CAA6C,GAAG,8DAAA;AAAA,EACjD,CAAC,8DAA8D,GAC3D,kHAAA;AAAA,EACJ,CAAC,yCAAyC,GACtC,wFAAA;AAAA,EACJ,CAAC,yCAAyC,GACtC,8FAAA;AAAA,EACJ,CAAC,uDAAuD,GACpD,qFAAA;AAAA,EACJ,CAAC,gDAAgD,GAC7C,8FAAA;AAAA,EACJ,CAAC,0DAA0D,GAAG,iDAAA;AAAA,EAC9D,CAAC,4DAA4D,GAAG,+CAAA;AAAA,EAChE,CAAC,sDAAsD,GACnD,gFAAA;AAAA,EACJ,CAAC,2DAA2D,GACxD,iEAAA;AAAA,EACJ,CAAC,0DAA0D,GACvD,6EAAA;AAAA,EACJ,CAAC,uDAAuD,GAAG,wCAAA;AAAA,EAC3D,CAAC,uDAAuD,GAAG,4CAAA;AAAA,EAC3D,CAAC,wDAAwD,GACrD,4DAAA;AAAA,EACJ,CAAC,oDAAoD,GAAG,+BAAA;AAAA,EACxD,CAAC,+CAA+C,GAAG,+DAAA;AAAA,EACnD,CAAC,4EAA4E,GACzE,6CAAA;AAAA,EACJ,CAAC,2CAA2C,GAAG,8CAAA;AAAA,EAC/C,CAAC,8DAA8D,GAAG,+BAAA;AAAA,EAClE,CAAC,uCAAuC,GAAG,8BAAA;AAAA,EAC3C,CAAC,wDAAwD,GAAG,yCAAA;AAAA,EAC5D,CAAC,8DAA8D,GAC3D,6DAAA;AAAA,EACJ,CAAC,mEAAmE,GAAG,yCAAA;AAAA,EACvE,CAAC,yDAAyD,GAAG,8CAAA;AAAA,EAC7D,CAAC,0DAA0D,GACvD,0DAAA;AAAA,EACJ,CAAC,oDAAoD,GAAG,kDAAA;AAAA,EACxD,CAAC,+DAA+D,GAC5D,yDAAA;AAAA,EACJ,CAAC,+DAA+D,GAC5D,kEAAA;AAAA,EACJ,CAAC,8CAA8C,GAAG,2BAAA;AAAA,EAClD,CAAC,8CAA8C,GAAG,+BAAA;AAAA,EAClD,CAAC,0CAA0C,GAAG,sBAAA;AAAA,EAC9C,CAAC,oDAAoD,GAAG,8BAAA;AAAA,EACxD,CAAC,qDAAqD,GAAG,sCAAA;AAAA,EACzD,CAAC,mDAAmD,GAAG,oCAAA;AAAA,EACvD,CAAC,qDAAqD,GAAG,sCAAA;AAAA,EACzD,CAAC,sDAAsD,GAAG,uBAAA;AAAA,EAC1D,CAAC,iDAAiD,GAAG,0BAAA;AAAA,EACrD,CAAC,8CAA8C,GAAG,qCAAA;AAAA,EAClD,CAAC,yDAAyD,GAAG,0BAAA;AAAA,EAC7D,CAAC,gDAAgD,GAAG,mCAAA;AAAA,EACpD,CAAC,8CAA8C,GAAG,iDAAA;AAAA,EAClD,CAAC,uEAAuE,GACpE,wEAAA;AAAA,EACJ,CAAC,sDAAsD,GAAG,uBAAA;AAAA,EAC1D,CAAC,sEAAsE,GAAG,uCAAA;AAAA,EAC1E,CAAC,yDAAyD,GACtD,uDAAA;AAAA,EACJ,CAAC,gDAAgD,GAAG,mDAAA;AAAA,EACpD,CAAC,2DAA2D,GAAG,4CAAA;AAAA,EAC/D,CAAC,oDAAoD,GACjD,6DAAA;AAAA,EACJ,CAAC,wDAAwD,GAAG,2CAAA;AAAA,EAC5D,CAAC,qDAAqD,GAClD,uEAAA;AAAA,EACJ,CAAC,kEAAkE,GAC/D,gDAAA;AAAA,EACJ,CAAC,0DAA0D,GAAG,2BAAA;AAAA,EAC9D,CAAC,2DAA2D,GAAG,4BAAA;AAAA,EAC/D,CAAC,uDAAuD,GAAG,kDAAA;AAAA,EAC3D,CAAC,wDAAwD,GACrD,wDAAA;AAAA,EACJ,CAAC,uDAAuD,GACpD,sEAAA;AAAA,EACJ,CAAC,oDAAoD,GAAG,+CAAA;AAAA,EACxD,CAAC,uDAAuD,GACpD,mEAAA;AAAA,EACJ,CAAC,sDAAsD,GAAG,6CAAA;AAAA,EAC1D,CAAC,wCAAwC,GAAG,EAAA;AAAA,EAC5C,CAAC,uDAAuD,GAAG,wBAAA;AAAA,EAC3D,CAAC,mDAAmD,GAAG,oBAAA;AAAA,EACvD,CAAC,oDAAoD,GAAG,6CAAA;AAAA,EACxD,CAAC,gDAAgD,GAAG,yCAAA;AAAA,EACpD,CAAC,8CAA8C,GAC3C,mGAAA;AAAA,EACJ,CAAC,2CAA2C,GACxC,yGAAA;AAAA,EACJ,CAAC,2BAA2B,GACxB,4FAAA;AAAA,EACJ,CAAC,gFAAgF,GAC7E,oLAAA;AAAA,EAGJ,CAAC,uEAAuE,GACpE,mJAAA;AAAA,EAEJ,CAAC,gHAAgH,GAC7G,+NAAA;AAAA,EAGJ,CAAC,sEAAsE,GACnE,4KAAA;AAAA,EAEJ,CAAC,4DAA4D,GACzD,sMAAA;AAAA,EAGJ,CAAC,sCAAsC,GAAG,4DAAA;AAAA,EAC1C,CAAC,sCAAsC,GAAG,gEAAA;AAAA,EAC1C,CAAC,uCAAuC,GACpC,kFAAA;AAAA,EACJ,CAAC,wCAAwC,GACrC,iFAAA;AAAA,EACJ,CAAC,mCAAmC,GAChC,gGAAA;AAAA,EACJ,CAAC,kCAAkC,GAAG,kBAAA;AAAA,EACtC,CAAC,qDAAqD,GAAG,kBAAA;AAAA,EACzD,CAAC,wDAAwD,GAAG,kBAAA;AAAA,EAC5D,CAAC,mEAAmE,GAAG,kBAAA;AAAA,EACvE,CAAC,sEAAsE,GAAG,kBAAA;AAAA,EAC1E,CAAC,mEAAmE,GAAG,kBAAA;AAAA,EACvE,CAAC,iEAAiE,GAAG,2CAAA;AAAA,EACrE,CAAC,mDAAmD,GAAG,oDAAA;AAAA,EACvD,CAAC,gDAAgD,GAAG,aAAA;AAAA,EACpD,CAAC,uEAAuE,GAAG,+BAAA;AAAA,EAC3E,CAAC,iDAAiD,GAAG,kBAAA;AAAA,EACrD,CAAC,sEAAsE,GACnE,qDAAA;AAAA,EACJ,CAAC,gFAAgF,GAAG,kBAAA;AAAA,EACpF,CAAC,uEAAuE,GAAG,uCAAA;AAAA,EAC3E,CAAC,+EAA+E,GAC5E,4CAAA;AAAA,EACJ,CAAC,oEAAoE,GAAG,kBAAA;AAAA,EACxE,CAAC,gDAAgD,GAAG,uDAAA;AAAA,EACpD,CAAC,mDAAmD,GAChD,0EAAA;AAAA,EACJ,CAAC,iDAAiD,GAC9C,yGAAA;AAAA,EACJ,CAAC,qDAAqD,GAClD,kEAAA;AAAA,EACJ,CAAC,wDAAwD,GACrD,yGAAA;AAAA,EACJ,CAAC,mCAAmC,GAAG,iDAAA;AAAA,EACvC,CAAC,qCAAqC,GAAG,yCAAA;AAAA,EACzC,CAAC,sCAAsC,GAAG,UAAA;AAAA,EAC1C,CAAC,qCAAqC,GAAG,yCAAA;AAAA,EACzC,CAAC,qCAAqC,GAAG,mEAAA;AAAA,EACzC,CAAC,gEAAgE,GAC7D,0JAAA;AAAA,EAEJ,CAAC,uEAAuE,GACpE,uEAAA;AAAA,EACJ,CAAC,0DAA0D,GAAG,6BAAA;AAAA,EAC9D,CAAC,0DAA0D,GAAG,6BAAA;AAAA,EAC9D,CAAC,gEAAgE,GAC7D,oDAAA;AAAA,EACJ,CAAC,kDAAkD,GAAG,sDAAA;AAAA,EACtD,CAAC,mCAAmC,GAChC,kMAAA;AAAA,EAGJ,CAAC,uCAAuC,GAAG,oCAAA;AAAA,EAC3C,CAAC,kDAAkD,GAC/C,4HAAA;AAAA,EAEJ,CAAC,0DAA0D,GACvD,+IAAA;AAAA,EAEJ,CAAC,8CAA8C,GAC3C,qEAAA;AAAA,EACJ,CAAC,uDAAuD,GACpD,8EAAA;AAAA,EACJ,CAAC,qDAAqD,GAClD,4EAAA;AAAA,EACJ,CAAC,6CAA6C,GAC1C,6EAAA;AAAA,EACJ,CAAC,2DAA2D,GACxD,kFAAA;AAAA,EACJ,CAAC,yDAAyD,GACtD,gFAAA;AAAA,EACJ,CAAC,yDAAyD,GACtD,gFAAA;AAAA,EACJ,CAAC,iDAAiD,GAC9C,iFAAA;AAAA,EACJ,CAAC,sEAAsE,GACnE,0DAAA;AAAA,EACJ,CAAC,wDAAwD,GACrD,yHAAA;AAAA,EAEJ,CAAC,oDAAoD,GACjD,oGAAA;AAAA,EACJ,CAAC,8DAA8D,GAAG,sCAAA;AAAA,EAClE,CAAC,iDAAiD,GAAG,0CAAA;AAAA,EACrD,CAAC,2DAA2D,GACxD,8JAAA;AAAA,EAEJ,CAAC,4DAA4D,GACzD,4VAAA;AAAA,EAKJ,CAAC,0DAA0D,GACvD,0DAAA;AAAA,EACJ,CAAC,4DAA4D,GAAG,kDAAA;AAAA,EAChE,CAAC,wDAAwD,GAAG,2CAAA;AAAA,EAC5D,CAAC,0DAA0D,GAAG,8CAAA;AAAA,EAC9D,CAAC,oCAAoC,GACjC,sFAAA;AAAA,EACJ,CAAC,2DAA2D,GACxD,+EAAA;AAAA,EACJ,CAAC,+CAA+C,GAAG,gBAAA;AAAA,EACnD,CAAC,qDAAqD,GAAG,sBAAA;AAAA,EACzD,CAAC,kDAAkD,GAC/C,oEAAA;AAAA,EACJ,CAAC,+DAA+D,GAC5D,+DAAA;AAAA,EACJ,CAAC,kDAAkD,GAAG,6CAAA;AAAA,EACtD,CAAC,oDAAoD,GAAG,qBAAA;AAAA,EACxD,CAAC,oDAAoD,GAAG,+BAAA;AAAA,EACxD,CAAC,oDAAoD,GACjD,gEAAA;AAAA,EACJ,CAAC,sDAAsD,GACnD,2EAAA;AAAA,EACJ,CAAC,2DAA2D,GAAG,4BAAA;AAAA,EAC/D,CAAC,4DAA4D,GACzD,oFAAA;AAAA,EACJ,CAAC,wDAAwD,GAAG,sDAAA;AAAA,EAC5D,CAAC,sDAAsD,GAAG,mDAAA;AAAA,EAC1D,CAAC,kEAAkE,GAC/D,8DAAA;AAAA,EACJ,CAAC,mEAAmE,GAChE,wDAAA;AAAA,EACJ,CAAC,mEAAmE,GAChE,kEAAA;AAAA,EACJ,CAAC,wEAAwE,GACrE,yEAAA;AAAA,EACJ,CAAC,8DAA8D,GAC3D,yDAAA;AAAA,EACJ,CAAC,4DAA4D,GACzD,6EAAA;AAAA,EACJ,CAAC,yDAAyD,GACtD,6DAAA;AAAA,EACJ,CAAC,uEAAuE,GACpE,wDAAA;AAAA,EACJ,CAAC,0DAA0D,GACvD,yDAAA;AAAA,EACJ,CAAC,0DAA0D,GAAG,+CAAA;AAAA,EAC9D,CAAC,yEAAyE,GACtE,kGAAA;AAAA,EACJ,CAAC,sDAAsD,GAAG,sBAAA;AAAA,EAC1D,CAAC,iDAAiD,GAAG,2DAAA;AAAA,EACrD,CAAC,kDAAkD,GAAG,iDAAA;AAAA,EACtD,CAAC,uDAAuD,GAAG,sCAAA;AAAA,EAC3D,CAAC,uDAAuD,GACpD,mEAAA;AAAA,EACJ,CAAC,wCAAwC,GAAG,oDAAA;AAAA,EAC5C,CAAC,oDAAoD,GAAG,oCAAA;AAAA,EACxD,CAAC,sEAAsE,GACnE,8DAAA;AAAA,EACJ,CAAC,sEAAsE,GACnE,mDAAA;AAAA,EACJ,CAAC,oEAAoE,GACjE,6DAAA;AAAA,EACJ,CAAC,kEAAkE,GAC/D,+CAAA;AAAA,EACJ,CAAC,iEAAiE,GAAG,8CAAA;AAAA,EACrE,CAAC,4DAA4D,GACzD,6EAAA;AAAA,EACJ,CAAC,0CAA0C,GAAG,qDAAA;AAAA,EAC9C,CAAC,8DAA8D,GAC3D,oEAAA;AAAA,EACJ,CAAC,sDAAsD,GAAG,gDAAA;AAAA,EAC1D,CAAC,kDAAkD,GAAG,gDAAA;AAAA,EACtD,CAAC,oFAAoF,GACjF,wEAAA;AAAA,EACJ,CAAC,sFAAsF,GACnF,uNAAA;AAAA,EAGJ,CAAC,gEAAgE,GAAG,yCAAA;AAAA,EACpE,CAAC,oFAAoF,GACjF,gDAAA;AAAA,EACJ,CAAC,2DAA2D,GACxD,kMAAA;AAAA,EAGJ,CAAC,2EAA2E,GACxE,0UAAA;AAAA,EAIJ,CAAC,4CAA4C,GAAG,qCAAA;AAAA,EAChD,CAAC,sDAAsD,GACnD,oHAAA;AAAA,EAEJ,CAAC,4FAA4F,GACzF,yEAAA;AAAA,EACJ,CAAC,yEAAyE,GACtE,uEAAA;AAAA,EACJ,CAAC,2DAA2D,GACxD,6IAAA;AAAA,EAEJ,CAAC,gEAAgE,GAC7D,2IAAA;AAAA,EAEJ,CAAC,sDAAsD,GACnD,oHAAA;AAAA,EACJ,CAAC,6CAA6C,GAAG,8DAAA;AAAA,EACjD,CAAC,sDAAsD,GACnD;AACR,CAAA;;;AC9lBA,IAAM,WAAc,GAAA,GAAA;AACpB,IAAM,IAAO,GAAA,GAAA;AAEN,SAAS,4BACZ,CAAA,IAAA,EACA,OAAkB,GAAA,EACZ,EAAA;AACN,EAAM,MAAA,mBAAA,GAAsB,oBAAoB,IAAI,CAAA;AACpD,EAAI,IAAA,mBAAA,CAAoB,WAAW,CAAG,EAAA;AAClC,IAAO,OAAA,EAAA;AAAA;AAEX,EAAI,IAAA,KAAA;AACJ,EAAA,SAAS,gBAAgB,QAAmB,EAAA;AACxC,IAAI,IAAA,KAAA,CAAM,IAAI,CAAA,KAAM,CAAoB,iBAAA;AACpC,MAAA,MAAM,eAAe,mBAAoB,CAAA,KAAA,CAAM,MAAM,WAAW,CAAA,GAAI,GAAG,QAAQ,CAAA;AAE/E,MAAU,SAAA,CAAA,IAAA;AAAA,QACN,YAAgB,IAAA,OAAA;AAAA;AAAA,UAEV,CAAA,EAAG,OAAQ,CAAA,YAAoC,CAAC,CAAA;AAAA,YAChD,IAAI,YAAY,CAAA;AAAA,OAC1B;AAAA,KACO,MAAA,IAAA,KAAA,CAAM,IAAI,CAAA,KAAM,CAAgB,aAAA;AACvC,MAAA,SAAA,CAAU,KAAK,mBAAoB,CAAA,KAAA,CAAM,MAAM,WAAW,CAAA,EAAG,QAAQ,CAAC,CAAA;AAAA;AAC1E;AAEJ,EAAA,MAAM,YAAsB,EAAC;AAC7B,EAAA,mBAAA,CAAoB,MAAM,EAAE,CAAA,CAAE,OAAQ,CAAA,CAAC,MAAM,EAAO,KAAA;AAChD,IAAA,IAAI,OAAO,CAAG,EAAA;AACV,MAAQ,KAAA,GAAA;AAAA,QACJ,CAAC,WAAW,GAAG,CAAA;AAAA,QACf,CAAC,IAAI,GACD,mBAAA,CAAoB,CAAC,CAAA,KAAM,IACrB,GAAA,CAAA,wBACA,mBAAoB,CAAA,CAAC,CAAM,KAAA,GAAA,GACzB,CACA,kBAAA,CAAA;AAAA,OAChB;AACA,MAAA;AAAA;AAEJ,IAAI,IAAA,SAAA;AACJ,IAAQ,QAAA,KAAA,CAAM,IAAI,CAAG;AAAA,MACjB,KAAK,CAAA;AACD,QAAY,SAAA,GAAA,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,CAAe,aAAA;AACxD,QAAA;AAAA,MACJ,KAAK,CAAA;AACD,QAAA,IAAI,SAAS,IAAM,EAAA;AACf,UAAY,SAAA,GAAA,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,CAAyB,uBAAA;AAAA,SACtE,MAAA,IAAW,SAAS,GAAK,EAAA;AACrB,UAAY,SAAA,GAAA,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,CAAmB,iBAAA;AAAA;AAEhE,QAAA;AAAA,MACJ,KAAK,CAAA;AACD,QAAA,IAAI,SAAS,IAAM,EAAA;AACf,UAAY,SAAA,GAAA,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,CAAyB,uBAAA;AAAA,SACtE,MAAA,IAAW,SAAS,GAAK,EAAA;AACrB,UAAY,SAAA,GAAA,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,CAAmB,iBAAA;AAAA,SACrD,MAAA,IAAA,CAAC,IAAK,CAAA,KAAA,CAAM,IAAI,CAAG,EAAA;AAC1B,UAAY,SAAA,GAAA,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,CAAe,aAAA;AAAA;AAE5D,QAAA;AAAA;AAER,IAAA,IAAI,SAAW,EAAA;AACX,MAAA,IAAI,UAAU,SAAW,EAAA;AACrB,QAAA,eAAA,CAAgB,EAAE,CAAA;AAAA;AAEtB,MAAQ,KAAA,GAAA,SAAA;AAAA;AACZ,GACH,CAAA;AACD,EAAgB,eAAA,EAAA;AAChB,EAAO,OAAA,SAAA,CAAU,KAAK,EAAE,CAAA;AAC5B;AAEO,SAAS,eACZ,CAAA,IAAA,EACA,OAAmC,GAAA,EAC7B,EAAA;AACN,EAAA,IAAI,yBAAyB,YAAc,EAAA;AACvC,IAAO,OAAA,4BAAA,CAA6B,MAAM,OAAO,CAAA;AAAA,GAC9C,MAAA;AACH,IAAA,IAAI,qBAAwB,GAAA,CAAA,cAAA,EAAiB,IAAI,CAAA,8DAAA,EAAiE,IAAI,CAAA,CAAA;AACtH,IAAA,IAAI,MAAO,CAAA,IAAA,CAAK,OAAO,CAAA,CAAE,MAAQ,EAAA;AAM7B,MAAyB,qBAAA,IAAA,CAAA,EAAA,EAAK,mBAAoB,CAAA,OAAO,CAAC,CAAA,CAAA,CAAA;AAAA;AAE9D,IAAA,OAAO,GAAG,qBAAqB,CAAA,EAAA,CAAA;AAAA;AAEvC;;;AC1DO,SAAS,aAAA,CACZ,GAKA,IAC4B,EAAA;AAC5B,EAAA,MAAMA,cAAgB,GAAA,CAAA,YAAa,KAAS,IAAA,CAAA,CAAE,IAAS,KAAA,aAAA;AACvD,EAAA,IAAIA,cAAe,EAAA;AACf,IAAA,IAAI,SAAS,MAAW,EAAA;AACpB,MAAQ,OAAA,CAAA,CAA8B,QAAQ,MAAW,KAAA,IAAA;AAAA;AAE7D,IAAO,OAAA,IAAA;AAAA;AAEX,EAAO,OAAA,KAAA;AACX;AAYa,IAAA,WAAA,GAAN,cAAgF,KAAM,CAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhF,QAA8E,IAAK,CAAA,KAAA;AAAA;AAAA;AAAA;AAAA,EAInF,OAAA;AAAA,EACT,WACO,CAAA,GAAA,CAAC,IAAM,EAAA,sBAAsB,CAGlC,EAAA;AACE,IAAI,IAAA,OAAA;AACJ,IAAI,IAAA,YAAA;AACJ,IAAA,IAAI,sBAAwB,EAAA;AAExB,MAAA,MAAM,EAAE,KAAA,EAAO,GAAG,WAAA,EAAgB,GAAA,sBAAA;AAClC,MAAA,IAAI,KAAO,EAAA;AACP,QAAA,YAAA,GAAe,EAAE,KAAM,EAAA;AAAA;AAE3B,MAAA,IAAI,MAAO,CAAA,IAAA,CAAK,WAAW,CAAA,CAAE,SAAS,CAAG,EAAA;AACrC,QAAU,OAAA,GAAA,WAAA;AAAA;AACd;AAEJ,IAAM,MAAA,OAAA,GAAU,eAAgB,CAAA,IAAA,EAAM,OAAO,CAAA;AAC7C,IAAA,KAAA,CAAM,SAAS,YAAY,CAAA;AAC3B,IAAA,IAAA,CAAK,OAAU,GAAA;AAAA,MACX,MAAQ,EAAA,IAAA;AAAA,MACR,GAAG;AAAA,KACP;AAGA,IAAA,IAAA,CAAK,IAAO,GAAA,aAAA;AAAA;AAEpB;;;ACjHO,SAAS,yBAAyB,IAAwD,EAAA;AAC7F,EAAA,IAAI,mBAAuB,IAAA,KAAA,IAAS,OAAO,KAAA,CAAM,sBAAsB,UAAY,EAAA;AAC/E,IAAM,KAAA,CAAA,iBAAA,CAAkB,GAAG,IAAI,CAAA;AAAA;AAEvC;;;AC6BO,SAAS,2BACZ,EAAE,mBAAA,EAAqB,iBAAiB,iBAAmB,EAAA,YAAA,IAE3D,cACW,EAAA;AACX,EAAI,IAAA,YAAA;AACJ,EAAI,IAAA,eAAA;AACJ,EAAI,IAAA,OAAO,iBAAiB,QAAU,EAAA;AAClC,IAAe,YAAA,GAAA,YAAA;AAAA,GACZ,MAAA;AACH,IAAA,YAAA,GAAe,MAAO,CAAA,IAAA,CAAK,YAAY,CAAA,CAAE,CAAC,CAAA;AAC1C,IAAA,eAAA,GAAkB,aAAa,YAAY,CAAA;AAAA;AAE/C,EAAM,MAAA,UAAA,GAAa,iBAAkB,CAAA,OAAA,CAAQ,YAAY,CAAA;AACzD,EAAA,MAAM,YAAa,mBAAsB,GAAA,UAAA;AACzC,EAAA,MAAM,YAAe,GAAA,eAAA,CAAgB,SAAW,EAAA,YAAA,EAAc,eAAe,CAAA;AAC7E,EAAA,MAAM,GAAM,GAAA,IAAI,WAAY,CAAA,SAAA,EAAW,YAAY,CAAA;AACnD,EAAA,qBAAA,CAAsB,KAAK,cAAc,CAAA;AACzC,EAAO,OAAA,GAAA;AACX;;;AC5CA,IAAM,mBAAsB,GAAA;AAAA;AAAA;AAAA;AAAA,EAIxB,cAAA;AAAA,EACA,iBAAA;AAAA,EACA,wBAAA;AAAA,EACA,oBAAA;AAAA,EACA,qBAAA;AAAA,EACA,mBAAA;AAAA,EACA,oBAAA;AAAA,EACA,0BAAA;AAAA,EACA,2BAAA;AAAA,EACA,sBAAA;AAAA,EACA,uBAAA;AAAA,EACA,mBAAA;AAAA,EACA,6BAAA;AAAA,EACA,6BAAA;AAAA,EACA,uBAAA;AAAA,EACA,sBAAA;AAAA,EACA,uBAAA;AAAA,EACA,oBAAA;AAAA,EACA,mBAAA;AAAA,EACA,sBAAA;AAAA,EACA,wBAAA;AAAA,EACA,sBAAA;AAAA,EACA,qBAAA;AAAA,EACA,0BAAA;AAAA,EACA,2BAAA;AAAA,EACA,QAAA;AAAA,EACA,cAAA;AAAA,EACA,wBAAA;AAAA,EACA,yBAAA;AAAA,EACA,gCAAA;AAAA,EACA,sBAAA;AAAA,EACA,WAAA;AAAA,EACA,gBAAA;AAAA,EACA,sBAAA;AAAA,EACA,uBAAA;AAAA,EACA,cAAA;AAAA,EACA,gBAAA;AAAA,EACA,6BAAA;AAAA,EACA,qBAAA;AAAA,EACA,gCAAA;AAAA,EACA,yBAAA;AAAA,EACA,wBAAA;AAAA,EACA,WAAA;AAAA,EACA,oBAAA;AAAA,EACA,cAAA;AAAA,EACA,sBAAA;AAAA,EACA,qBAAA;AAAA,EACA,oBAAA;AAAA,EACA,mBAAA;AAAA,EACA,cAAA;AAAA,EACA,oCAAA;AAAA,EACA,qBAAA;AAAA,EACA,mCAAA;AAAA,EACA;AACJ,CAAA;AAEO,SAAS,kCAAA,CAIZ,OACA,gBACW,EAAA;AACX,EAAM,MAAA,WAAA,GAAc,OAAO,KAAK,CAAA;AAChC,EAAO,OAAA,0BAAA;AAAA,IACH;AAAA,MACI,mBAAqB,EAAA,OAAA;AAAA,MACrB,eAAA,CAAgB,SAAW,EAAA,YAAA,EAAc,eAAiB,EAAA;AACtD,QAAA,IAAI,cAAc,wCAA0C,EAAA;AACxD,UAAO,OAAA;AAAA,YACH,SAAW,EAAA,YAAA;AAAA,YACX,KAAO,EAAA,WAAA;AAAA,YACP,GAAI,eAAoB,KAAA,MAAA,GAAY,EAAE,uBAAA,EAAyB,iBAAoB,GAAA;AAAA,WACvF;AAAA,SACJ,MAAA,IAAW,cAAc,uCAAyC,EAAA;AAC9D,UAAO,OAAA;AAAA,YACH,IAAA,EAAM,OAAO,eAAkC,CAAA;AAAA,YAC/C,KAAO,EAAA;AAAA,WACX;AAAA,SACJ,MAAA,IAAW,cAAc,+CAAiD,EAAA;AACtE,UAAO,OAAA;AAAA,YACH,WAAa,EAAA,eAAA;AAAA,YACb,KAAO,EAAA;AAAA,WACX;AAAA;AAEJ,QAAO,OAAA,EAAE,OAAO,WAAY,EAAA;AAAA,OAChC;AAAA,MACA,iBAAmB,EAAA,mBAAA;AAAA,MACnB,YAAc,EAAA;AAAA,KAClB;AAAA,IACA;AAAA,GACJ;AACJ;;;ACvFA,IAAMC,oBAAsB,GAAA;AAAA;AAAA;AAAA;AAAA,EAIxB,cAAA;AAAA,EACA,oBAAA;AAAA,EACA,iBAAA;AAAA,EACA,wBAAA;AAAA,EACA,yBAAA;AAAA,EACA,sBAAA;AAAA,EACA,kBAAA;AAAA,EACA,mBAAA;AAAA;AAAA,EAEA,kBAAA;AAAA,EACA,wBAAA;AAAA,EACA,qBAAA;AAAA,EACA,kBAAA;AAAA,EACA,4BAAA;AAAA,EACA,iBAAA;AAAA,EACA,oBAAA;AAAA,EACA,0BAAA;AAAA,EACA,8BAAA;AAAA,EACA,oBAAA;AAAA,EACA,wBAAA;AAAA,EACA,gCAAA;AAAA,EACA,kCAAA;AAAA,EACA,qBAAA;AAAA,EACA,4BAAA;AAAA,EACA,gCAAA;AAAA,EACA,+BAAA;AAAA,EACA,gCAAA;AAAA,EACA,0BAAA;AAAA,EACA,6BAAA;AAAA,EACA,kCAAA;AAAA,EACA,sBAAA;AAAA,EACA,0BAAA;AAAA,EACA,mCAAA;AAAA,EACA,oCAAA;AAAA,EACA,sBAAA;AAAA,EACA,uCAAA;AAAA,EACA;AACJ,CAAA;AAEO,SAAS,mCAAmC,gBAAoE,EAAA;AACnH,EAAA,IAAI,OAAO,gBAAA,KAAqB,QAAY,IAAA,kBAAA,IAAsB,gBAAkB,EAAA;AAChF,IAAO,OAAA,kCAAA;AAAA,MACH,GAAI,gBAAiB,CAAA;AAAA,KACzB;AAAA;AAEJ,EAAO,OAAA,0BAAA;AAAA,IACH;AAAA,MACI,mBAAqB,EAAA,OAAA;AAAA,MACrB,eAAA,CAAgB,SAAW,EAAA,YAAA,EAAc,eAAiB,EAAA;AACtD,QAAA,IAAI,cAAc,wCAA0C,EAAA;AACxD,UAAO,OAAA;AAAA,YACH,SAAW,EAAA,YAAA;AAAA,YACX,GAAI,eAAoB,KAAA,MAAA,GAAY,EAAE,uBAAA,EAAyB,iBAAoB,GAAA;AAAA,WACvF;AAAA,SACJ,MAAA,IAAW,cAAc,sDAAwD,EAAA;AAC7E,UAAO,OAAA;AAAA,YACH,KAAA,EAAO,OAAO,eAAkC;AAAA,WACpD;AAAA,SAEA,MAAA,IAAA,SAAA,KAAc,4DACd,IAAA,SAAA,KAAc,yEAChB,EAAA;AACE,UAAO,OAAA;AAAA,YACH,YAAA,EAAc,MAAQ,CAAA,eAAA,CAAuD,aAAa;AAAA,WAC9F;AAAA;AACJ,OACJ;AAAA,MACA,iBAAmBA,EAAAA,oBAAAA;AAAA,MACnB,YAAc,EAAA;AAAA,KAClB;AAAA,IACA;AAAA,GACJ;AACJ;;;ACCO,SAAS,+BAA+B,qBAA6C,EAAA;AACxF,EAAI,IAAA,GAAA;AACJ,EAAI,IAAA,kBAAA,CAAmB,qBAAqB,CAAG,EAAA;AAC3C,IAAA,MAAM,EAAE,IAAA,EAAM,OAAS,EAAA,IAAA,EAAM,SAAY,GAAA,qBAAA;AACzC,IAAM,MAAA,IAAA,GAAO,OAAO,OAAO,CAAA;AAC3B,IAAA,IAAI,SAAS,uEAAyE,EAAA;AAClF,MAAA,MAAM,EAAE,GAAA,EAAK,GAAG,qBAAA,EAA0B,GAAA,IAAA;AAC1C,MAAA,MAAM,cAAc,GAAM,GAAA,EAAE,OAAO,kCAAmC,CAAA,GAAG,GAAM,GAAA,IAAA;AAC/E,MAAM,GAAA,GAAA,IAAI,YAAY,uEAAyE,EAAA;AAAA,QAC3F,GAAG,qBAAA;AAAA,QACH,GAAG;AAAA,OACN,CAAA;AAAA,KACE,MAAA;AACH,MAAI,IAAA,YAAA;AACJ,MAAA,QAAQ,IAAM;AAAA,QACV,KAAK,sCAAA;AAAA,QACL,KAAK,sCAAA;AAAA,QACL,KAAK,uCAAA;AAAA,QACL,KAAK,wCAAA;AAAA,QACL,KAAK,mCAAA;AAAA,QACL,KAAK,kCAAA;AAAA,QACL,KAAK,qDAAA;AAAA,QACL,KAAK,wDAAA;AAAA,QACL,KAAK,mEAAA;AAAA,QACL,KAAK,sEAAA;AAAA,QACL,KAAK,mEAAA;AAAA,QACL,KAAK,iDAAA;AAAA,QACL,KAAK,gFAAA;AAAA,QACL,KAAK,oEAAA;AAKD,UAAe,YAAA,GAAA,EAAE,iBAAiB,OAAQ,EAAA;AAC1C,UAAA;AAAA,QACJ;AACI,UAAA,IAAI,OAAO,IAAS,KAAA,QAAA,IAAY,CAAC,KAAM,CAAA,OAAA,CAAQ,IAAI,CAAG,EAAA;AAClD,YAAe,YAAA,GAAA,IAAA;AAAA;AACnB;AAER,MAAM,GAAA,GAAA,IAAI,WAAY,CAAA,IAAA,EAAyB,YAAmD,CAAA;AAAA;AACtG,GACG,MAAA;AACH,IAAA,MAAM,OACF,GAAA,OAAO,qBAA0B,KAAA,QAAA,IACjC,qBAA0B,KAAA,IAAA,IAC1B,SAAa,IAAA,qBAAA,IACb,OAAO,qBAAA,CAAsB,OAAY,KAAA,QAAA,GACnC,sBAAsB,OACtB,GAAA,oDAAA;AACV,IAAA,GAAA,GAAM,IAAI,WAAY,CAAA,sCAAA,EAAwC,EAAE,KAAO,EAAA,qBAAA,EAAuB,SAAS,CAAA;AAAA;AAE3G,EAAA,qBAAA,CAAsB,KAAK,8BAA8B,CAAA;AACzD,EAAO,OAAA,GAAA;AACX;AAEA,SAAS,mBAAmB,KAA2C,EAAA;AACnE,EAAA,OACI,OAAO,KAAU,KAAA,QAAA,IACjB,UAAU,IACV,IAAA,MAAA,IAAU,SACV,SAAa,IAAA,KAAA,KACZ,OAAO,KAAM,CAAA,IAAA,KAAS,YAAY,OAAO,KAAA,CAAM,SAAS,QACzD,CAAA,IAAA,OAAO,MAAM,OAAY,KAAA,QAAA;AAEjC", "file": "index.node.mjs", "sourcesContent": ["/**\n * To add a new error, follow the instructions at\n * https://github.com/anza-xyz/kit/tree/main/packages/errors/#adding-a-new-error\n *\n * @module\n * @privateRemarks\n * WARNING:\n *   - Don't remove error codes\n *   - Don't change or reorder error codes.\n *\n * Good naming conventions:\n *   - Prefixing common errors — e.g. under the same package — can be a good way to namespace them. E.g. All codec-related errors start with `SOLANA_ERROR__CODECS__`.\n *   - Use consistent names — e.g. choose `PDA` or `PROGRAM_DERIVED_ADDRESS` and stick with it. Ensure your names are consistent with existing error codes. The decision might have been made for you.\n *   - Recommended prefixes and suffixes:\n *     - `MALFORMED_`: Some input was not constructed properly. E.g. `MALFORMED_BASE58_ENCODED_ADDRESS`.\n *     - `INVALID_`: Some input is invalid (other than because it was MALFORMED). E.g. `INVALID_NUMBER_OF_BYTES`.\n *     - `EXPECTED_`: Some input was different than expected, no need to specify the \"GOT\" part unless necessary. E.g. `EXPECTED_DECODED_ACCOUNT`.\n *     - `_CANNOT_`: Some operation cannot be performed or some input cannot be used due to some condition. E.g. `CANNOT_DECODE_EMPTY_BYTE_ARRAY` or `PDA_CANNOT_END_WITH_PDA_MARKER`.\n *     - `_MUST_BE_`: Some condition must be true. E.g. `NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE`.\n *     - `_FAILED_TO_`: Tried to perform some operation and failed. E.g. `FAILED_TO_DECODE_ACCOUNT`.\n *     - `_NOT_FOUND`: Some operation lead to not finding something. E.g. `ACCOUNT_NOT_FOUND`.\n *     - `_OUT_OF_RANGE`: Some value is out of range. E.g. `ENUM_DISCRIMINATOR_OUT_OF_RANGE`.\n *     - `_EXCEEDED`: Some limit was exceeded. E.g. `PDA_MAX_SEED_LENGTH_EXCEEDED`.\n *     - `_MISMATCH`: Some elements do not match. E.g. `ENCODER_DECODER_FIXED_SIZE_MISMATCH`.\n *     - `_MISSING`: Some required input is missing. E.g. `TRANSACTION_FEE_PAYER_MISSING`.\n *     - `_UNIMPLEMENTED`: Some required component is not available in the environment. E.g. `SUBTLE_CRYPTO_VERIFY_FUNCTION_UNIMPLEMENTED`.\n */\nexport const SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED = 1;\nexport const SOLANA_ERROR__INVALID_NONCE = 2;\nexport const SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND = 3;\nexport const SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE = 4;\nexport const SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH = 5;\nexport const SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE = 6;\nexport const SOLANA_ERROR__MALFORMED_BIGINT_STRING = 7;\nexport const SOLANA_ERROR__MALFORMED_NUMBER_STRING = 8;\nexport const SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE = 9;\nexport const SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR = 10;\n\n// JSON-RPC-related errors.\n// Reserve error codes in the range [-32768, -32000]\n// Keep in sync with https://github.com/anza-xyz/agave/blob/master/rpc-client-api/src/custom_error.rs\nexport const SOLANA_ERROR__JSON_RPC__PARSE_ERROR = -32700;\nexport const SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR = -32603;\nexport const SOLANA_ERROR__JSON_RPC__INVALID_PARAMS = -32602;\nexport const SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND = -32601;\nexport const SOLANA_ERROR__JSON_RPC__INVALID_REQUEST = -32600;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED = -32016;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION = -32015;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET = -32014;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH = -32013;\nexport const SOLANA_ERROR__JSON_RPC__SCAN_ERROR = -32012;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE = -32011;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX = -32010;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED = -32009;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT = -32008;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED = -32007;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE = -32006;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY = -32005;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE = -32004;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE = -32003;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE = -32002;\nexport const SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP = -32001;\n\n// Addresses-related errors.\n// Reserve error codes in the range [2800000-2800999].\nexport const SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH = 2800000;\nexport const SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE = 2800001;\nexport const SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS = 2800002;\nexport const SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY = 2800003;\nexport const SOLANA_ERROR__ADDRESSES__MALFORMED_PDA = 2800004;\nexport const SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE = 2800005;\nexport const SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED = 2800006;\nexport const SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED = 2800007;\nexport const SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE = 2800008;\nexport const SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED = 2800009;\nexport const SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER = 2800010;\n\n// Account-related errors.\n// Reserve error codes in the range [3230000-3230999].\nexport const SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND = 3230000;\nexport const SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND = ********;\nexport const SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT = 3230002;\nexport const SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT = 3230003;\nexport const SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED = 3230004;\n\n// Subtle-Crypto-related errors.\n// Reserve error codes in the range [3610000-3610999].\nexport const SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT = 3610000;\nexport const SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED = 3610001;\nexport const SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED = 3610002;\nexport const SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED = 3610003;\nexport const SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED = 3610004;\nexport const SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED = 3610005;\nexport const SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED = 3610006;\nexport const SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY = 3610007;\n\n// Crypto-related errors.\n// Reserve error codes in the range [3611000-3611050].\nexport const SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED = 3611000;\n\n// Key-related errors.\n// Reserve error codes in the range [3704000-3704999].\nexport const SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH = 3704000;\nexport const SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH = 3704001;\nexport const SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH = 3704002;\nexport const SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE = 3704003;\nexport const SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY = 3704004;\n\n// Instruction-related errors.\n// Reserve error codes in the range [4128000-4128999].\nexport const SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS = 4128000;\nexport const SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA = 4128001;\nexport const SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH = 4128002;\n\n// Instruction errors.\n// Reserve error codes starting with [4615000-4615999] for the Rust enum `InstructionError`.\n// Error names here are dictated by the RPC (see ./instruction-error.ts).\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN = 4615000;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR = 4615001;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT = 4615002;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA = 4615003;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA = 4615004;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL = 4615005;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS = 4615006;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID = 4615007;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE = 4615008;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED = 4615009;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT = 4615010;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION = 4615011;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID = 4615012;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND = 4615013;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED = 4615014;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE = 4615015;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED = 4615016;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX = 4615017;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED = 4615018;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED = 4615019;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS = 4615020;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED = 4615021;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE = 4615022;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED = 4615023;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING = 4615024;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC = 4615025;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM = 4615026;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR = 4615027;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED = 4615028;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE = 4615029;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT = 4615030;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID = 4615031;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH = 4615032;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT = 4615033;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED = 4615034;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED = 4615035;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS = 4615036;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC = 4615037;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED = 4615038;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION = 4615039;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE = 4615040;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE = 4615041;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE = 4615042;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE = 4615043;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY = 4615044;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR = 4615045;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT = 4615046;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER = 4615047;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW = 4615048;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR = 4615049;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER = 4615050;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED = 4615051;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED = 4615052;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED = 4615053;\nexport const SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS = 4615054;\n\n// Signer-related errors.\n// Reserve error codes in the range [5508000-5508999].\nexport const SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS = 5508000;\nexport const SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER = 5508001;\nexport const SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER = 5508002;\nexport const SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER = 5508003;\nexport const SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER = 5508004;\nexport const SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER = 5508005;\nexport const SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER = 5508006;\nexport const SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER = 5508007;\nexport const SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER = 5508008;\nexport const SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS = 5508009;\nexport const SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING = 5508010;\nexport const SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED = 5508011;\n\n// Transaction-related errors.\n// Reserve error codes in the range [5663000-5663999].\nexport const SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES = 5663000;\nexport const SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE = 5663001;\nexport const SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME = 5663002;\nexport const SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME = 5663003;\nexport const SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE = 5663004;\nexport const SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING = 5663005;\nexport const SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE = 5663006;\nexport const SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND = 5663007;\nexport const SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING = 5663008;\nexport const SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING = 5663009;\nexport const SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING = 5663010;\nexport const SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING = 5663011;\nexport const SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING = 5663012;\nexport const SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING = 5663013;\nexport const SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE = 5663014;\nexport const SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION = 5663015;\nexport const SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES = 5663016;\nexport const SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH = 5663017;\nexport const SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT = 5663018;\nexport const SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT = 5663019;\n\n// Transaction errors.\n// Reserve error codes starting with [7050000-7050999] for the Rust enum `TransactionError`.\n// Error names here are dictated by the RPC (see ./transaction-error.ts).\nexport const SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN = 7050000;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE = 7050001;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE = 7050002;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND = 7050003;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND = 7050004;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE = 7050005;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE = 7050006;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED = 7050007;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND = 7050008;\n// `InstructionError` intentionally omitted.\nexport const SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP = 7050009;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE = 7050010;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX = 7050011;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE = 7050012;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION = 7050013;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE = 7050014;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE = 7050015;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING = 7050016;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT = 7050017;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION = 7050018;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT = 7050019;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT = 7050020;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT = 7050021;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS = 7050022;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND = 7050023;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER = 7050024;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA = 7050025;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX = 7050026;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT = 7050027;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT = 7050028;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT = 7050029;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION = 7050030;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT = 7050031;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED = 7050032;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT = 7050033;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED = 7050034;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED = 7050035;\nexport const SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION = 7050036;\n\n// Codec-related errors.\n// Reserve error codes in the range [8078000-8078999].\nexport const SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY = 8078000;\nexport const SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH = 8078001;\nexport const SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH = 8078002;\nexport const SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH = 8078003;\nexport const SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH = 8078004;\nexport const SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH = 8078005;\nexport const SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH = 8078006;\nexport const SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS = 8078007;\nexport const SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE = 8078008;\nexport const SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT = 8078009;\nexport const SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT = 8078010;\nexport const SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE = 8078011;\nexport const SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE = 8078012;\nexport const SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH = 8078013;\nexport const SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE = 8078014;\nexport const SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT = 8078015;\nexport const SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE = 8078016;\nexport const SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE = 8078017;\nexport const SOLANA_ERROR__CODECS__INVALID_CONSTANT = 8078018;\nexport const SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE = 8078019;\nexport const SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL = 8078020;\nexport const SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES = 8078021;\nexport const SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS = 8078022;\n\n// RPC-related errors.\n// Reserve error codes in the range [8100000-8100999].\nexport const SOLANA_ERROR__RPC__INTEGER_OVERFLOW = 8100000;\nexport const SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN = 8100001;\nexport const SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR = 8100002;\nexport const SOLANA_ERROR__RPC__API_PLAN_MISSING_FOR_RPC_METHOD = 8100003;\n\n// RPC-Subscriptions-related errors.\n// Reserve error codes in the range [8190000-8190999].\nexport const SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_PLAN = 8190000;\nexport const SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID = 8190001;\nexport const SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CLOSED_BEFORE_MESSAGE_BUFFERED = 8190002;\nexport const SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CONNECTION_CLOSED = 8190003;\nexport const SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_FAILED_TO_CONNECT = 8190004;\n\n// Invariant violation errors.\n// Reserve error codes in the range [9900000-9900999].\n// These errors should only be thrown when there is a bug with the\n// library itself and should, in theory, never reach the end user.\nexport const SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_STATE_MISSING = 9900000;\nexport const SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE = 9900001;\nexport const SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING = 9900002;\nexport const SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE = 9900003;\nexport const SOLANA_ERROR__INVARIANT_VIOLATION__DATA_PUBLISHER_CHANNEL_UNIMPLEMENTED = 9900004;\n\n/**\n * A union of every Solana error code\n *\n * @privateRemarks\n * You might be wondering why this is not a TypeScript enum or const enum.\n *\n * One of the goals of this library is to enable people to use some or none of it without having to\n * bundle all of it.\n *\n * If we made the set of error codes an enum then anyone who imported it (even if to only use a\n * single error code) would be forced to bundle every code and its label.\n *\n * Const enums appear to solve this problem by letting the compiler inline only the codes that are\n * actually used. Unfortunately exporting ambient (const) enums from a library like `@solana/errors`\n * is not safe, for a variety of reasons covered here: https://stackoverflow.com/a/********\n */\nexport type SolanaErrorCode =\n    | typeof SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND\n    | typeof SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED\n    | typeof SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT\n    | typeof SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT\n    | typeof SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND\n    | typeof SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED\n    | typeof SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS\n    | typeof SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH\n    | typeof SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY\n    | typeof SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE\n    | typeof SOLANA_ERROR__ADDRESSES__MALFORMED_PDA\n    | typeof SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED\n    | typeof SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED\n    | typeof SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER\n    | typeof SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED\n    | typeof SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY\n    | typeof SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS\n    | typeof SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL\n    | typeof SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH\n    | typeof SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH\n    | typeof SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH\n    | typeof SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH\n    | typeof SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH\n    | typeof SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH\n    | typeof SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE\n    | typeof SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH\n    | typeof SOLANA_ERROR__CODECS__INVALID_CONSTANT\n    | typeof SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT\n    | typeof SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT\n    | typeof SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT\n    | typeof SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS\n    | typeof SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE\n    | typeof SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES\n    | typeof SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS\n    | typeof SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA\n    | typeof SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID\n    | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR\n    | typeof SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH\n    | typeof SOLANA_ERROR__INVALID_NONCE\n    | typeof SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING\n    | typeof SOLANA_ERROR__INVARIANT_VIOLATION__DATA_PUBLISHER_CHANNEL_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE\n    | typeof SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_STATE_MISSING\n    | typeof SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE\n    | typeof SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR\n    | typeof SOLANA_ERROR__JSON_RPC__INVALID_PARAMS\n    | typeof SOLANA_ERROR__JSON_RPC__INVALID_REQUEST\n    | typeof SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND\n    | typeof SOLANA_ERROR__JSON_RPC__PARSE_ERROR\n    | typeof SOLANA_ERROR__JSON_RPC__SCAN_ERROR\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE\n    | typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION\n    | typeof SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH\n    | typeof SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH\n    | typeof SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH\n    | typeof SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY\n    | typeof SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__MALFORMED_BIGINT_STRING\n    | typeof SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR\n    | typeof SOLANA_ERROR__MALFORMED_NUMBER_STRING\n    | typeof SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND\n    | typeof SOLANA_ERROR__RPC__API_PLAN_MISSING_FOR_RPC_METHOD\n    | typeof SOLANA_ERROR__RPC__INTEGER_OVERFLOW\n    | typeof SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR\n    | typeof SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN\n    | typeof SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_PLAN\n    | typeof SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CLOSED_BEFORE_MESSAGE_BUFFERED\n    | typeof SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CONNECTION_CLOSED\n    | typeof SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_FAILED_TO_CONNECT\n    | typeof SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID\n    | typeof SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS\n    | typeof SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER\n    | typeof SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER\n    | typeof SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER\n    | typeof SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER\n    | typeof SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER\n    | typeof SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER\n    | typeof SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER\n    | typeof SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER\n    | typeof SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS\n    | typeof SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING\n    | typeof SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY\n    | typeof SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT\n    | typeof SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED\n    | typeof SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING\n    | typeof SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION\n    | typeof SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES\n    | typeof SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME\n    | typeof SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME\n    | typeof SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING\n    | typeof SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING\n    | typeof SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND\n    | typeof SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT\n    | typeof SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT\n    | typeof SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING\n    | typeof SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING\n    | typeof SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE\n    | typeof SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING\n    | typeof SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES\n    | typeof SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE\n    | typeof SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH\n    | typeof SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING\n    | typeof SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT\n    | typeof SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT;\n\n/**\n * Errors of this type are understood to have an optional {@link SolanaError} nested inside as\n * `cause`.\n */\nexport type SolanaErrorCodeWithCause = typeof SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE;\n", "/**\n * To add a new error, follow the instructions at\n * https://github.com/anza-xyz/kit/tree/main/packages/errors/#adding-a-new-error\n *\n * @privateRemarks\n * WARNING:\n *   - Don't change or remove members of an error's context.\n */\nimport {\n    SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND,\n    SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED,\n    SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT,\n    SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT,\n    SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND,\n    SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS,\n    SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH,\n    SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED,\n    SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED,\n    SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE,\n    SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE,\n    SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED,\n    SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY,\n    SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS,\n    SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH,\n    SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH,\n    SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE,\n    SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH,\n    SOLANA_ERROR__CODECS__INVALID_CONSTANT,\n    SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT,\n    SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT,\n    SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT,\n    SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS,\n    SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE,\n    SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES,\n    SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE,\n    SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS,\n    SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA,\n    SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW,\n    SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR,\n    SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS,\n    SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH,\n    SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM,\n    SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX,\n    SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND,\n    SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER,\n    SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID,\n    SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS,\n    SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION,\n    SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR,\n    SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH,\n    SOLANA_ERROR__INVALID_NONCE,\n    SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING,\n    SOLANA_ERROR__INVARIANT_VIOLATION__DATA_PUBLISHER_CHANNEL_UNIMPLEMENTED,\n    SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE,\n    SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR,\n    SOLANA_ERROR__JSON_RPC__INVALID_PARAMS,\n    SOLANA_ERROR__JSON_RPC__INVALID_REQUEST,\n    SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND,\n    SOLANA_ERROR__JSON_RPC__PARSE_ERROR,\n    SOLANA_ERROR__JSON_RPC__SCAN_ERROR,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION,\n    SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH,\n    SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH,\n    SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH,\n    SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE,\n    SOLANA_ERROR__MALFORMED_BIGINT_STRING,\n    SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR,\n    SOLANA_ERROR__MALFORMED_NUMBER_STRING,\n    SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND,\n    SOLANA_ERROR__RPC__API_PLAN_MISSING_FOR_RPC_METHOD,\n    SOLANA_ERROR__RPC__INTEGER_OVERFLOW,\n    SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR,\n    SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN,\n    SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_PLAN,\n    SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_FAILED_TO_CONNECT,\n    SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS,\n    SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER,\n    SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY,\n    SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE,\n    SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING,\n    SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION,\n    SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING,\n    SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE,\n    SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND,\n    SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT,\n    SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES,\n    SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE,\n    SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH,\n    SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING,\n    SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE,\n    SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION,\n    SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT,\n    SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED,\n    SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN,\n    SolanaErrorCode,\n} from './codes';\nimport { RpcSimulateTransactionResult } from './json-rpc-error';\n\ntype BasicInstructionErrorContext<T extends SolanaErrorCode> = Readonly<{ [P in T]: { index: number } }>;\n\ntype DefaultUnspecifiedErrorContextToUndefined<T> = {\n    [P in SolanaErrorCode]: P extends keyof T ? T[P] : undefined;\n};\n\ntype TypedArrayMutableProperties = 'copyWithin' | 'fill' | 'reverse' | 'set' | 'sort';\ninterface ReadonlyUint8Array extends Omit<Uint8Array, TypedArrayMutableProperties> {\n    readonly [n: number]: number;\n}\n\n/**\n * A map of every {@link SolanaError} code to the type of its `context` property.\n */\nexport type SolanaErrorContext = DefaultUnspecifiedErrorContextToUndefined<\n    BasicInstructionErrorContext<\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID\n        | typeof SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR\n    > & {\n        [SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND]: {\n            address: string;\n        };\n        [SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED]: {\n            addresses: string[];\n        };\n        [SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT]: {\n            address: string;\n        };\n        [SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT]: {\n            address: string;\n        };\n        [SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND]: {\n            addresses: string[];\n        };\n        [SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS]: {\n            putativeAddress: string;\n        };\n        [SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH]: {\n            actualLength: number;\n        };\n        [SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED]: {\n            actual: number;\n            maxSeeds: number;\n        };\n        [SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED]: {\n            actual: number;\n            index: number;\n            maxSeedLength: number;\n        };\n        [SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE]: {\n            bump: number;\n        };\n        [SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE]: {\n            actualLength: number;\n        };\n        [SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE]: {\n            actualLength: number;\n        };\n        [SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED]: {\n            currentBlockHeight: bigint;\n            lastValidBlockHeight: bigint;\n        };\n        [SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY]: {\n            codecDescription: string;\n        };\n        [SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS]: {\n            stringValues: string[];\n        };\n        [SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL]: {\n            encodedBytes: ReadonlyUint8Array;\n            hexEncodedBytes: string;\n            hexSentinel: string;\n            sentinel: ReadonlyUint8Array;\n        };\n        [SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH]: {\n            decoderFixedSize: number;\n            encoderFixedSize: number;\n        };\n        [SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH]: {\n            decoderMaxSize: number | undefined;\n            encoderMaxSize: number | undefined;\n        };\n        [SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE]: {\n            discriminator: bigint | number;\n            formattedValidDiscriminators: string;\n            validDiscriminators: number[];\n        };\n        [SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH]: {\n            bytesLength: number;\n            codecDescription: string;\n        };\n        [SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE]: {\n            codecDescription: string;\n            expectedSize: number;\n            hexZeroValue: string;\n            zeroValue: ReadonlyUint8Array;\n        };\n        [SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH]: {\n            bytesLength: number;\n            codecDescription: string;\n            expected: number;\n        };\n        [SOLANA_ERROR__CODECS__INVALID_CONSTANT]: {\n            constant: ReadonlyUint8Array;\n            data: ReadonlyUint8Array;\n            hexConstant: string;\n            hexData: string;\n            offset: number;\n        };\n        [SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT]: {\n            value: bigint | boolean | number | string | null | undefined;\n            variants: readonly (bigint | boolean | number | string | null | undefined)[];\n        };\n        [SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT]: {\n            formattedNumericalValues: string;\n            numericalValues: number[];\n            stringValues: string[];\n            variant: number | string | symbol;\n        };\n        [SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT]: {\n            value: bigint | boolean | number | string | null | undefined;\n            variants: readonly (bigint | boolean | number | string | null | undefined)[];\n        };\n        [SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS]: {\n            actual: bigint | number;\n            codecDescription: string;\n            expected: bigint | number;\n        };\n        [SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE]: {\n            alphabet: string;\n            base: number;\n            value: string;\n        };\n        [SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE]: {\n            discriminator: bigint | number;\n            maxRange: number;\n            minRange: number;\n        };\n        [SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE]: {\n            codecDescription: string;\n            max: bigint | number;\n            min: bigint | number;\n            value: bigint | number;\n        };\n        [SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE]: {\n            bytesLength: number;\n            codecDescription: string;\n            offset: number;\n        };\n        [SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES]: {\n            decodedBytes: ReadonlyUint8Array;\n            hexDecodedBytes: string;\n            hexSentinel: string;\n            sentinel: ReadonlyUint8Array;\n        };\n        [SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE]: {\n            maxRange: number;\n            minRange: number;\n            variant: number;\n        };\n        [SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR]: {\n            encodedData: string;\n            index: number;\n        };\n        [SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM]: {\n            code: number;\n            index: number;\n        };\n        [SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN]: {\n            errorName: string;\n            index: number;\n            instructionErrorContext?: unknown;\n        };\n        [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS]: {\n            data?: ReadonlyUint8Array;\n            programAddress: string;\n        };\n        [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA]: {\n            accountAddresses?: string[];\n            programAddress: string;\n        };\n        [SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH]: {\n            actualProgramAddress: string;\n            expectedProgramAddress: string;\n        };\n        [SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH]: {\n            actualLength: number;\n        };\n        [SOLANA_ERROR__INVALID_NONCE]: {\n            actualNonceValue: string;\n            expectedNonceValue: string;\n        };\n        [SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING]: {\n            cacheKey: string;\n        };\n        [SOLANA_ERROR__INVARIANT_VIOLATION__DATA_PUBLISHER_CHANNEL_UNIMPLEMENTED]: {\n            channelName: string;\n            supportedChannelNames: string[];\n        };\n        [SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE]: {\n            unexpectedValue: unknown;\n        };\n        [SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__INVALID_PARAMS]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__INVALID_REQUEST]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__PARSE_ERROR]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SCAN_ERROR]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED]: {\n            contextSlot: bigint;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY]: {\n            numSlotsBehind?: number;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE]: Omit<\n            RpcSimulateTransactionResult,\n            'err'\n        >;\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION]: {\n            __serverMessage: string;\n        };\n        [SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH]: {\n            byteLength: number;\n        };\n        [SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH]: {\n            actualLength: number;\n        };\n        [SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH]: {\n            actualLength: number;\n        };\n        [SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE]: {\n            actualLength: number;\n        };\n        [SOLANA_ERROR__MALFORMED_BIGINT_STRING]: {\n            value: string;\n        };\n        [SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR]: {\n            error: unknown;\n            message: string;\n        };\n        [SOLANA_ERROR__MALFORMED_NUMBER_STRING]: {\n            value: string;\n        };\n        [SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND]: {\n            nonceAccountAddress: string;\n        };\n        [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_PLAN]: {\n            notificationName: string;\n        };\n        [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_FAILED_TO_CONNECT]: {\n            errorEvent: Event;\n        };\n        [SOLANA_ERROR__RPC__API_PLAN_MISSING_FOR_RPC_METHOD]: {\n            method: string;\n            params: unknown[];\n        };\n        [SOLANA_ERROR__RPC__INTEGER_OVERFLOW]: {\n            argumentLabel: string;\n            keyPath: readonly (number | string | symbol)[];\n            methodName: string;\n            optionalPathLabel: string;\n            path?: string;\n            value: bigint;\n        };\n        [SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR]: {\n            headers: Headers;\n            message: string;\n            statusCode: number;\n        };\n        [SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN]: {\n            headers: string[];\n        };\n        [SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER]: {\n            address: string;\n        };\n        [SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY]: {\n            key: CryptoKey;\n        };\n        [SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE]: {\n            value: bigint;\n        };\n        [SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION]: {\n            index: number;\n        };\n        [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT]: {\n            accountIndex: number;\n        };\n        [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED]: {\n            accountIndex: number;\n        };\n        [SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN]: {\n            errorName: string;\n            transactionErrorContext?: unknown;\n        };\n        [SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION]: {\n            expectedAddresses: string[];\n            unexpectedAddresses: string[];\n        };\n        [SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING]: {\n            index: number;\n        };\n        [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING]: {\n            lookupTableAddresses: string[];\n        };\n        [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE]: {\n            highestKnownIndex: number;\n            highestRequestedIndex: number;\n            lookupTableAddress: string;\n        };\n        [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND]: {\n            index: number;\n        };\n        [SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT]: {\n            unitsConsumed: number;\n        };\n        [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES]: {\n            programAddress: string;\n        };\n        [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE]: {\n            programAddress: string;\n        };\n        [SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH]: {\n            numRequiredSignatures: number;\n            signaturesLength: number;\n            signerAddresses: string[];\n        };\n        [SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING]: {\n            addresses: string[];\n        };\n        [SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE]: {\n            actualVersion: number;\n        };\n    }\n>;\n\nexport function decodeEncodedContext(encodedContext: string): object {\n    const decodedUrlString = __NODEJS__ ? Buffer.from(encodedContext, 'base64').toString('utf8') : atob(encodedContext);\n    return Object.fromEntries(new URLSearchParams(decodedUrlString).entries());\n}\n\nfunction encodeValue(value: unknown): string {\n    if (Array.isArray(value)) {\n        const commaSeparatedValues = value.map(encodeValue).join('%2C%20' /* \", \" */);\n        return '%5B' /* \"[\" */ + commaSeparatedValues + /* \"]\" */ '%5D';\n    } else if (typeof value === 'bigint') {\n        return `${value}n`;\n    } else {\n        return encodeURIComponent(\n            String(\n                value != null && Object.getPrototypeOf(value) === null\n                    ? // Plain objects with no prototype don't have a `toString` method.\n                      // Convert them before stringifying them.\n                      { ...(value as object) }\n                    : value,\n            ),\n        );\n    }\n}\n\nfunction encodeObjectContextEntry([key, value]: [string, unknown]): `${typeof key}=${string}` {\n    return `${key}=${encodeValue(value)}`;\n}\n\nexport function encodeContextObject(context: object): string {\n    const searchParamsString = Object.entries(context).map(encodeObjectContextEntry).join('&');\n    return __NODEJS__ ? Buffer.from(searchParamsString, 'utf8').toString('base64') : btoa(searchParamsString);\n}\n", "/**\n * To add a new error, follow the instructions at\n * https://github.com/anza-xyz/kit/tree/main/packages/errors#adding-a-new-error\n *\n * WARNING:\n *   - Don't change the meaning of an error message.\n */\nimport {\n    SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND,\n    SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED,\n    SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT,\n    SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT,\n    SOL<PERSON>A_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND,\n    SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED,\n    SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS,\n    SOLAN<PERSON>_ERROR__ADDRESSES__INVALID_BYTE_LENGTH,\n    SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY,\n    SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE,\n    SOLANA_ERROR__ADDRESSES__MALFORMED_PDA,\n    SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED,\n    SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED,\n    SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE,\n    SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER,\n    SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE,\n    SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED,\n    SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY,\n    SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS,\n    SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH,\n    SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH,\n    SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH,\n    SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH,\n    SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE,\n    SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH,\n    SOLANA_ERROR__CODECS__INVALID_CONSTANT,\n    SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT,\n    SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT,\n    SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT,\n    SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS,\n    SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE,\n    SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES,\n    SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE,\n    SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED,\n    SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS,\n    SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA,\n    SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW,\n    SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR,\n    SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS,\n    SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH,\n    SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM,\n    SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX,\n    SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND,\n    SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR,\n    SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER,\n    SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC,\n    SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID,\n    SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS,\n    SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION,\n    SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE,\n    SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR,\n    SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH,\n    SOLANA_ERROR__INVALID_NONCE,\n    SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING,\n    SOLANA_ERROR__INVARIANT_VIOLATION__DATA_PUBLISHER_CHANNEL_UNIMPLEMENTED,\n    SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE,\n    SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_STATE_MISSING,\n    SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE,\n    SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR,\n    SOLANA_ERROR__JSON_RPC__INVALID_PARAMS,\n    SOLANA_ERROR__JSON_RPC__INVALID_REQUEST,\n    SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND,\n    SOLANA_ERROR__JSON_RPC__PARSE_ERROR,\n    SOLANA_ERROR__JSON_RPC__SCAN_ERROR,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION,\n    SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH,\n    SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH,\n    SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH,\n    SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY,\n    SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE,\n    SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE,\n    SOLANA_ERROR__MALFORMED_BIGINT_STRING,\n    SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR,\n    SOLANA_ERROR__MALFORMED_NUMBER_STRING,\n    SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND,\n    SOLANA_ERROR__RPC__API_PLAN_MISSING_FOR_RPC_METHOD,\n    SOLANA_ERROR__RPC__INTEGER_OVERFLOW,\n    SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR,\n    SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN,\n    SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_PLAN,\n    SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CLOSED_BEFORE_MESSAGE_BUFFERED,\n    SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CONNECTION_CLOSED,\n    SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_FAILED_TO_CONNECT,\n    SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID,\n    SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS,\n    SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER,\n    SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER,\n    SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS,\n    SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING,\n    SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY,\n    SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT,\n    SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED,\n    SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED,\n    SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE,\n    SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING,\n    SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION,\n    SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES,\n    SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME,\n    SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME,\n    SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING,\n    SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE,\n    SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING,\n    SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND,\n    SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT,\n    SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT,\n    SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING,\n    SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING,\n    SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE,\n    SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING,\n    SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES,\n    SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE,\n    SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH,\n    SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING,\n    SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE,\n    SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING,\n    SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE,\n    SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE,\n    SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND,\n    SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND,\n    SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED,\n    SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND,\n    SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP,\n    SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE,\n    SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION,\n    SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE,\n    SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT,\n    SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT,\n    SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED,\n    SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE,\n    SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND,\n    SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED,\n    SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED,\n    SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE,\n    SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE,\n    SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS,\n    SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION,\n    SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN,\n    SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION,\n    SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT,\n    SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT,\n    SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT,\n    SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT,\n    SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT,\n    SolanaErrorCode,\n} from './codes';\n\n/**\n * A map of every {@link SolanaError} code to the error message shown to developers in development\n * mode.\n */\nexport const SolanaErrorMessages: Readonly<{\n    // This type makes this data structure exhaustive with respect to `SolanaErrorCode`.\n    // TypeScript will fail to build this project if add an error code without a message.\n    [P in SolanaErrorCode]: string;\n}> = {\n    [SOLANA_ERROR__ACCOUNTS__ACCOUNT_NOT_FOUND]: 'Account not found at address: $address',\n    [SOLANA_ERROR__ACCOUNTS__EXPECTED_ALL_ACCOUNTS_TO_BE_DECODED]:\n        'Not all accounts were decoded. Encoded accounts found at addresses: $addresses.',\n    [SOLANA_ERROR__ACCOUNTS__EXPECTED_DECODED_ACCOUNT]: 'Expected decoded account at address: $address',\n    [SOLANA_ERROR__ACCOUNTS__FAILED_TO_DECODE_ACCOUNT]: 'Failed to decode account data at address: $address',\n    [SOLANA_ERROR__ACCOUNTS__ONE_OR_MORE_ACCOUNTS_NOT_FOUND]: 'Accounts not found at addresses: $addresses',\n    [SOLANA_ERROR__ADDRESSES__FAILED_TO_FIND_VIABLE_PDA_BUMP_SEED]:\n        'Unable to find a viable program address bump seed.',\n    [SOLANA_ERROR__ADDRESSES__INVALID_BASE58_ENCODED_ADDRESS]: '$putativeAddress is not a base58-encoded address.',\n    [SOLANA_ERROR__ADDRESSES__INVALID_BYTE_LENGTH]:\n        'Expected base58 encoded address to decode to a byte array of length 32. Actual length: $actualLength.',\n    [SOLANA_ERROR__ADDRESSES__INVALID_ED25519_PUBLIC_KEY]: 'The `CryptoKey` must be an `Ed25519` public key.',\n    [SOLANA_ERROR__ADDRESSES__INVALID_SEEDS_POINT_ON_CURVE]: 'Invalid seeds; point must fall off the Ed25519 curve.',\n    [SOLANA_ERROR__ADDRESSES__MALFORMED_PDA]:\n        'Expected given program derived address to have the following format: [Address, ProgramDerivedAddressBump].',\n    [SOLANA_ERROR__ADDRESSES__MAX_NUMBER_OF_PDA_SEEDS_EXCEEDED]:\n        'A maximum of $maxSeeds seeds, including the bump seed, may be supplied when creating an address. Received: $actual.',\n    [SOLANA_ERROR__ADDRESSES__MAX_PDA_SEED_LENGTH_EXCEEDED]:\n        'The seed at index $index with length $actual exceeds the maximum length of $maxSeedLength bytes.',\n    [SOLANA_ERROR__ADDRESSES__PDA_BUMP_SEED_OUT_OF_RANGE]:\n        'Expected program derived address bump to be in the range [0, 255], got: $bump.',\n    [SOLANA_ERROR__ADDRESSES__PDA_ENDS_WITH_PDA_MARKER]: 'Program address cannot end with PDA marker.',\n    [SOLANA_ERROR__ADDRESSES__STRING_LENGTH_OUT_OF_RANGE]:\n        'Expected base58-encoded address string of length in the range [32, 44]. Actual length: $actualLength.',\n    [SOLANA_ERROR__BLOCKHASH_STRING_LENGTH_OUT_OF_RANGE]:\n        'Expected base58-encoded blockash string of length in the range [32, 44]. Actual length: $actualLength.',\n    [SOLANA_ERROR__BLOCK_HEIGHT_EXCEEDED]:\n        'The network has progressed past the last block for which this transaction could have been committed.',\n    [SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY]:\n        'Codec [$codecDescription] cannot decode empty byte arrays.',\n    [SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS]:\n        'Enum codec cannot use lexical values [$stringValues] as discriminators. Either remove all lexical values or set `useValuesAsDiscriminators` to `false`.',\n    [SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL]:\n        'Sentinel [$hexSentinel] must not be present in encoded bytes [$hexEncodedBytes].',\n    [SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH]:\n        'Encoder and decoder must have the same fixed size, got [$encoderFixedSize] and [$decoderFixedSize].',\n    [SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH]:\n        'Encoder and decoder must have the same max size, got [$encoderMaxSize] and [$decoderMaxSize].',\n    [SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH]:\n        'Encoder and decoder must either both be fixed-size or variable-size.',\n    [SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE]:\n        'Enum discriminator out of range. Expected a number in [$formattedValidDiscriminators], got $discriminator.',\n    [SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH]: 'Expected a fixed-size codec, got a variable-size one.',\n    [SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH]:\n        'Codec [$codecDescription] expected a positive byte length, got $bytesLength.',\n    [SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH]: 'Expected a variable-size codec, got a fixed-size one.',\n    [SOLANA_ERROR__CODECS__EXPECTED_ZERO_VALUE_TO_MATCH_ITEM_FIXED_SIZE]:\n        'Codec [$codecDescription] expected zero-value [$hexZeroValue] to have the same size as the provided fixed-size item [$expectedSize bytes].',\n    [SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH]:\n        'Codec [$codecDescription] expected $expected bytes, got $bytesLength.',\n    [SOLANA_ERROR__CODECS__INVALID_CONSTANT]:\n        'Expected byte array constant [$hexConstant] to be present in data [$hexData] at offset [$offset].',\n    [SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT]:\n        'Invalid discriminated union variant. Expected one of [$variants], got $value.',\n    [SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT]:\n        'Invalid enum variant. Expected one of [$stringValues] or a number in [$formattedNumericalValues], got $variant.',\n    [SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT]:\n        'Invalid literal union variant. Expected one of [$variants], got $value.',\n    [SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS]:\n        'Expected [$codecDescription] to have $expected items, got $actual.',\n    [SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE]: 'Invalid value $value for base $base with alphabet $alphabet.',\n    [SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE]:\n        'Literal union discriminator out of range. Expected a number between $minRange and $maxRange, got $discriminator.',\n    [SOLANA_ERROR__CODECS__NUMBER_OUT_OF_RANGE]:\n        'Codec [$codecDescription] expected number to be in the range [$min, $max], got $value.',\n    [SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE]:\n        'Codec [$codecDescription] expected offset to be in the range [0, $bytesLength], got $offset.',\n    [SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES]:\n        'Expected sentinel [$hexSentinel] to be present in decoded bytes [$hexDecodedBytes].',\n    [SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE]:\n        'Union variant out of range. Expected an index between $minRange and $maxRange, got $variant.',\n    [SOLANA_ERROR__CRYPTO__RANDOM_VALUES_FUNCTION_UNIMPLEMENTED]: 'No random values implementation could be found.',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_ALREADY_INITIALIZED]: 'instruction requires an uninitialized account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_FAILED]:\n        'instruction tries to borrow reference for an account which is already borrowed',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_BORROW_OUTSTANDING]:\n        'instruction left account with an outstanding borrowed reference',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_SIZE_CHANGED]:\n        \"program other than the account's owner changed the size of the account data\",\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_DATA_TOO_SMALL]: 'account data too small for instruction',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_EXECUTABLE]: 'instruction expected an executable account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ACCOUNT_NOT_RENT_EXEMPT]:\n        'An account does not have enough lamports to be rent-exempt',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ARITHMETIC_OVERFLOW]: 'Program arithmetic overflowed',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR]: 'Failed to serialize or deserialize account data: $encodedData',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__BUILTIN_PROGRAMS_MUST_CONSUME_COMPUTE_UNITS]:\n        'Builtin programs must consume compute units',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__CALL_DEPTH]: 'Cross-program invocation call depth too deep',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__COMPUTATIONAL_BUDGET_EXCEEDED]: 'Computational budget exceeded',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM]: 'custom program error: #$code',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_INDEX]: 'instruction contains duplicate accounts',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__DUPLICATE_ACCOUNT_OUT_OF_SYNC]:\n        'instruction modifications of multiply-passed account differ',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_ACCOUNT_NOT_RENT_EXEMPT]: 'executable accounts must be rent exempt',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_DATA_MODIFIED]: 'instruction changed executable accounts data',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_LAMPORT_CHANGE]:\n        'instruction changed the balance of an executable account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__EXECUTABLE_MODIFIED]: 'instruction changed executable bit of an account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_DATA_MODIFIED]:\n        'instruction modified data of an account it does not own',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__EXTERNAL_ACCOUNT_LAMPORT_SPEND]:\n        'instruction spent from the balance of an account it does not own',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__GENERIC_ERROR]: 'generic instruction error',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__ILLEGAL_OWNER]: 'Provided owner is not allowed',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__IMMUTABLE]: 'Account is immutable',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_AUTHORITY]: 'Incorrect authority provided',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INCORRECT_PROGRAM_ID]: 'incorrect program id for instruction',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INSUFFICIENT_FUNDS]: 'insufficient funds for instruction',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_DATA]: 'invalid account data for instruction',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ACCOUNT_OWNER]: 'Invalid account owner',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ARGUMENT]: 'invalid program argument',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_ERROR]: 'program returned invalid error code',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_INSTRUCTION_DATA]: 'invalid instruction data',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_REALLOC]: 'Failed to reallocate account data',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__INVALID_SEEDS]: 'Provided seeds do not result in a valid address',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_DATA_ALLOCATIONS_EXCEEDED]:\n        'Accounts data allocations exceeded the maximum allowed per transaction',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_ACCOUNTS_EXCEEDED]: 'Max accounts exceeded',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_INSTRUCTION_TRACE_LENGTH_EXCEEDED]: 'Max instruction trace length exceeded',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__MAX_SEED_LENGTH_EXCEEDED]:\n        'Length of the seed is too long for address generation',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_ACCOUNT]: 'An account required by the instruction is missing',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__MISSING_REQUIRED_SIGNATURE]: 'missing required signature for instruction',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__MODIFIED_PROGRAM_ID]:\n        'instruction illegally modified the program id of an account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__NOT_ENOUGH_ACCOUNT_KEYS]: 'insufficient account keys for instruction',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__PRIVILEGE_ESCALATION]:\n        'Cross-program invocation with unauthorized signer or writable account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_ENVIRONMENT_SETUP_FAILURE]:\n        'Failed to create program execution environment',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPILE]: 'Program failed to compile',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__PROGRAM_FAILED_TO_COMPLETE]: 'Program failed to complete',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_DATA_MODIFIED]: 'instruction modified data of a read-only account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__READONLY_LAMPORT_CHANGE]:\n        'instruction changed the balance of a read-only account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__REENTRANCY_NOT_ALLOWED]:\n        'Cross-program invocation reentrancy not allowed for this instruction',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__RENT_EPOCH_MODIFIED]: 'instruction modified rent epoch of an account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__UNBALANCED_INSTRUCTION]:\n        'sum of account balances before and after instruction do not match',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__UNINITIALIZED_ACCOUNT]: 'instruction requires an initialized account',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN]: '',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_PROGRAM_ID]: 'Unsupported program id',\n    [SOLANA_ERROR__INSTRUCTION_ERROR__UNSUPPORTED_SYSVAR]: 'Unsupported sysvar',\n    [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_ACCOUNTS]: 'The instruction does not have any accounts.',\n    [SOLANA_ERROR__INSTRUCTION__EXPECTED_TO_HAVE_DATA]: 'The instruction does not have any data.',\n    [SOLANA_ERROR__INSTRUCTION__PROGRAM_ID_MISMATCH]:\n        'Expected instruction to have progress address $expectedProgramAddress, got $actualProgramAddress.',\n    [SOLANA_ERROR__INVALID_BLOCKHASH_BYTE_LENGTH]:\n        'Expected base58 encoded blockhash to decode to a byte array of length 32. Actual length: $actualLength.',\n    [SOLANA_ERROR__INVALID_NONCE]:\n        'The nonce `$expectedNonceValue` is no longer valid. It has advanced to `$actualNonceValue`',\n    [SOLANA_ERROR__INVARIANT_VIOLATION__CACHED_ABORTABLE_ITERABLE_CACHE_ENTRY_MISSING]:\n        'Invariant violation: Found no abortable iterable cache entry for key `$cacheKey`. It ' +\n        'should be impossible to hit this error; please file an issue at ' +\n        'https://sola.na/web3invariant',\n    [SOLANA_ERROR__INVARIANT_VIOLATION__DATA_PUBLISHER_CHANNEL_UNIMPLEMENTED]:\n        'Invariant violation: This data publisher does not publish to the channel named ' +\n        '`$channelName`. Supported channels include $supportedChannelNames.',\n    [SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_MUST_NOT_POLL_BEFORE_RESOLVING_EXISTING_MESSAGE_PROMISE]:\n        'Invariant violation: WebSocket message iterator state is corrupt; iterated without first ' +\n        'resolving existing message promise. It should be impossible to hit this error; please ' +\n        'file an issue at https://sola.na/web3invariant',\n    [SOLANA_ERROR__INVARIANT_VIOLATION__SUBSCRIPTION_ITERATOR_STATE_MISSING]:\n        'Invariant violation: WebSocket message iterator is missing state storage. It should be ' +\n        'impossible to hit this error; please file an issue at https://sola.na/web3invariant',\n    [SOLANA_ERROR__INVARIANT_VIOLATION__SWITCH_MUST_BE_EXHAUSTIVE]:\n        'Invariant violation: Switch statement non-exhaustive. Received unexpected value ' +\n        '`$unexpectedValue`. It should be impossible to hit this error; please file an issue at ' +\n        'https://sola.na/web3invariant',\n    [SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR]: 'JSON-RPC error: Internal JSON-RPC error ($__serverMessage)',\n    [SOLANA_ERROR__JSON_RPC__INVALID_PARAMS]: 'JSON-RPC error: Invalid method parameter(s) ($__serverMessage)',\n    [SOLANA_ERROR__JSON_RPC__INVALID_REQUEST]:\n        'JSON-RPC error: The JSON sent is not a valid `Request` object ($__serverMessage)',\n    [SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND]:\n        'JSON-RPC error: The method does not exist / is not available ($__serverMessage)',\n    [SOLANA_ERROR__JSON_RPC__PARSE_ERROR]:\n        'JSON-RPC error: An error occurred on the server while parsing the JSON text ($__serverMessage)',\n    [SOLANA_ERROR__JSON_RPC__SCAN_ERROR]: '$__serverMessage',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP]: '$__serverMessage',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE]: '$__serverMessage',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET]: '$__serverMessage',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX]: '$__serverMessage',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED]: '$__serverMessage',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED]: 'Minimum context slot has not been reached',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NODE_UNHEALTHY]: 'Node is unhealthy; behind by $numSlotsBehind slots',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_NO_SNAPSHOT]: 'No snapshot',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE]: 'Transaction simulation failed',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED]: '$__serverMessage',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE]:\n        'Transaction history is not available from this node',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE]: '$__serverMessage',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH]: 'Transaction signature length mismatch',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE]:\n        'Transaction signature verification failure',\n    [SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION]: '$__serverMessage',\n    [SOLANA_ERROR__KEYS__INVALID_KEY_PAIR_BYTE_LENGTH]: 'Key pair bytes must be of length 64, got $byteLength.',\n    [SOLANA_ERROR__KEYS__INVALID_PRIVATE_KEY_BYTE_LENGTH]:\n        'Expected private key bytes with length 32. Actual length: $actualLength.',\n    [SOLANA_ERROR__KEYS__INVALID_SIGNATURE_BYTE_LENGTH]:\n        'Expected base58-encoded signature to decode to a byte array of length 64. Actual length: $actualLength.',\n    [SOLANA_ERROR__KEYS__PUBLIC_KEY_MUST_MATCH_PRIVATE_KEY]:\n        'The provided private key does not match the provided public key.',\n    [SOLANA_ERROR__KEYS__SIGNATURE_STRING_LENGTH_OUT_OF_RANGE]:\n        'Expected base58-encoded signature string of length in the range [64, 88]. Actual length: $actualLength.',\n    [SOLANA_ERROR__LAMPORTS_OUT_OF_RANGE]: 'Lamports value must be in the range [0, 2e64-1]',\n    [SOLANA_ERROR__MALFORMED_BIGINT_STRING]: '`$value` cannot be parsed as a `BigInt`',\n    [SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR]: '$message',\n    [SOLANA_ERROR__MALFORMED_NUMBER_STRING]: '`$value` cannot be parsed as a `Number`',\n    [SOLANA_ERROR__NONCE_ACCOUNT_NOT_FOUND]: 'No nonce account could be found at address `$nonceAccountAddress`',\n    [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CANNOT_CREATE_SUBSCRIPTION_PLAN]:\n        \"The notification name must end in 'Notifications' and the API must supply a \" +\n        \"subscription plan creator function for the notification '$notificationName'.\",\n    [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CLOSED_BEFORE_MESSAGE_BUFFERED]:\n        'WebSocket was closed before payload could be added to the send buffer',\n    [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_CONNECTION_CLOSED]: 'WebSocket connection closed',\n    [SOLANA_ERROR__RPC_SUBSCRIPTIONS__CHANNEL_FAILED_TO_CONNECT]: 'WebSocket failed to connect',\n    [SOLANA_ERROR__RPC_SUBSCRIPTIONS__EXPECTED_SERVER_SUBSCRIPTION_ID]:\n        'Failed to obtain a subscription id from the server',\n    [SOLANA_ERROR__RPC__API_PLAN_MISSING_FOR_RPC_METHOD]: 'Could not find an API plan for RPC method: `$method`',\n    [SOLANA_ERROR__RPC__INTEGER_OVERFLOW]:\n        'The $argumentLabel argument to the `$methodName` RPC method$optionalPathLabel was ' +\n        '`$value`. This number is unsafe for use with the Solana JSON-RPC because it exceeds ' +\n        '`Number.MAX_SAFE_INTEGER`.',\n    [SOLANA_ERROR__RPC__TRANSPORT_HTTP_ERROR]: 'HTTP error ($statusCode): $message',\n    [SOLANA_ERROR__RPC__TRANSPORT_HTTP_HEADER_FORBIDDEN]:\n        'HTTP header(s) forbidden: $headers. Learn more at ' +\n        'https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name.',\n    [SOLANA_ERROR__SIGNER__ADDRESS_CANNOT_HAVE_MULTIPLE_SIGNERS]:\n        'Multiple distinct signers were identified for address `$address`. Please ensure that ' +\n        'you are using the same signer instance for each address.',\n    [SOLANA_ERROR__SIGNER__EXPECTED_KEY_PAIR_SIGNER]:\n        'The provided value does not implement the `KeyPairSigner` interface',\n    [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_MODIFYING_SIGNER]:\n        'The provided value does not implement the `MessageModifyingSigner` interface',\n    [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_PARTIAL_SIGNER]:\n        'The provided value does not implement the `MessagePartialSigner` interface',\n    [SOLANA_ERROR__SIGNER__EXPECTED_MESSAGE_SIGNER]:\n        'The provided value does not implement any of the `MessageSigner` interfaces',\n    [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_MODIFYING_SIGNER]:\n        'The provided value does not implement the `TransactionModifyingSigner` interface',\n    [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_PARTIAL_SIGNER]:\n        'The provided value does not implement the `TransactionPartialSigner` interface',\n    [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SENDING_SIGNER]:\n        'The provided value does not implement the `TransactionSendingSigner` interface',\n    [SOLANA_ERROR__SIGNER__EXPECTED_TRANSACTION_SIGNER]:\n        'The provided value does not implement any of the `TransactionSigner` interfaces',\n    [SOLANA_ERROR__SIGNER__TRANSACTION_CANNOT_HAVE_MULTIPLE_SENDING_SIGNERS]:\n        'More than one `TransactionSendingSigner` was identified.',\n    [SOLANA_ERROR__SIGNER__TRANSACTION_SENDING_SIGNER_MISSING]:\n        'No `TransactionSendingSigner` was identified. Please provide a valid ' +\n        '`ITransactionWithSingleSendingSigner` transaction.',\n    [SOLANA_ERROR__SIGNER__WALLET_MULTISIGN_UNIMPLEMENTED]:\n        'Wallet account signers do not support signing multiple messages/transactions in a single operation',\n    [SOLANA_ERROR__SUBTLE_CRYPTO__CANNOT_EXPORT_NON_EXTRACTABLE_KEY]: 'Cannot export a non-extractable key.',\n    [SOLANA_ERROR__SUBTLE_CRYPTO__DIGEST_UNIMPLEMENTED]: 'No digest implementation could be found.',\n    [SOLANA_ERROR__SUBTLE_CRYPTO__DISALLOWED_IN_INSECURE_CONTEXT]:\n        'Cryptographic operations are only allowed in secure browser contexts. Read more ' +\n        'here: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts.',\n    [SOLANA_ERROR__SUBTLE_CRYPTO__ED25519_ALGORITHM_UNIMPLEMENTED]:\n        'This runtime does not support the generation of Ed25519 key pairs.\\n\\nInstall ' +\n        '@solana/webcrypto-ed25519-polyfill and call its `install` function before generating keys in ' +\n        'environments that do not support Ed25519.\\n\\nFor a list of runtimes that ' +\n        'currently support Ed25519 operations, visit ' +\n        'https://github.com/WICG/webcrypto-secure-curves/issues/20.',\n    [SOLANA_ERROR__SUBTLE_CRYPTO__EXPORT_FUNCTION_UNIMPLEMENTED]:\n        'No signature verification implementation could be found.',\n    [SOLANA_ERROR__SUBTLE_CRYPTO__GENERATE_FUNCTION_UNIMPLEMENTED]: 'No key generation implementation could be found.',\n    [SOLANA_ERROR__SUBTLE_CRYPTO__SIGN_FUNCTION_UNIMPLEMENTED]: 'No signing implementation could be found.',\n    [SOLANA_ERROR__SUBTLE_CRYPTO__VERIFY_FUNCTION_UNIMPLEMENTED]: 'No key export implementation could be found.',\n    [SOLANA_ERROR__TIMESTAMP_OUT_OF_RANGE]:\n        'Timestamp value must be in the range [-(2n ** 63n), (2n ** 63n) - 1]. `$value` given',\n    [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_BORROW_OUTSTANDING]:\n        'Transaction processing left an account with an outstanding borrowed reference',\n    [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_IN_USE]: 'Account in use',\n    [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_LOADED_TWICE]: 'Account loaded twice',\n    [SOLANA_ERROR__TRANSACTION_ERROR__ACCOUNT_NOT_FOUND]:\n        'Attempt to debit an account but found no record of a prior credit.',\n    [SOLANA_ERROR__TRANSACTION_ERROR__ADDRESS_LOOKUP_TABLE_NOT_FOUND]:\n        \"Transaction loads an address table account that doesn't exist\",\n    [SOLANA_ERROR__TRANSACTION_ERROR__ALREADY_PROCESSED]: 'This transaction has already been processed',\n    [SOLANA_ERROR__TRANSACTION_ERROR__BLOCKHASH_NOT_FOUND]: 'Blockhash not found',\n    [SOLANA_ERROR__TRANSACTION_ERROR__CALL_CHAIN_TOO_DEEP]: 'Loader call chain is too deep',\n    [SOLANA_ERROR__TRANSACTION_ERROR__CLUSTER_MAINTENANCE]:\n        'Transactions are currently disabled due to cluster maintenance',\n    [SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION]:\n        'Transaction contains a duplicate instruction ($index) that is not allowed',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_FEE]: 'Insufficient funds for fee',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT]:\n        'Transaction results in an account ($accountIndex) with insufficient funds for rent',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_FOR_FEE]: 'This account may not be used to pay transaction fees',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ACCOUNT_INDEX]: 'Transaction contains an invalid account reference',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_DATA]:\n        'Transaction loads an address table account with invalid data',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_INDEX]:\n        'Transaction address table lookup uses an invalid index',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_ADDRESS_LOOKUP_TABLE_OWNER]:\n        'Transaction loads an address table account with an invalid owner',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_LOADED_ACCOUNTS_DATA_SIZE_LIMIT]:\n        'LoadedAccountsDataSizeLimit set for transaction must be greater than 0.',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_PROGRAM_FOR_EXECUTION]:\n        'This program may not be used for executing instructions',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_RENT_PAYING_ACCOUNT]:\n        'Transaction leaves an account with a lower balance than rent-exempt minimum',\n    [SOLANA_ERROR__TRANSACTION_ERROR__INVALID_WRITABLE_ACCOUNT]:\n        'Transaction loads a writable account that cannot be written',\n    [SOLANA_ERROR__TRANSACTION_ERROR__MAX_LOADED_ACCOUNTS_DATA_SIZE_EXCEEDED]:\n        'Transaction exceeded max loaded accounts data size cap',\n    [SOLANA_ERROR__TRANSACTION_ERROR__MISSING_SIGNATURE_FOR_FEE]:\n        'Transaction requires a fee but has no signature present',\n    [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_ACCOUNT_NOT_FOUND]: 'Attempt to load a program that does not exist',\n    [SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED]:\n        'Execution of the program referenced by account at index $accountIndex is temporarily restricted.',\n    [SOLANA_ERROR__TRANSACTION_ERROR__RESANITIZATION_NEEDED]: 'ResanitizationNeeded',\n    [SOLANA_ERROR__TRANSACTION_ERROR__SANITIZE_FAILURE]: 'Transaction failed to sanitize accounts offsets correctly',\n    [SOLANA_ERROR__TRANSACTION_ERROR__SIGNATURE_FAILURE]: 'Transaction did not pass signature verification',\n    [SOLANA_ERROR__TRANSACTION_ERROR__TOO_MANY_ACCOUNT_LOCKS]: 'Transaction locked too many accounts',\n    [SOLANA_ERROR__TRANSACTION_ERROR__UNBALANCED_TRANSACTION]:\n        'Sum of account balances before and after transaction do not match',\n    [SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN]: 'The transaction failed with the error `$errorName`',\n    [SOLANA_ERROR__TRANSACTION_ERROR__UNSUPPORTED_VERSION]: 'Transaction version is unsupported',\n    [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_BLOCK_LIMIT]:\n        'Transaction would exceed account data limit within the block',\n    [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_ACCOUNT_DATA_TOTAL_LIMIT]:\n        'Transaction would exceed total account data limit',\n    [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_ACCOUNT_COST_LIMIT]:\n        'Transaction would exceed max account limit within the block',\n    [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_BLOCK_COST_LIMIT]:\n        'Transaction would exceed max Block Cost Limit',\n    [SOLANA_ERROR__TRANSACTION_ERROR__WOULD_EXCEED_MAX_VOTE_COST_LIMIT]: 'Transaction would exceed max Vote Cost Limit',\n    [SOLANA_ERROR__TRANSACTION__ADDRESSES_CANNOT_SIGN_TRANSACTION]:\n        'Attempted to sign a transaction with an address that is not a signer for it',\n    [SOLANA_ERROR__TRANSACTION__ADDRESS_MISSING]: 'Transaction is missing an address at index: $index.',\n    [SOLANA_ERROR__TRANSACTION__CANNOT_ENCODE_WITH_EMPTY_SIGNATURES]:\n        'Transaction has no expected signers therefore it cannot be encoded',\n    [SOLANA_ERROR__TRANSACTION__EXPECTED_BLOCKHASH_LIFETIME]: 'Transaction does not have a blockhash lifetime',\n    [SOLANA_ERROR__TRANSACTION__EXPECTED_NONCE_LIFETIME]: 'Transaction is not a durable nonce transaction',\n    [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_CONTENTS_MISSING]:\n        'Contents of these address lookup tables unknown: $lookupTableAddresses',\n    [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_ADDRESS_LOOKUP_TABLE_INDEX_OUT_OF_RANGE]:\n        'Lookup of address at index $highestRequestedIndex failed for lookup table ' +\n        '`$lookupTableAddress`. Highest known index is $highestKnownIndex. The lookup table ' +\n        'may have been extended since its contents were retrieved',\n    [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_FEE_PAYER_MISSING]: 'No fee payer set in CompiledTransaction',\n    [SOLANA_ERROR__TRANSACTION__FAILED_TO_DECOMPILE_INSTRUCTION_PROGRAM_ADDRESS_NOT_FOUND]:\n        'Could not find program address at index $index',\n    [SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT]:\n        'Failed to estimate the compute unit consumption for this transaction message. This is ' +\n        'likely because simulating the transaction failed. Inspect the `cause` property of this ' +\n        'error to learn more',\n    [SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT]:\n        'Transaction failed when it was simulated in order to estimate the compute unit consumption. ' +\n        'The compute unit estimate provided is for a transaction that failed when simulated and may not ' +\n        'be representative of the compute units this transaction would consume if successful. Inspect the ' +\n        '`cause` property of this error to learn more',\n    [SOLANA_ERROR__TRANSACTION__FEE_PAYER_MISSING]: 'Transaction is missing a fee payer.',\n    [SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING]:\n        \"Could not determine this transaction's signature. Make sure that the transaction has \" +\n        'been signed by its fee payer.',\n    [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_FIRST_INSTRUCTION_MUST_BE_ADVANCE_NONCE]:\n        'Transaction first instruction is not advance nonce account instruction.',\n    [SOLANA_ERROR__TRANSACTION__INVALID_NONCE_TRANSACTION_INSTRUCTIONS_MISSING]:\n        'Transaction with no instructions cannot be durable nonce transaction.',\n    [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_CANNOT_PAY_FEES]:\n        'This transaction includes an address (`$programAddress`) which is both ' +\n        'invoked and set as the fee payer. Program addresses may not pay fees',\n    [SOLANA_ERROR__TRANSACTION__INVOKED_PROGRAMS_MUST_NOT_BE_WRITABLE]:\n        'This transaction includes an address (`$programAddress`) which is both invoked and ' +\n        'marked writable. Program addresses may not be writable',\n    [SOLANA_ERROR__TRANSACTION__MESSAGE_SIGNATURES_MISMATCH]:\n        'The transaction message expected the transaction to have $signerAddressesLength signatures, got $signaturesLength.',\n    [SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING]: 'Transaction is missing signatures for addresses: $addresses.',\n    [SOLANA_ERROR__TRANSACTION__VERSION_NUMBER_OUT_OF_RANGE]:\n        'Transaction version must be in the range [0, 127]. `$actualVersion` given',\n};\n", "import { SolanaErrorCode } from './codes';\nimport { encodeContextObject } from './context';\nimport { SolanaErrorMessages } from './messages';\n\nconst enum StateType {\n    EscapeSequence,\n    Text,\n    Variable,\n}\ntype State = Readonly<{\n    [START_INDEX]: number;\n    [TYPE]: StateType;\n}>;\nconst START_INDEX = 'i';\nconst TYPE = 't';\n\nexport function getHumanReadableErrorMessage<TErrorCode extends SolanaErrorCode>(\n    code: TErrorCode,\n    context: object = {},\n): string {\n    const messageFormatString = SolanaErrorMessages[code];\n    if (messageFormatString.length === 0) {\n        return '';\n    }\n    let state: State;\n    function commitStateUpTo(endIndex?: number) {\n        if (state[TYPE] === StateType.Variable) {\n            const variableName = messageFormatString.slice(state[START_INDEX] + 1, endIndex);\n\n            fragments.push(\n                variableName in context\n                    ? // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n                      `${context[variableName as keyof typeof context]}`\n                    : `$${variableName}`,\n            );\n        } else if (state[TYPE] === StateType.Text) {\n            fragments.push(messageFormatString.slice(state[START_INDEX], endIndex));\n        }\n    }\n    const fragments: string[] = [];\n    messageFormatString.split('').forEach((char, ii) => {\n        if (ii === 0) {\n            state = {\n                [START_INDEX]: 0,\n                [TYPE]:\n                    messageFormatString[0] === '\\\\'\n                        ? StateType.EscapeSequence\n                        : messageFormatString[0] === '$'\n                          ? StateType.Variable\n                          : StateType.Text,\n            };\n            return;\n        }\n        let nextState;\n        switch (state[TYPE]) {\n            case StateType.EscapeSequence:\n                nextState = { [START_INDEX]: ii, [TYPE]: StateType.Text };\n                break;\n            case StateType.Text:\n                if (char === '\\\\') {\n                    nextState = { [START_INDEX]: ii, [TYPE]: StateType.EscapeSequence };\n                } else if (char === '$') {\n                    nextState = { [START_INDEX]: ii, [TYPE]: StateType.Variable };\n                }\n                break;\n            case StateType.Variable:\n                if (char === '\\\\') {\n                    nextState = { [START_INDEX]: ii, [TYPE]: StateType.EscapeSequence };\n                } else if (char === '$') {\n                    nextState = { [START_INDEX]: ii, [TYPE]: StateType.Variable };\n                } else if (!char.match(/\\w/)) {\n                    nextState = { [START_INDEX]: ii, [TYPE]: StateType.Text };\n                }\n                break;\n        }\n        if (nextState) {\n            if (state !== nextState) {\n                commitStateUpTo(ii);\n            }\n            state = nextState;\n        }\n    });\n    commitStateUpTo();\n    return fragments.join('');\n}\n\nexport function getErrorMessage<TErrorCode extends SolanaErrorCode>(\n    code: TErrorCode,\n    context: Record<string, unknown> = {},\n): string {\n    if (process.env.NODE_ENV !== \"production\") {\n        return getHumanReadableErrorMessage(code, context);\n    } else {\n        let decodingAdviceMessage = `Solana error #${code}; Decode this error by running \\`npx @solana/errors decode -- ${code}`;\n        if (Object.keys(context).length) {\n            /**\n             * DANGER: Be sure that the shell command is escaped in such a way that makes it\n             *         impossible for someone to craft malicious context values that would result in\n             *         an exploit against anyone who bindly copy/pastes it into their terminal.\n             */\n            decodingAdviceMessage += ` '${encodeContextObject(context)}'`;\n        }\n        return `${decodingAdviceMessage}\\``;\n    }\n}\n", "import { SolanaErrorCode, SolanaErrorCodeWithCause } from './codes';\nimport { SolanaErrorContext } from './context';\nimport { getErrorMessage } from './message-formatter';\n\n/**\n * A type guard that returns `true` if the input is a {@link SolanaError}, optionally with a\n * particular error code.\n *\n * When the `code` argument is supplied and the input is a {@link SolanaError}, TypeScript will\n * refine the error's {@link SolanaError#context | `context`} property to the type associated with\n * that error code. You can use that context to render useful error messages, or to make\n * context-aware decisions that help your application to recover from the error.\n *\n * @example\n * ```ts\n * import {\n *     SOLANA_ERROR__TRANSACTION__MISSING_SIGNATURE,\n *     SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING,\n *     isSolanaError,\n * } from '@solana/errors';\n * import { assertTransactionIsFullySigned, getSignatureFromTransaction } from '@solana/transactions';\n *\n * try {\n *     const transactionSignature = getSignatureFromTransaction(tx);\n *     assertTransactionIsFullySigned(tx);\n *     /* ... *\\/\n * } catch (e) {\n *     if (isSolanaError(e, SOLANA_ERROR__TRANSACTION__SIGNATURES_MISSING)) {\n *         displayError(\n *             \"We can't send this transaction without signatures for these addresses:\\n- %s\",\n *             // The type of the `context` object is now refined to contain `addresses`.\n *             e.context.addresses.join('\\n- '),\n *         );\n *         return;\n *     } else if (isSolanaError(e, SOLANA_ERROR__TRANSACTION__FEE_PAYER_SIGNATURE_MISSING)) {\n *         if (!tx.feePayer) {\n *             displayError('Choose a fee payer for this transaction before sending it');\n *         } else {\n *             displayError('The fee payer still needs to sign for this transaction');\n *         }\n *         return;\n *     }\n *     throw e;\n * }\n * ```\n */\nexport function isSolanaError<TErrorCode extends SolanaErrorCode>(\n    e: unknown,\n    /**\n     * When supplied, this function will require that the input is a {@link SolanaError} _and_ that\n     * its error code is exactly this value.\n     */\n    code?: TErrorCode,\n): e is SolanaError<TErrorCode> {\n    const isSolanaError = e instanceof Error && e.name === 'SolanaError';\n    if (isSolanaError) {\n        if (code !== undefined) {\n            return (e as SolanaError<TErrorCode>).context.__code === code;\n        }\n        return true;\n    }\n    return false;\n}\n\ntype SolanaErrorCodedContext = Readonly<{\n    [P in SolanaErrorCode]: (SolanaErrorContext[P] extends undefined ? object : SolanaErrorContext[P]) & {\n        __code: P;\n    };\n}>;\n\n/**\n * Encapsulates an error's stacktrace, a Solana-specific numeric code that indicates what went\n * wrong, and optional context if the type of error indicated by the code supports it.\n */\nexport class SolanaError<TErrorCode extends SolanaErrorCode = SolanaErrorCode> extends Error {\n    /**\n     * Indicates the root cause of this {@link SolanaError}, if any.\n     *\n     * For example, a transaction error might have an instruction error as its root cause. In this\n     * case, you will be able to access the instruction error on the transaction error as `cause`.\n     */\n    readonly cause?: TErrorCode extends SolanaErrorCodeWithCause ? SolanaError : unknown = this.cause;\n    /**\n     * Contains context that can assist in understanding or recovering from a {@link SolanaError}.\n     */\n    readonly context: SolanaErrorCodedContext[TErrorCode];\n    constructor(\n        ...[code, contextAndErrorOptions]: SolanaErrorContext[TErrorCode] extends undefined\n            ? [code: TErrorCode, errorOptions?: ErrorOptions | undefined]\n            : [code: TErrorCode, contextAndErrorOptions: SolanaErrorContext[TErrorCode] & (ErrorOptions | undefined)]\n    ) {\n        let context: SolanaErrorContext[TErrorCode] | undefined;\n        let errorOptions: ErrorOptions | undefined;\n        if (contextAndErrorOptions) {\n            // If the `ErrorOptions` type ever changes, update this code.\n            const { cause, ...contextRest } = contextAndErrorOptions;\n            if (cause) {\n                errorOptions = { cause };\n            }\n            if (Object.keys(contextRest).length > 0) {\n                context = contextRest as SolanaErrorContext[TErrorCode];\n            }\n        }\n        const message = getErrorMessage(code, context);\n        super(message, errorOptions);\n        this.context = {\n            __code: code,\n            ...context,\n        } as SolanaErrorCodedContext[TErrorCode];\n        // This is necessary so that `isSolanaError()` can identify a `SolanaError` without having\n        // to import the class for use in an `instanceof` check.\n        this.name = 'SolanaError';\n    }\n}\n", "export function safeCaptureStackTrace(...args: Parameters<typeof Error.captureStackTrace>): void {\n    if ('captureStackTrace' in Error && typeof Error.captureStackTrace === 'function') {\n        Error.captureStackTrace(...args);\n    }\n}\n", "import { SolanaErrorCode } from './codes';\nimport { SolanaErrorContext } from './context';\nimport { SolanaError } from './error';\nimport { safeCaptureStackTrace } from './stack-trace';\n\ntype Config = Readonly<{\n    /**\n     * Oh, hello. You might wonder what in tarnation is going on here. Allow us to explain.\n     *\n     * One of the goals of `@solana/errors` is to allow errors that are not interesting to your\n     * application to shake out of your app bundle in production. This means that we must never\n     * export large hardcoded maps of error codes/messages.\n     *\n     * Unfortunately, where instruction and transaction errors from the RPC are concerned, we have\n     * no choice but to keep a map between the RPC `rpcEnumError` enum name and its corresponding\n     * `SolanaError` code. In the interest of implementing that map in as few bytes of source code\n     * as possible, we do the following:\n     *\n     *   1. Reserve a block of sequential error codes for the enum in question\n     *   2. Hardcode the list of enum names in that same order\n     *   3. Match the enum error name from the RPC with its index in that list, and reconstruct the\n     *      `SolanaError` code by adding the `errorCodeBaseOffset` to that index\n     */\n    errorCodeBaseOffset: number;\n    getErrorContext: (\n        errorCode: SolanaErrorCode,\n        rpcErrorName: string,\n        rpcErrorContext?: unknown,\n    ) => SolanaErrorContext[SolanaErrorCode];\n    orderedErrorNames: string[];\n    rpcEnumError: string | { [key: string]: unknown };\n}>;\n\nexport function getSolanaErrorFromRpcError(\n    { errorCodeBaseOffset, getErrorContext, orderedErrorNames, rpcEnumError }: Config,\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\n    constructorOpt: Function,\n): SolanaError {\n    let rpcErrorName;\n    let rpcErrorContext;\n    if (typeof rpcEnumError === 'string') {\n        rpcErrorName = rpcEnumError;\n    } else {\n        rpcErrorName = Object.keys(rpcEnumError)[0];\n        rpcErrorContext = rpcEnumError[rpcErrorName];\n    }\n    const codeOffset = orderedErrorNames.indexOf(rpcErrorName);\n    const errorCode = (errorCodeBaseOffset + codeOffset) as SolanaErrorCode;\n    const errorContext = getErrorContext(errorCode, rpcErrorName, rpcErrorContext);\n    const err = new SolanaError(errorCode, errorContext);\n    safeCaptureStackTrace(err, constructorOpt);\n    return err;\n}\n", "import {\n    SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR,\n    SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM,\n    SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN,\n} from './codes';\nimport { SolanaError } from './error';\nimport { getSolanaErrorFromRpcError } from './rpc-enum-errors';\n\nconst ORDERED_ERROR_NAMES = [\n    // Keep synced with RPC source: https://github.com/anza-xyz/agave/blob/master/sdk/program/src/instruction.rs\n    // If this list ever gets too large, consider implementing a compression strategy like this:\n    // https://gist.github.com/steveluscher/aaa7cbbb5433b1197983908a40860c47\n    'GenericError',\n    'InvalidArgument',\n    'InvalidInstructionData',\n    'InvalidAccountData',\n    'AccountDataTooSmall',\n    'InsufficientFunds',\n    'IncorrectProgramId',\n    'MissingRequiredSignature',\n    'AccountAlreadyInitialized',\n    'UninitializedAccount',\n    'UnbalancedInstruction',\n    'ModifiedProgramId',\n    'ExternalAccountLamportSpend',\n    'ExternalAccountDataModified',\n    'ReadonlyLamportChange',\n    'ReadonlyDataModified',\n    'DuplicateAccountIndex',\n    'ExecutableModified',\n    'RentEpochModified',\n    'NotEnoughAccountKeys',\n    'AccountDataSizeChanged',\n    'AccountNotExecutable',\n    'AccountBorrowFailed',\n    'AccountBorrowOutstanding',\n    'DuplicateAccountOutOfSync',\n    'Custom',\n    'InvalidError',\n    'ExecutableDataModified',\n    'ExecutableLamportChange',\n    'ExecutableAccountNotRentExempt',\n    'UnsupportedProgramId',\n    'CallDepth',\n    'MissingAccount',\n    'ReentrancyNotAllowed',\n    'MaxSeedLengthExceeded',\n    'InvalidSeeds',\n    'InvalidRealloc',\n    'ComputationalBudgetExceeded',\n    'PrivilegeEscalation',\n    'ProgramEnvironmentSetupFailure',\n    'ProgramFailedToComplete',\n    'ProgramFailedToCompile',\n    'Immutable',\n    'IncorrectAuthority',\n    'BorshIoError',\n    'AccountNotRentExempt',\n    'InvalidAccountOwner',\n    'ArithmeticOverflow',\n    'UnsupportedSysvar',\n    'IllegalOwner',\n    'MaxAccountsDataAllocationsExceeded',\n    'MaxAccountsExceeded',\n    'MaxInstructionTraceLengthExceeded',\n    'BuiltinProgramsMustConsumeComputeUnits',\n];\n\nexport function getSolanaErrorFromInstructionError(\n    /**\n     * The index of the instruction inside the transaction.\n     */\n    index: bigint | number,\n    instructionError: string | { [key: string]: unknown },\n): SolanaError {\n    const numberIndex = Number(index);\n    return getSolanaErrorFromRpcError(\n        {\n            errorCodeBaseOffset: 4615001,\n            getErrorContext(errorCode, rpcErrorName, rpcErrorContext) {\n                if (errorCode === SOLANA_ERROR__INSTRUCTION_ERROR__UNKNOWN) {\n                    return {\n                        errorName: rpcErrorName,\n                        index: numberIndex,\n                        ...(rpcErrorContext !== undefined ? { instructionErrorContext: rpcErrorContext } : null),\n                    };\n                } else if (errorCode === SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM) {\n                    return {\n                        code: Number(rpcErrorContext as bigint | number),\n                        index: numberIndex,\n                    };\n                } else if (errorCode === SOLANA_ERROR__INSTRUCTION_ERROR__BORSH_IO_ERROR) {\n                    return {\n                        encodedData: rpcErrorContext as string,\n                        index: numberIndex,\n                    };\n                }\n                return { index: numberIndex };\n            },\n            orderedErrorNames: ORDERED_ERROR_NAMES,\n            rpcEnumError: instructionError,\n        },\n        getSolanaErrorFromInstructionError,\n    );\n}\n", "import {\n    SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION,\n    SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT,\n    SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED,\n    SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN,\n} from './codes';\nimport { SolanaError } from './error';\nimport { getSolanaErrorFromInstructionError } from './instruction-error';\nimport { getSolanaErrorFromRpcError } from './rpc-enum-errors';\n\n/**\n * How to add an error when an entry is added to the RPC `TransactionError` enum:\n *\n *   1. Follow the instructions in `./codes.ts` to add a corresponding Solana error code\n *   2. Add the `TransactionError` enum name in the same order as it appears in `./codes.ts`\n *   3. Add the new error name/code mapping to `./__tests__/transaction-error-test.ts`\n */\nconst ORDERED_ERROR_NAMES = [\n    // Keep synced with RPC source: https://github.com/anza-xyz/agave/blob/master/sdk/src/transaction/error.rs\n    // If this list ever gets too large, consider implementing a compression strategy like this:\n    // https://gist.github.com/steveluscher/aaa7cbbb5433b1197983908a40860c47\n    'AccountInUse',\n    'AccountLoadedTwice',\n    'AccountNotFound',\n    'ProgramAccountNotFound',\n    'InsufficientFundsForFee',\n    'InvalidAccountForFee',\n    'AlreadyProcessed',\n    'BlockhashNotFound',\n    // `InstructionError` intentionally omitted; delegated to `getSolanaErrorFromInstructionError`\n    'CallChainTooDeep',\n    'MissingSignatureForFee',\n    'InvalidAccountIndex',\n    'SignatureFailure',\n    'InvalidProgramForExecution',\n    'SanitizeFailure',\n    'ClusterMaintenance',\n    'AccountBorrowOutstanding',\n    'WouldExceedMaxBlockCostLimit',\n    'UnsupportedVersion',\n    'InvalidWritableAccount',\n    'WouldExceedMaxAccountCostLimit',\n    'WouldExceedAccountDataBlockLimit',\n    'TooManyAccountLocks',\n    'AddressLookupTableNotFound',\n    'InvalidAddressLookupTableOwner',\n    'InvalidAddressLookupTableData',\n    'InvalidAddressLookupTableIndex',\n    'InvalidRentPayingAccount',\n    'WouldExceedMaxVoteCostLimit',\n    'WouldExceedAccountDataTotalLimit',\n    'DuplicateInstruction',\n    'InsufficientFundsForRent',\n    'MaxLoadedAccountsDataSizeExceeded',\n    'InvalidLoadedAccountsDataSizeLimit',\n    'ResanitizationNeeded',\n    'ProgramExecutionTemporarilyRestricted',\n    'UnbalancedTransaction',\n];\n\nexport function getSolanaErrorFromTransactionError(transactionError: string | { [key: string]: unknown }): SolanaError {\n    if (typeof transactionError === 'object' && 'InstructionError' in transactionError) {\n        return getSolanaErrorFromInstructionError(\n            ...(transactionError.InstructionError as Parameters<typeof getSolanaErrorFromInstructionError>),\n        );\n    }\n    return getSolanaErrorFromRpcError(\n        {\n            errorCodeBaseOffset: 7050001,\n            getErrorContext(errorCode, rpcErrorName, rpcErrorContext) {\n                if (errorCode === SOLANA_ERROR__TRANSACTION_ERROR__UNKNOWN) {\n                    return {\n                        errorName: rpcErrorName,\n                        ...(rpcErrorContext !== undefined ? { transactionErrorContext: rpcErrorContext } : null),\n                    };\n                } else if (errorCode === SOLANA_ERROR__TRANSACTION_ERROR__DUPLICATE_INSTRUCTION) {\n                    return {\n                        index: Number(rpcErrorContext as bigint | number),\n                    };\n                } else if (\n                    errorCode === SOLANA_ERROR__TRANSACTION_ERROR__INSUFFICIENT_FUNDS_FOR_RENT ||\n                    errorCode === SOLANA_ERROR__TRANSACTION_ERROR__PROGRAM_EXECUTION_TEMPORARILY_RESTRICTED\n                ) {\n                    return {\n                        accountIndex: Number((rpcErrorContext as { account_index: bigint | number }).account_index),\n                    };\n                }\n            },\n            orderedErrorNames: ORDERED_ERROR_NAMES,\n            rpcEnumError: transactionError,\n        },\n        getSolanaErrorFromTransactionError,\n    );\n}\n", "import {\n    SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR,\n    SOLANA_ERROR__JSON_RPC__INVALID_PARAMS,\n    SOLANA_ERROR__JSON_RPC__INVALID_REQUEST,\n    SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND,\n    SOLANA_ERROR__JSON_RPC__PARSE_ERROR,\n    SOLANA_ERROR__JSON_RPC__SCAN_ERROR,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE,\n    SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION,\n    SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR,\n    SolanaErrorCode,\n} from './codes';\nimport { SolanaErrorContext } from './context';\nimport { SolanaError } from './error';\nimport { safeCaptureStackTrace } from './stack-trace';\nimport { getSolanaErrorFromTransactionError } from './transaction-error';\n\ninterface RpcErrorResponse {\n    code: bigint | number;\n    data?: unknown;\n    message: string;\n}\n\ntype TransactionError = string | { [key: string]: unknown };\n\n/**\n * Keep in sync with https://github.com/anza-xyz/agave/blob/master/rpc-client-api/src/response.rs\n * @hidden\n */\nexport interface RpcSimulateTransactionResult {\n    accounts:\n        | ({\n              data:\n                  | string // LegacyBinary\n                  | {\n                        // Json\n                        parsed: unknown;\n                        program: string;\n                        space: number;\n                    }\n                  // Binary\n                  | [encodedBytes: string, encoding: 'base58' | 'base64' | 'base64+zstd' | 'binary' | 'jsonParsed'];\n              executable: boolean;\n              lamports: number;\n              owner: string;\n              rentEpoch: number;\n              space?: number;\n          } | null)[]\n        | null;\n    err: TransactionError | null;\n    // Enabled by `enable_cpi_recording`\n    innerInstructions?:\n        | {\n              index: number;\n              instructions: (\n                  | {\n                        // Compiled\n                        accounts: number[];\n                        data: string;\n                        programIdIndex: number;\n                        stackHeight?: number;\n                    }\n                  | {\n                        // Parsed\n                        parsed: unknown;\n                        program: string;\n                        programId: string;\n                        stackHeight?: number;\n                    }\n                  | {\n                        // PartiallyDecoded\n                        accounts: string[];\n                        data: string;\n                        programId: string;\n                        stackHeight?: number;\n                    }\n              )[];\n          }[]\n        | null;\n    logs: string[] | null;\n    returnData: {\n        data: [string, 'base64'];\n        programId: string;\n    } | null;\n    unitsConsumed: number | null;\n}\n\nexport function getSolanaErrorFromJsonRpcError(putativeErrorResponse: unknown): SolanaError {\n    let out: SolanaError;\n    if (isRpcErrorResponse(putativeErrorResponse)) {\n        const { code: rawCode, data, message } = putativeErrorResponse;\n        const code = Number(rawCode);\n        if (code === SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE) {\n            const { err, ...preflightErrorContext } = data as RpcSimulateTransactionResult;\n            const causeObject = err ? { cause: getSolanaErrorFromTransactionError(err) } : null;\n            out = new SolanaError(SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE, {\n                ...preflightErrorContext,\n                ...causeObject,\n            });\n        } else {\n            let errorContext;\n            switch (code) {\n                case SOLANA_ERROR__JSON_RPC__INTERNAL_ERROR:\n                case SOLANA_ERROR__JSON_RPC__INVALID_PARAMS:\n                case SOLANA_ERROR__JSON_RPC__INVALID_REQUEST:\n                case SOLANA_ERROR__JSON_RPC__METHOD_NOT_FOUND:\n                case SOLANA_ERROR__JSON_RPC__PARSE_ERROR:\n                case SOLANA_ERROR__JSON_RPC__SCAN_ERROR:\n                case SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_CLEANED_UP:\n                case SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_NOT_AVAILABLE:\n                case SOLANA_ERROR__JSON_RPC__SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET:\n                case SOLANA_ERROR__JSON_RPC__SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX:\n                case SOLANA_ERROR__JSON_RPC__SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED:\n                case SOLANA_ERROR__JSON_RPC__SERVER_ERROR_SLOT_SKIPPED:\n                case SOLANA_ERROR__JSON_RPC__SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE:\n                case SOLANA_ERROR__JSON_RPC__SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION:\n                    // The server supplies no structured data, but rather a pre-formatted message. Put\n                    // the server message in `context` so as not to completely lose the data. The long\n                    // term fix for this is to add data to the server responses and modify the\n                    // messages in `@solana/errors` to be actual format strings.\n                    errorContext = { __serverMessage: message };\n                    break;\n                default:\n                    if (typeof data === 'object' && !Array.isArray(data)) {\n                        errorContext = data;\n                    }\n            }\n            out = new SolanaError(code as SolanaErrorCode, errorContext as SolanaErrorContext[SolanaErrorCode]);\n        }\n    } else {\n        const message =\n            typeof putativeErrorResponse === 'object' &&\n            putativeErrorResponse !== null &&\n            'message' in putativeErrorResponse &&\n            typeof putativeErrorResponse.message === 'string'\n                ? putativeErrorResponse.message\n                : 'Malformed JSON-RPC error with no message attribute';\n        out = new SolanaError(SOLANA_ERROR__MALFORMED_JSON_RPC_ERROR, { error: putativeErrorResponse, message });\n    }\n    safeCaptureStackTrace(out, getSolanaErrorFromJsonRpcError);\n    return out;\n}\n\nfunction isRpcErrorResponse(value: unknown): value is RpcErrorResponse {\n    return (\n        typeof value === 'object' &&\n        value !== null &&\n        'code' in value &&\n        'message' in value &&\n        (typeof value.code === 'number' || typeof value.code === 'bigint') &&\n        typeof value.message === 'string'\n    );\n}\n"]}