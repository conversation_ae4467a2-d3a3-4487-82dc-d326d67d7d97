{"version": 3, "sources": ["../src/bytes.ts", "../src/codec.ts", "../src/combine-codec.ts", "../src/add-codec-sentinel.ts", "../src/assertions.ts", "../src/add-codec-size-prefix.ts", "../src/fix-codec-size.ts", "../src/offset-codec.ts", "../src/resize-codec.ts", "../src/pad-codec.ts", "../src/reverse-codec.ts", "../src/transform-codec.ts"], "names": ["SolanaError", "SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH", "SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH", "SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH", "SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH", "SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH", "SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL", "SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES", "SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY", "SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH", "SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE", "SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH"], "mappings": ";;;;;;;AAiBa,IAAA,UAAA,GAAa,CAAC,UAAyC,KAAA;AAChE,EAAA,MAAM,kBAAqB,GAAA,UAAA,CAAW,MAAO,CAAA,CAAA,GAAA,KAAO,IAAI,MAAM,CAAA;AAC9D,EAAI,IAAA,kBAAA,CAAmB,WAAW,CAAG,EAAA;AACjC,IAAA,OAAO,WAAW,MAAS,GAAA,UAAA,CAAW,CAAC,CAAA,GAAI,IAAI,UAAW,EAAA;AAAA;AAG9D,EAAI,IAAA,kBAAA,CAAmB,WAAW,CAAG,EAAA;AACjC,IAAA,OAAO,mBAAmB,CAAC,CAAA;AAAA;AAG/B,EAAM,MAAA,WAAA,GAAc,mBAAmB,MAAO,CAAA,CAAC,OAAO,GAAQ,KAAA,KAAA,GAAQ,GAAI,CAAA,MAAA,EAAQ,CAAC,CAAA;AACnF,EAAM,MAAA,MAAA,GAAS,IAAI,UAAA,CAAW,WAAW,CAAA;AACzC,EAAA,IAAI,MAAS,GAAA,CAAA;AACb,EAAA,kBAAA,CAAmB,QAAQ,CAAO,GAAA,KAAA;AAC9B,IAAO,MAAA,CAAA,GAAA,CAAI,KAAK,MAAM,CAAA;AACtB,IAAA,MAAA,IAAU,GAAI,CAAA,MAAA;AAAA,GACjB,CAAA;AACD,EAAO,OAAA,MAAA;AACX;AAyBa,IAAA,QAAA,GAAW,CAAC,KAAA,EAAwC,MAAoD,KAAA;AACjH,EAAI,IAAA,KAAA,CAAM,MAAU,IAAA,MAAA,EAAe,OAAA,KAAA;AACnC,EAAA,MAAM,cAAc,IAAI,UAAA,CAAW,MAAM,CAAA,CAAE,KAAK,CAAC,CAAA;AACjD,EAAA,WAAA,CAAY,IAAI,KAAK,CAAA;AACrB,EAAO,OAAA,WAAA;AACX;AAkCO,IAAM,QAAW,GAAA,CAAC,KAAwC,EAAA,MAAA,KAC7D,SAAS,KAAM,CAAA,MAAA,IAAU,MAAS,GAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,CAAG,EAAA,MAAM,GAAG,MAAM;AAkBrE,SAAS,aAAA,CACZ,IACA,EAAA,KAAA,EACA,MACO,EAAA;AACP,EAAA,MAAM,KAAQ,GAAA,MAAA,KAAW,CAAK,IAAA,IAAA,CAAK,MAAW,KAAA,KAAA,CAAM,MAAS,GAAA,IAAA,GAAO,IAAK,CAAA,KAAA,CAAM,MAAQ,EAAA,MAAA,GAAS,MAAM,MAAM,CAAA;AAC5G,EAAA,IAAI,KAAM,CAAA,MAAA,KAAW,KAAM,CAAA,MAAA,EAAe,OAAA,KAAA;AAC1C,EAAO,OAAA,KAAA,CAAM,MAAM,CAAC,CAAA,EAAG,MAAM,CAAM,KAAA,KAAA,CAAM,CAAC,CAAC,CAAA;AAC/C;ACyQO,SAAS,cAAA,CACZ,OACA,OACM,EAAA;AACN,EAAA,OAAO,eAAe,OAAU,GAAA,OAAA,CAAQ,SAAY,GAAA,OAAA,CAAQ,iBAAiB,KAAK,CAAA;AACtF;AA6FO,SAAS,cACZ,OACc,EAAA;AACd,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,QAAQ,CAAS,KAAA,KAAA;AACb,MAAA,MAAM,QAAQ,IAAI,UAAA,CAAW,cAAe,CAAA,KAAA,EAAO,OAAO,CAAC,CAAA;AAC3D,MAAQ,OAAA,CAAA,KAAA,CAAM,KAAO,EAAA,KAAA,EAAO,CAAC,CAAA;AAC7B,MAAO,OAAA,KAAA;AAAA;AACX,GACH,CAAA;AACL;AA4FO,SAAS,cACZ,OACY,EAAA;AACZ,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,MAAA,EAAQ,CAAC,KAAA,EAAO,MAAS,GAAA,CAAA,KAAM,QAAQ,IAAK,CAAA,KAAA,EAAO,MAAM,CAAA,CAAE,CAAC;AAAA,GAC/D,CAAA;AACL;AAsHO,SAAS,YACZ,KAGiB,EAAA;AACjB,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,GAAG,KAAA;AAAA,IACH,MAAA,EAAQ,CAAC,KAAA,EAAO,MAAS,GAAA,CAAA,KAAM,MAAM,IAAK,CAAA,KAAA,EAAO,MAAM,CAAA,CAAE,CAAC,CAAA;AAAA,IAC1D,QAAQ,CAAS,KAAA,KAAA;AACb,MAAA,MAAM,QAAQ,IAAI,UAAA,CAAW,cAAe,CAAA,KAAA,EAAO,KAAK,CAAC,CAAA;AACzD,MAAM,KAAA,CAAA,KAAA,CAAM,KAAO,EAAA,KAAA,EAAO,CAAC,CAAA;AAC3B,MAAO,OAAA,KAAA;AAAA;AACX,GACH,CAAA;AACL;AAgDO,SAAS,YAAY,KAAqF,EAAA;AAC7G,EAAA,OAAO,WAAe,IAAA,KAAA,IAAS,OAAO,KAAA,CAAM,SAAc,KAAA,QAAA;AAC9D;AA6CO,SAAS,kBACZ,KACsC,EAAA;AACtC,EAAI,IAAA,CAAC,WAAY,CAAA,KAAK,CAAG,EAAA;AACrB,IAAM,MAAA,IAAIA,mBAAYC,kDAA2C,CAAA;AAAA;AAEzE;AAwCO,SAAS,eAAe,KAAoF,EAAA;AAC/G,EAAO,OAAA,CAAC,YAAY,KAAK,CAAA;AAC7B;AA4CO,SAAS,qBACZ,KACqC,EAAA;AACrC,EAAI,IAAA,CAAC,cAAe,CAAA,KAAK,CAAG,EAAA;AACxB,IAAM,MAAA,IAAID,mBAAYE,qDAA8C,CAAA;AAAA;AAE5E;ACtzBO,SAAS,YAAA,CACZ,SACA,OACiB,EAAA;AACjB,EAAA,IAAI,WAAY,CAAA,OAAO,CAAM,KAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AAC/C,IAAM,MAAA,IAAIF,mBAAYG,wEAAiE,CAAA;AAAA;AAG3F,EAAI,IAAA,WAAA,CAAY,OAAO,CAAK,IAAA,WAAA,CAAY,OAAO,CAAK,IAAA,OAAA,CAAQ,SAAc,KAAA,OAAA,CAAQ,SAAW,EAAA;AACzF,IAAM,MAAA,IAAIH,mBAAYI,gEAA2D,EAAA;AAAA,MAC7E,kBAAkB,OAAQ,CAAA,SAAA;AAAA,MAC1B,kBAAkB,OAAQ,CAAA;AAAA,KAC7B,CAAA;AAAA;AAGL,EAAI,IAAA,CAAC,WAAY,CAAA,OAAO,CAAK,IAAA,CAAC,WAAY,CAAA,OAAO,CAAK,IAAA,OAAA,CAAQ,OAAY,KAAA,OAAA,CAAQ,OAAS,EAAA;AACvF,IAAM,MAAA,IAAIJ,mBAAYK,8DAAyD,EAAA;AAAA,MAC3E,gBAAgB,OAAQ,CAAA,OAAA;AAAA,MACxB,gBAAgB,OAAQ,CAAA;AAAA,KAC3B,CAAA;AAAA;AAGL,EAAO,OAAA;AAAA,IACH,GAAG,OAAA;AAAA,IACH,GAAG,OAAA;AAAA,IACH,QAAQ,OAAQ,CAAA,MAAA;AAAA,IAChB,QAAQ,OAAQ,CAAA,MAAA;AAAA,IAChB,MAAM,OAAQ,CAAA,IAAA;AAAA,IACd,OAAO,OAAQ,CAAA;AAAA,GACnB;AACJ;;;AC1FO,SAAS,kBAAA,CAA0B,SAAyB,QAA8C,EAAA;AAC7G,EAAA,MAAM,KAAS,GAAA,CAAC,KAAO,EAAA,KAAA,EAAO,MAAW,KAAA;AAIrC,IAAM,MAAA,YAAA,GAAe,OAAQ,CAAA,MAAA,CAAO,KAAK,CAAA;AACzC,IAAA,IAAI,iBAAkB,CAAA,YAAA,EAAc,QAAQ,CAAA,IAAK,CAAG,EAAA;AAChD,MAAM,MAAA,IAAIL,mBAAYM,oEAA+D,EAAA;AAAA,QACjF,YAAc,EAAA,YAAA;AAAA,QACd,eAAA,EAAiB,SAAS,YAAY,CAAA;AAAA,QACtC,WAAA,EAAa,SAAS,QAAQ,CAAA;AAAA,QAC9B;AAAA,OACH,CAAA;AAAA;AAEL,IAAM,KAAA,CAAA,GAAA,CAAI,cAAc,MAAM,CAAA;AAC9B,IAAA,MAAA,IAAU,YAAa,CAAA,MAAA;AACvB,IAAM,KAAA,CAAA,GAAA,CAAI,UAAU,MAAM,CAAA;AAC1B,IAAA,MAAA,IAAU,QAAS,CAAA,MAAA;AACnB,IAAO,OAAA,MAAA;AAAA,GACX;AAEA,EAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,IAAO,OAAA,aAAA,CAAc,EAAE,GAAG,OAAS,EAAA,SAAA,EAAW,QAAQ,SAAY,GAAA,QAAA,CAAS,MAAQ,EAAA,KAAA,EAAO,CAAA;AAAA;AAG9F,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,GAAI,OAAQ,CAAA,OAAA,IAAW,IAAO,GAAA,EAAE,OAAS,EAAA,OAAA,CAAQ,OAAU,GAAA,QAAA,CAAS,MAAO,EAAA,GAAI,EAAC;AAAA,IAChF,kBAAkB,CAAS,KAAA,KAAA,OAAA,CAAQ,gBAAiB,CAAA,KAAK,IAAI,QAAS,CAAA,MAAA;AAAA,IACtE;AAAA,GACH,CAAA;AACL;AAiBO,SAAS,kBAAA,CAAwB,SAAuB,QAA4C,EAAA;AACvG,EAAM,MAAA,IAAA,GAAQ,CAAC,KAAA,EAAO,MAAW,KAAA;AAC7B,IAAA,MAAM,iBAAiB,MAAW,KAAA,CAAA,GAAI,KAAQ,GAAA,KAAA,CAAM,MAAM,MAAM,CAAA;AAChE,IAAM,MAAA,aAAA,GAAgB,iBAAkB,CAAA,cAAA,EAAgB,QAAQ,CAAA;AAChE,IAAA,IAAI,kBAAkB,EAAI,EAAA;AACtB,MAAM,MAAA,IAAIN,mBAAYO,8DAAyD,EAAA;AAAA,QAC3E,YAAc,EAAA,cAAA;AAAA,QACd,eAAA,EAAiB,SAAS,cAAc,CAAA;AAAA,QACxC,WAAA,EAAa,SAAS,QAAQ,CAAA;AAAA,QAC9B;AAAA,OACH,CAAA;AAAA;AAEL,IAAA,MAAM,gBAAmB,GAAA,cAAA,CAAe,KAAM,CAAA,CAAA,EAAG,aAAa,CAAA;AAI9D,IAAO,OAAA,CAAC,QAAQ,MAAO,CAAA,gBAAgB,GAAG,MAAS,GAAA,gBAAA,CAAiB,MAAS,GAAA,QAAA,CAAS,MAAM,CAAA;AAAA,GAChG;AAEA,EAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,IAAO,OAAA,aAAA,CAAc,EAAE,GAAG,OAAS,EAAA,SAAA,EAAW,QAAQ,SAAY,GAAA,QAAA,CAAS,MAAQ,EAAA,IAAA,EAAM,CAAA;AAAA;AAG7F,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,GAAI,OAAQ,CAAA,OAAA,IAAW,IAAO,GAAA,EAAE,OAAS,EAAA,OAAA,CAAQ,OAAU,GAAA,QAAA,CAAS,MAAO,EAAA,GAAI,EAAC;AAAA,IAChF;AAAA,GACH,CAAA;AACL;AAmDO,SAAS,gBAAA,CACZ,OACA,QACiB,EAAA;AACjB,EAAO,OAAA,YAAA,CAAa,mBAAmB,KAAO,EAAA,QAAQ,GAAG,kBAAmB,CAAA,KAAA,EAAO,QAAQ,CAAC,CAAA;AAChG;AAEA,SAAS,iBAAA,CAAkB,OAA2B,QAA8B,EAAA;AAChF,EAAA,OAAO,KAAM,CAAA,SAAA,CAAU,CAAC,IAAA,EAAM,OAAO,GAAQ,KAAA;AACzC,IAAA,IAAI,SAAS,MAAW,KAAA,CAAA,EAAU,OAAA,IAAA,KAAS,SAAS,CAAC,CAAA;AACrD,IAAO,OAAA,aAAA,CAAc,GAAK,EAAA,QAAA,EAAU,KAAK,CAAA;AAAA,GAC5C,CAAA;AACL;AAEA,SAAS,SAAS,KAAmC,EAAA;AACjD,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,SAAS,GAAM,GAAA,IAAA,CAAK,QAAS,CAAA,EAAE,CAAE,CAAA,QAAA,CAAS,CAAG,EAAA,GAAG,GAAG,EAAE,CAAA;AACnF;AC9JO,SAAS,iCACZ,CAAA,gBAAA,EACA,KACA,EAAA,MAAA,GAAS,CACX,EAAA;AACE,EAAI,IAAA,KAAA,CAAM,MAAS,GAAA,MAAA,IAAU,CAAG,EAAA;AAC5B,IAAM,MAAA,IAAIP,mBAAYQ,2DAAsD,EAAA;AAAA,MACxE;AAAA,KACH,CAAA;AAAA;AAET;AAuBO,SAAS,qCACZ,CAAA,gBAAA,EACA,QACA,EAAA,KAAA,EACA,SAAS,CACX,EAAA;AACE,EAAM,MAAA,WAAA,GAAc,MAAM,MAAS,GAAA,MAAA;AACnC,EAAA,IAAI,cAAc,QAAU,EAAA;AACxB,IAAM,MAAA,IAAIR,mBAAYS,gDAA2C,EAAA;AAAA,MAC7D,WAAA;AAAA,MACA,gBAAA;AAAA,MACA;AAAA,KACH,CAAA;AAAA;AAET;AAoBO,SAAS,oCAAA,CAAqC,gBAA0B,EAAA,MAAA,EAAgB,WAAqB,EAAA;AAChH,EAAI,IAAA,MAAA,GAAS,CAAK,IAAA,MAAA,GAAS,WAAa,EAAA;AACpC,IAAM,MAAA,IAAIT,mBAAYU,gDAA2C,EAAA;AAAA,MAC7D,WAAA;AAAA,MACA,gBAAA;AAAA,MACA;AAAA,KACH,CAAA;AAAA;AAET;;;ACzDO,SAAS,oBAAA,CAA4B,SAAyB,MAAuC,EAAA;AACxG,EAAA,MAAM,KAAS,GAAA,CAAC,KAAO,EAAA,KAAA,EAAO,MAAW,KAAA;AAGrC,IAAM,MAAA,YAAA,GAAe,OAAQ,CAAA,MAAA,CAAO,KAAK,CAAA;AACzC,IAAA,MAAA,GAAS,MAAO,CAAA,KAAA,CAAM,YAAa,CAAA,MAAA,EAAQ,OAAO,MAAM,CAAA;AACxD,IAAM,KAAA,CAAA,GAAA,CAAI,cAAc,MAAM,CAAA;AAC9B,IAAA,OAAO,SAAS,YAAa,CAAA,MAAA;AAAA,GACjC;AAEA,EAAA,IAAI,WAAY,CAAA,MAAM,CAAK,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AAC7C,IAAO,OAAA,aAAA,CAAc,EAAE,GAAG,OAAS,EAAA,SAAA,EAAW,OAAO,SAAY,GAAA,OAAA,CAAQ,SAAW,EAAA,KAAA,EAAO,CAAA;AAAA;AAG/F,EAAA,MAAM,gBAAgB,WAAY,CAAA,MAAM,IAAI,MAAO,CAAA,SAAA,GAAa,OAAO,OAAW,IAAA,IAAA;AAClF,EAAA,MAAM,iBAAiB,WAAY,CAAA,OAAO,IAAI,OAAQ,CAAA,SAAA,GAAa,QAAQ,OAAW,IAAA,IAAA;AACtF,EAAA,MAAM,UAAU,aAAkB,KAAA,IAAA,IAAQ,cAAmB,KAAA,IAAA,GAAO,gBAAgB,cAAiB,GAAA,IAAA;AAErG,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,GAAI,OAAY,KAAA,IAAA,GAAO,EAAE,OAAA,KAAY,EAAC;AAAA,IACtC,kBAAkB,CAAS,KAAA,KAAA;AACvB,MAAM,MAAA,WAAA,GAAc,cAAe,CAAA,KAAA,EAAO,OAAO,CAAA;AACjD,MAAO,OAAA,cAAA,CAAe,WAAa,EAAA,MAAM,CAAI,GAAA,WAAA;AAAA,KACjD;AAAA,IACA;AAAA,GACH,CAAA;AACL;AAgBO,SAAS,oBAAA,CAA0B,SAAuB,MAAqC,EAAA;AAClG,EAAM,MAAA,IAAA,GAAQ,CAAC,KAAA,EAAO,MAAW,KAAA;AAC7B,IAAA,MAAM,CAAC,UAAY,EAAA,aAAa,IAAI,MAAO,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA;AAC7D,IAAM,MAAA,IAAA,GAAO,OAAO,UAAU,CAAA;AAC9B,IAAS,MAAA,GAAA,aAAA;AAET,IAAA,IAAI,MAAS,GAAA,CAAA,IAAK,KAAM,CAAA,MAAA,GAAS,IAAM,EAAA;AACnC,MAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,MAAQ,EAAA,MAAA,GAAS,IAAI,CAAA;AAAA;AAE7C,IAAsC,qCAAA,CAAA,sBAAA,EAAwB,MAAM,KAAK,CAAA;AAGzE,IAAA,OAAO,CAAC,OAAQ,CAAA,MAAA,CAAO,KAAK,CAAA,EAAG,SAAS,IAAI,CAAA;AAAA,GAChD;AAEA,EAAA,IAAI,WAAY,CAAA,MAAM,CAAK,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AAC7C,IAAO,OAAA,aAAA,CAAc,EAAE,GAAG,OAAS,EAAA,SAAA,EAAW,OAAO,SAAY,GAAA,OAAA,CAAQ,SAAW,EAAA,IAAA,EAAM,CAAA;AAAA;AAG9F,EAAA,MAAM,gBAAgB,WAAY,CAAA,MAAM,IAAI,MAAO,CAAA,SAAA,GAAa,OAAO,OAAW,IAAA,IAAA;AAClF,EAAA,MAAM,iBAAiB,WAAY,CAAA,OAAO,IAAI,OAAQ,CAAA,SAAA,GAAa,QAAQ,OAAW,IAAA,IAAA;AACtF,EAAA,MAAM,UAAU,aAAkB,KAAA,IAAA,IAAQ,cAAmB,KAAA,IAAA,GAAO,gBAAgB,cAAiB,GAAA,IAAA;AACrG,EAAA,OAAO,aAAc,CAAA,EAAE,GAAG,OAAA,EAAS,GAAI,OAAA,KAAY,IAAO,GAAA,EAAE,OAAQ,EAAA,GAAI,EAAC,EAAI,MAAM,CAAA;AACvF;AA4CO,SAAS,kBAAA,CACZ,OACA,MACiB,EAAA;AACjB,EAAO,OAAA,YAAA,CAAa,qBAAqB,KAAO,EAAA,MAAM,GAAG,oBAAqB,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA;AAChG;;;AClHO,SAAS,cAAA,CACZ,SACA,UAC8B,EAAA;AAC9B,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,UAAA;AAAA,IACX,KAAO,EAAA,CAAC,KAAc,EAAA,KAAA,EAAmB,MAAmB,KAAA;AAIxD,MAAM,MAAA,iBAAA,GAAoB,OAAQ,CAAA,MAAA,CAAO,KAAK,CAAA;AAC9C,MAAM,MAAA,cAAA,GACF,kBAAkB,MAAS,GAAA,UAAA,GAAa,kBAAkB,KAAM,CAAA,CAAA,EAAG,UAAU,CAAI,GAAA,iBAAA;AACrF,MAAM,KAAA,CAAA,GAAA,CAAI,gBAAgB,MAAM,CAAA;AAChC,MAAA,OAAO,MAAS,GAAA,UAAA;AAAA;AACpB,GACH,CAAA;AACL;AA+BO,SAAS,cAAA,CACZ,SACA,UAC4B,EAAA;AAC5B,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,UAAA;AAAA,IACX,IAAA,EAAM,CAAC,KAAA,EAAO,MAAW,KAAA;AACrB,MAAsC,qCAAA,CAAA,cAAA,EAAgB,UAAY,EAAA,KAAA,EAAO,MAAM,CAAA;AAE/E,MAAA,IAAI,MAAS,GAAA,CAAA,IAAK,KAAM,CAAA,MAAA,GAAS,UAAY,EAAA;AACzC,QAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,MAAQ,EAAA,MAAA,GAAS,UAAU,CAAA;AAAA;AAGnD,MAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,QAAQ,KAAA,GAAA,QAAA,CAAS,KAAO,EAAA,OAAA,CAAQ,SAAS,CAAA;AAAA;AAG7C,MAAA,MAAM,CAAC,KAAK,CAAA,GAAI,OAAQ,CAAA,IAAA,CAAK,OAAO,CAAC,CAAA;AACrC,MAAO,OAAA,CAAC,KAAO,EAAA,MAAA,GAAS,UAAU,CAAA;AAAA;AACtC,GACH,CAAA;AACL;AAiDO,SAAS,YAAA,CACZ,OACA,UACiC,EAAA;AACjC,EAAO,OAAA,YAAA,CAAa,eAAe,KAAO,EAAA,UAAU,GAAG,cAAe,CAAA,KAAA,EAAO,UAAU,CAAC,CAAA;AAC5F;;;AC+CO,SAAS,aAAA,CAA2C,SAAmB,MAAgC,EAAA;AAC1G,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,KAAO,EAAA,CAAC,KAAO,EAAA,KAAA,EAAO,SAAc,KAAA;AAChC,MAAA,MAAM,YAAY,CAAC,MAAA,KAAmB,MAAO,CAAA,MAAA,EAAQ,MAAM,MAAM,CAAA;AACjE,MAAM,MAAA,YAAA,GAAe,MAAO,CAAA,SAAA,GAAY,MAAO,CAAA,SAAA,CAAU,EAAE,KAAO,EAAA,SAAA,EAAW,SAAU,EAAC,CAAI,GAAA,SAAA;AAC5F,MAAqC,oCAAA,CAAA,eAAA,EAAiB,YAAc,EAAA,KAAA,CAAM,MAAM,CAAA;AAChF,MAAA,MAAM,UAAa,GAAA,OAAA,CAAQ,KAAM,CAAA,KAAA,EAAO,OAAO,YAAY,CAAA;AAC3D,MAAA,MAAM,aAAgB,GAAA,MAAA,CAAO,UACvB,GAAA,MAAA,CAAO,UAAW,CAAA,EAAE,KAAO,EAAA,YAAA,EAAc,UAAY,EAAA,SAAA,EAAW,SAAU,EAAC,CAC3E,GAAA,UAAA;AACN,MAAqC,oCAAA,CAAA,eAAA,EAAiB,aAAe,EAAA,KAAA,CAAM,MAAM,CAAA;AACjF,MAAO,OAAA,aAAA;AAAA;AACX,GACH,CAAA;AACL;AAwDO,SAAS,aAAA,CAA2C,SAAmB,MAAgC,EAAA;AAC1G,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,IAAA,EAAM,CAAC,KAAA,EAAO,SAAc,KAAA;AACxB,MAAA,MAAM,YAAY,CAAC,MAAA,KAAmB,MAAO,CAAA,MAAA,EAAQ,MAAM,MAAM,CAAA;AACjE,MAAM,MAAA,YAAA,GAAe,MAAO,CAAA,SAAA,GAAY,MAAO,CAAA,SAAA,CAAU,EAAE,KAAO,EAAA,SAAA,EAAW,SAAU,EAAC,CAAI,GAAA,SAAA;AAC5F,MAAqC,oCAAA,CAAA,eAAA,EAAiB,YAAc,EAAA,KAAA,CAAM,MAAM,CAAA;AAChF,MAAA,MAAM,CAAC,KAAO,EAAA,UAAU,IAAI,OAAQ,CAAA,IAAA,CAAK,OAAO,YAAY,CAAA;AAC5D,MAAA,MAAM,aAAgB,GAAA,MAAA,CAAO,UACvB,GAAA,MAAA,CAAO,UAAW,CAAA,EAAE,KAAO,EAAA,YAAA,EAAc,UAAY,EAAA,SAAA,EAAW,SAAU,EAAC,CAC3E,GAAA,UAAA;AACN,MAAqC,oCAAA,CAAA,eAAA,EAAiB,aAAe,EAAA,KAAA,CAAM,MAAM,CAAA;AACjF,MAAO,OAAA,CAAC,OAAO,aAAa,CAAA;AAAA;AAChC,GACH,CAAA;AACL;AAoEO,SAAS,WAAA,CAAqC,OAAe,MAA8B,EAAA;AAC9F,EAAO,OAAA,YAAA,CAAa,cAAc,KAAO,EAAA,MAAM,GAAG,aAAc,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA;AAClF;AAGA,SAAS,MAAA,CAAO,UAAkB,OAAiB,EAAA;AAC/C,EAAI,IAAA,OAAA,KAAY,GAAU,OAAA,CAAA;AAC1B,EAAS,OAAA,CAAA,QAAA,GAAW,UAAW,OAAW,IAAA,OAAA;AAC9C;ACxTO,SAAS,aAAA,CACZ,SACA,MACQ,EAAA;AACR,EAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,IAAM,MAAA,SAAA,GAAY,MAAO,CAAA,OAAA,CAAQ,SAAS,CAAA;AAC1C,IAAA,IAAI,YAAY,CAAG,EAAA;AACf,MAAM,MAAA,IAAIV,mBAAYW,0DAAqD,EAAA;AAAA,QACvE,WAAa,EAAA,SAAA;AAAA,QACb,gBAAkB,EAAA;AAAA,OACrB,CAAA;AAAA;AAEL,IAAA,OAAO,aAAc,CAAA,EAAE,GAAG,OAAA,EAAS,WAAW,CAAA;AAAA;AAElD,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,kBAAkB,CAAS,KAAA,KAAA;AACvB,MAAA,MAAM,OAAU,GAAA,MAAA,CAAO,OAAQ,CAAA,gBAAA,CAAiB,KAAK,CAAC,CAAA;AACtD,MAAA,IAAI,UAAU,CAAG,EAAA;AACb,QAAM,MAAA,IAAIX,mBAAYW,0DAAqD,EAAA;AAAA,UACvE,WAAa,EAAA,OAAA;AAAA,UACb,gBAAkB,EAAA;AAAA,SACrB,CAAA;AAAA;AAEL,MAAO,OAAA,OAAA;AAAA;AACX,GACH,CAAA;AACL;AA8CO,SAAS,aAAA,CACZ,SACA,MACQ,EAAA;AACR,EAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,IAAM,MAAA,SAAA,GAAY,MAAO,CAAA,OAAA,CAAQ,SAAS,CAAA;AAC1C,IAAA,IAAI,YAAY,CAAG,EAAA;AACf,MAAM,MAAA,IAAIX,mBAAYW,0DAAqD,EAAA;AAAA,QACvE,WAAa,EAAA,SAAA;AAAA,QACb,gBAAkB,EAAA;AAAA,OACrB,CAAA;AAAA;AAEL,IAAA,OAAO,aAAc,CAAA,EAAE,GAAG,OAAA,EAAS,WAAW,CAAA;AAAA;AAElD,EAAO,OAAA,OAAA;AACX;AAoDO,SAAS,WAAA,CAAqC,OAAe,MAA0C,EAAA;AAC1G,EAAO,OAAA,YAAA,CAAa,cAAc,KAAO,EAAA,MAAM,GAAG,aAAc,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA;AAClF;;;AC/KO,SAAS,cAAA,CAA4C,SAAmB,MAA0B,EAAA;AACrG,EAAO,OAAA,aAAA;AAAA,IACH,aAAc,CAAA,OAAA,EAAS,CAAQ,IAAA,KAAA,IAAA,GAAO,MAAM,CAAA;AAAA,IAC5C,EAAE,SAAW,EAAA,CAAC,EAAE,SAAU,EAAA,KAAM,YAAY,MAAO;AAAA,GACvD;AACJ;AAuBO,SAAS,eAAA,CAA6C,SAAmB,MAA0B,EAAA;AACtG,EAAO,OAAA,aAAA;AAAA,IACH,aAAc,CAAA,OAAA,EAAS,CAAQ,IAAA,KAAA,IAAA,GAAO,MAAM,CAAA;AAAA,IAC5C,EAAE,UAAY,EAAA,CAAC,EAAE,UAAW,EAAA,KAAM,aAAa,MAAO;AAAA,GAC1D;AACJ;AAuBO,SAAS,cAAA,CAA4C,SAAmB,MAA0B,EAAA;AACrG,EAAO,OAAA,aAAA;AAAA,IACH,aAAc,CAAA,OAAA,EAAS,CAAQ,IAAA,KAAA,IAAA,GAAO,MAAM,CAAA;AAAA,IAC5C,EAAE,SAAW,EAAA,CAAC,EAAE,SAAU,EAAA,KAAM,YAAY,MAAO;AAAA,GACvD;AACJ;AAuBO,SAAS,eAAA,CAA6C,SAAmB,MAA0B,EAAA;AACtG,EAAO,OAAA,aAAA;AAAA,IACH,aAAc,CAAA,OAAA,EAAS,CAAQ,IAAA,KAAA,IAAA,GAAO,MAAM,CAAA;AAAA,IAC5C,EAAE,UAAY,EAAA,CAAC,EAAE,UAAW,EAAA,KAAM,aAAa,MAAO;AAAA,GAC1D;AACJ;AAmCO,SAAS,YAAA,CAAsC,OAAe,MAAwB,EAAA;AACzF,EAAO,OAAA,YAAA,CAAa,eAAe,KAAO,EAAA,MAAM,GAAG,cAAe,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA;AACpF;AAmCO,SAAS,aAAA,CAAuC,OAAe,MAAwB,EAAA;AAC1F,EAAO,OAAA,YAAA,CAAa,gBAAgB,KAAO,EAAA,MAAM,GAAG,eAAgB,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA;AACtF;;;ACzLA,SAAS,4BACL,MACA,EAAA,kBAAA,EACA,YACA,EAAA,YAAA,EACA,eAAuB,CACzB,EAAA;AACE,EAAO,OAAA,YAAA,GAAe,EAAE,YAAc,EAAA;AAClC,IAAM,MAAA,SAAA,GAAY,OAAO,YAAY,CAAA;AACrC,IAAA,kBAAA,CAAmB,YAAe,GAAA,YAAY,CAAI,GAAA,MAAA,CAAO,YAAY,CAAA;AACrE,IAAmB,kBAAA,CAAA,YAAA,GAAe,YAAY,CAAI,GAAA,SAAA;AAClD,IAAA,YAAA,EAAA;AAAA;AAEJ,EAAA,IAAI,iBAAiB,YAAc,EAAA;AAC/B,IAAA,kBAAA,CAAmB,YAAe,GAAA,YAAY,CAAI,GAAA,MAAA,CAAO,YAAY,CAAA;AAAA;AAE7E;AA4BO,SAAS,eACZ,OAC8B,EAAA;AAC9B,EAAA,iBAAA,CAAkB,OAAO,CAAA;AACzB,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,KAAO,EAAA,CAAC,KAAc,EAAA,KAAA,EAAO,MAAW,KAAA;AACpC,MAAA,MAAM,SAAY,GAAA,OAAA,CAAQ,KAAM,CAAA,KAAA,EAAO,OAAO,MAAM,CAAA;AACpD,MAAA,2BAAA;AAAA,QACI,KAAA;AAAA,QACA,KAAA;AAAA,QACA,MAAA;AAAA,QACA,SAAS,OAAQ,CAAA;AAAA,OACrB;AACA,MAAO,OAAA,SAAA;AAAA;AACX,GACH,CAAA;AACL;AA4BO,SAAS,eACZ,OAC4B,EAAA;AAC5B,EAAA,iBAAA,CAAkB,OAAO,CAAA;AACzB,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,IAAA,EAAM,CAAC,KAAA,EAAO,MAAW,KAAA;AACrB,MAAM,MAAA,aAAA,GAAgB,MAAM,KAAM,EAAA;AAClC,MAAA,2BAAA;AAAA,QACI,KAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,SAAS,OAAQ,CAAA;AAAA,OACrB;AACA,MAAO,OAAA,OAAA,CAAQ,IAAK,CAAA,aAAA,EAAe,MAAM,CAAA;AAAA;AAC7C,GACH,CAAA;AACL;AAqCO,SAAS,aACZ,KACiC,EAAA;AACjC,EAAA,OAAO,aAAa,cAAe,CAAA,KAAK,CAAG,EAAA,cAAA,CAAe,KAAK,CAAC,CAAA;AACpE;;;ACtGO,SAAS,gBAAA,CACZ,SACA,KACiB,EAAA;AACjB,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAI,cAAe,CAAA,OAAO,CACpB,GAAA,EAAE,GAAG,OAAS,EAAA,gBAAA,EAAkB,CAAC,KAAA,KAAoB,QAAQ,gBAAiB,CAAA,KAAA,CAAM,KAAK,CAAC,GAC1F,GAAA,OAAA;AAAA,IACN,KAAA,EAAO,CAAC,KAAA,EAAiB,KAAO,EAAA,MAAA,KAAW,OAAQ,CAAA,KAAA,CAAM,KAAM,CAAA,KAAK,CAAG,EAAA,KAAA,EAAO,MAAM;AAAA,GACvF,CAAA;AACL;AAyCO,SAAS,gBAAA,CACZ,SACA,GACe,EAAA;AACf,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AACtD,MAAA,MAAM,CAAC,KAAO,EAAA,SAAS,IAAI,OAAQ,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA;AACrD,MAAA,OAAO,CAAC,GAAI,CAAA,KAAA,EAAO,KAAO,EAAA,MAAM,GAAG,SAAS,CAAA;AAAA;AAChD,GACH,CAAA;AACL;AAgFO,SAAS,cAAA,CACZ,KACA,EAAA,KAAA,EACA,GACuB,EAAA;AACvB,EAAA,OAAO,WAAY,CAAA;AAAA,IACf,GAAG,gBAAiB,CAAA,KAAA,EAAO,KAAK,CAAA;AAAA,IAChC,MAAM,GAAM,GAAA,gBAAA,CAAiB,OAAO,GAAG,CAAA,CAAE,OAAQ,KAAM,CAAA;AAAA,GAC1D,CAAA;AACL", "file": "index.node.cjs", "sourcesContent": ["import { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Concatenates an array of `Uint8Array`s into a single `Uint8Array`.\n * Reuses the original byte array when applicable.\n *\n * @param byteArrays - The array of byte arrays to concatenate.\n *\n * @example\n * ```ts\n * const bytes1 = new Uint8Array([0x01, 0x02]);\n * const bytes2 = new Uint8Array([]);\n * const bytes3 = new Uint8Array([0x03, 0x04]);\n * const bytes = mergeBytes([bytes1, bytes2, bytes3]);\n * //    ^ [0x01, 0x02, 0x03, 0x04]\n * ```\n */\nexport const mergeBytes = (byteArrays: Uint8Array[]): Uint8Array => {\n    const nonEmptyByteArrays = byteArrays.filter(arr => arr.length);\n    if (nonEmptyByteArrays.length === 0) {\n        return byteArrays.length ? byteArrays[0] : new Uint8Array();\n    }\n\n    if (nonEmptyByteArrays.length === 1) {\n        return nonEmptyByteArrays[0];\n    }\n\n    const totalLength = nonEmptyByteArrays.reduce((total, arr) => total + arr.length, 0);\n    const result = new Uint8Array(totalLength);\n    let offset = 0;\n    nonEmptyByteArrays.forEach(arr => {\n        result.set(arr, offset);\n        offset += arr.length;\n    });\n    return result;\n};\n\n/**\n * Pads a `Uint8Array` with zeroes to the specified length.\n * If the array is longer than the specified length, it is returned as-is.\n *\n * @param bytes - The byte array to pad.\n * @param length - The desired length of the byte array.\n *\n * @example\n * Adds zeroes to the end of the byte array to reach the desired length.\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02]);\n * const paddedBytes = padBytes(bytes, 4);\n * //    ^ [0x01, 0x02, 0x00, 0x00]\n * ```\n *\n * @example\n * Returns the original byte array if it is already at the desired length.\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02]);\n * const paddedBytes = padBytes(bytes, 2);\n * // bytes === paddedBytes\n * ```\n */\nexport const padBytes = (bytes: ReadonlyUint8Array | Uint8Array, length: number): ReadonlyUint8Array | Uint8Array => {\n    if (bytes.length >= length) return bytes;\n    const paddedBytes = new Uint8Array(length).fill(0);\n    paddedBytes.set(bytes);\n    return paddedBytes;\n};\n\n/**\n * Fixes a `Uint8Array` to the specified length.\n * If the array is longer than the specified length, it is truncated.\n * If the array is shorter than the specified length, it is padded with zeroes.\n *\n * @param bytes - The byte array to truncate or pad.\n * @param length - The desired length of the byte array.\n *\n * @example\n * Truncates the byte array to the desired length.\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02, 0x03, 0x04]);\n * const fixedBytes = fixBytes(bytes, 2);\n * //    ^ [0x01, 0x02]\n * ```\n *\n * @example\n * Adds zeroes to the end of the byte array to reach the desired length.\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02]);\n * const fixedBytes = fixBytes(bytes, 4);\n * //    ^ [0x01, 0x02, 0x00, 0x00]\n * ```\n *\n * @example\n * Returns the original byte array if it is already at the desired length.\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02]);\n * const fixedBytes = fixBytes(bytes, 2);\n * // bytes === fixedBytes\n * ```\n */\nexport const fixBytes = (bytes: ReadonlyUint8Array | Uint8Array, length: number): ReadonlyUint8Array | Uint8Array =>\n    padBytes(bytes.length <= length ? bytes : bytes.slice(0, length), length);\n\n/**\n * Returns true if and only if the provided `data` byte array contains\n * the provided `bytes` byte array at the specified `offset`.\n *\n * @param data - The byte sequence to search for.\n * @param bytes - The byte array in which to search for `data`.\n * @param offset - The position in `bytes` where the search begins.\n *\n * @example\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02, 0x03, 0x04]);\n * const data = new Uint8Array([0x02, 0x03]);\n * containsBytes(bytes, data, 1); // true\n * containsBytes(bytes, data, 2); // false\n * ```\n */\nexport function containsBytes(\n    data: ReadonlyUint8Array | Uint8Array,\n    bytes: ReadonlyUint8Array | Uint8Array,\n    offset: number,\n): boolean {\n    const slice = offset === 0 && data.length === bytes.length ? data : data.slice(offset, offset + bytes.length);\n    if (slice.length !== bytes.length) return false;\n    return bytes.every((b, i) => b === slice[i]);\n}\n", "import {\n    SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH,\n    SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH,\n    SolanaError,\n} from '@solana/errors';\n\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Defines an offset in bytes.\n */\nexport type Offset = number;\n\n/**\n * An object that can encode a value of type {@link TFrom} into a {@link ReadonlyUint8Array}.\n *\n * This is a common interface for {@link FixedSizeEncoder} and {@link VariableSizeEncoder}.\n *\n * @interface\n * @typeParam TFrom - The type of the value to encode.\n *\n * @see {@link FixedSizeEncoder}\n * @see {@link VariableSizeEncoder}\n */\ntype BaseEncoder<TFrom> = {\n    /** Encode the provided value and return the encoded bytes directly. */\n    readonly encode: (value: TFrom) => ReadonlyUint8Array;\n    /**\n     * Writes the encoded value into the provided byte array at the given offset.\n     * Returns the offset of the next byte after the encoded value.\n     */\n    readonly write: (value: TFrom, bytes: Uint8Array, offset: Offset) => Offset;\n};\n\n/**\n * An object that can encode a value of type {@link TFrom} into a fixed-size {@link ReadonlyUint8Array}.\n *\n * See {@link Encoder} to learn more about creating and composing encoders.\n *\n * @interface\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n *\n * @example\n * ```ts\n * const encoder: FixedSizeEncoder<number, 4>;\n * const bytes = encoder.encode(42);\n * const size = encoder.fixedSize; // 4\n * ```\n *\n * @see {@link Encoder}\n * @see {@link VariableSizeEncoder}\n */\nexport type FixedSizeEncoder<TFrom, TSize extends number = number> = BaseEncoder<TFrom> & {\n    /** The fixed size of the encoded value in bytes. */\n    readonly fixedSize: TSize;\n};\n\n/**\n * An object that can encode a value of type {@link TFrom} into a variable-size {@link ReadonlyUint8Array}.\n *\n * See {@link Encoder} to learn more about creating and composing encoders.\n *\n * @interface\n * @typeParam TFrom - The type of the value to encode.\n *\n * @example\n * ```ts\n * const encoder: VariableSizeEncoder<string>;\n * const bytes = encoder.encode('hello');\n * const size = encoder.getSizeFromValue('hello');\n * ```\n *\n * @see {@link Encoder}\n * @see {@link FixedSizeEncoder}\n */\nexport type VariableSizeEncoder<TFrom> = BaseEncoder<TFrom> & {\n    /** Returns the size of the encoded value in bytes for a given input. */\n    readonly getSizeFromValue: (value: TFrom) => number;\n    /** The maximum possible size of an encoded value in bytes, if applicable. */\n    readonly maxSize?: number;\n};\n\n/**\n * An object that can encode a value of type {@link TFrom} into a {@link ReadonlyUint8Array}.\n *\n * An `Encoder` can be either:\n * - A {@link FixedSizeEncoder}, where all encoded values have the same fixed size.\n * - A {@link VariableSizeEncoder}, where encoded values can vary in size.\n *\n * @typeParam TFrom - The type of the value to encode.\n *\n * @example\n * Encoding a value into a new byte array.\n * ```ts\n * const encoder: Encoder<string>;\n * const bytes = encoder.encode('hello');\n * ```\n *\n * @example\n * Writing the encoded value into an existing byte array.\n * ```ts\n * const encoder: Encoder<string>;\n * const bytes = new Uint8Array(100);\n * const nextOffset = encoder.write('hello', bytes, 20);\n * ```\n *\n * @remarks\n * You may create `Encoders` manually using the {@link createEncoder} function but it is more common\n * to compose multiple `Encoders` together using the various helpers of the `@solana/codecs` package.\n *\n * For instance, here's how you might create an `Encoder` for a `Person` object type that contains\n * a `name` string and an `age` number:\n *\n * ```ts\n * import { getStructEncoder, addEncoderSizePrefix, getUtf8Encoder, getU32Encoder } from '@solana/codecs';\n *\n * type Person = { name: string; age: number };\n * const getPersonEncoder = (): Encoder<Person> =>\n *     getStructEncoder([\n *         ['name', addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder())],\n *         ['age', getU32Encoder()],\n *     ]);\n * ```\n *\n * Note that composed `Encoder` types are clever enough to understand whether\n * they are fixed-size or variable-size. In the example above, `getU32Encoder()` is\n * a fixed-size encoder, while `addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder())`\n * is a variable-size encoder. This makes the final `Person` encoder a variable-size encoder.\n *\n * @see {@link FixedSizeEncoder}\n * @see {@link VariableSizeEncoder}\n * @see {@link createEncoder}\n */\nexport type Encoder<TFrom> = FixedSizeEncoder<TFrom> | VariableSizeEncoder<TFrom>;\n\n/**\n * An object that can decode a byte array into a value of type {@link TTo}.\n *\n * This is a common interface for {@link FixedSizeDecoder} and {@link VariableSizeDecoder}.\n *\n * @interface\n * @typeParam TTo - The type of the decoded value.\n *\n * @see {@link FixedSizeDecoder}\n * @see {@link VariableSizeDecoder}\n */\ntype BaseDecoder<TTo> = {\n    /** Decodes the provided byte array at the given offset (or zero) and returns the value directly. */\n    readonly decode: (bytes: ReadonlyUint8Array | Uint8Array, offset?: Offset) => TTo;\n    /**\n     * Reads the encoded value from the provided byte array at the given offset.\n     * Returns the decoded value and the offset of the next byte after the encoded value.\n     */\n    readonly read: (bytes: ReadonlyUint8Array | Uint8Array, offset: Offset) => [TTo, Offset];\n};\n\n/**\n * An object that can decode a fixed-size byte array into a value of type {@link TTo}.\n *\n * See {@link Decoder} to learn more about creating and composing decoders.\n *\n * @interface\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n *\n * @example\n * ```ts\n * const decoder: FixedSizeDecoder<number, 4>;\n * const value = decoder.decode(bytes);\n * const size = decoder.fixedSize; // 4\n * ```\n *\n * @see {@link Decoder}\n * @see {@link VariableSizeDecoder}\n */\nexport type FixedSizeDecoder<TTo, TSize extends number = number> = BaseDecoder<TTo> & {\n    /** The fixed size of the encoded value in bytes. */\n    readonly fixedSize: TSize;\n};\n\n/**\n * An object that can decode a variable-size byte array into a value of type {@link TTo}.\n *\n * See {@link Decoder} to learn more about creating and composing decoders.\n *\n * @interface\n * @typeParam TTo - The type of the decoded value.\n *\n * @example\n * ```ts\n * const decoder: VariableSizeDecoder<number>;\n * const value = decoder.decode(bytes);\n * ```\n *\n * @see {@link Decoder}\n * @see {@link VariableSizeDecoder}\n */\nexport type VariableSizeDecoder<TTo> = BaseDecoder<TTo> & {\n    /** The maximum possible size of an encoded value in bytes, if applicable. */\n    readonly maxSize?: number;\n};\n\n/**\n * An object that can decode a byte array into a value of type {@link TTo}.\n *\n * An `Decoder` can be either:\n * - A {@link FixedSizeDecoder}, where all byte arrays have the same fixed size.\n * - A {@link VariableSizeDecoder}, where byte arrays can vary in size.\n *\n * @typeParam TTo - The type of the decoded value.\n *\n * @example\n * Getting the decoded value from a byte array.\n * ```ts\n * const decoder: Decoder<string>;\n * const value = decoder.decode(bytes);\n * ```\n *\n * @example\n * Reading the decoded value from a byte array at a specific offset\n * and getting the offset of the next byte to read.\n * ```ts\n * const decoder: Decoder<string>;\n * const [value, nextOffset] = decoder.read('hello', bytes, 20);\n * ```\n *\n * @remarks\n * You may create `Decoders` manually using the {@link createDecoder} function but it is more common\n * to compose multiple `Decoders` together using the various helpers of the `@solana/codecs` package.\n *\n * For instance, here's how you might create an `Decoder` for a `Person` object type that contains\n * a `name` string and an `age` number:\n *\n * ```ts\n * import { getStructDecoder, addDecoderSizePrefix, getUtf8Decoder, getU32Decoder } from '@solana/codecs';\n *\n * type Person = { name: string; age: number };\n * const getPersonDecoder = (): Decoder<Person> =>\n *     getStructDecoder([\n *         ['name', addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder())],\n *         ['age', getU32Decoder()],\n *     ]);\n * ```\n *\n * Note that composed `Decoder` types are clever enough to understand whether\n * they are fixed-size or variable-size. In the example above, `getU32Decoder()` is\n * a fixed-size decoder, while `addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder())`\n * is a variable-size decoder. This makes the final `Person` decoder a variable-size decoder.\n *\n * @see {@link FixedSizeDecoder}\n * @see {@link VariableSizeDecoder}\n * @see {@link createDecoder}\n */\nexport type Decoder<TTo> = FixedSizeDecoder<TTo> | VariableSizeDecoder<TTo>;\n\n/**\n * An object that can encode and decode a value to and from a fixed-size byte array.\n *\n * See {@link Codec} to learn more about creating and composing codecs.\n *\n * @interface\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n *\n * @example\n * ```ts\n * const codec: FixedSizeCodec<number | bigint, bigint, 8>;\n * const bytes = codec.encode(42);\n * const value = codec.decode(bytes); // 42n\n * const size = codec.fixedSize; // 8\n * ```\n *\n * @see {@link Codec}\n * @see {@link VariableSizeCodec}\n */\nexport type FixedSizeCodec<TFrom, TTo extends TFrom = TFrom, TSize extends number = number> = FixedSizeDecoder<\n    TTo,\n    TSize\n> &\n    FixedSizeEncoder<TFrom, TSize>;\n\n/**\n * An object that can encode and decode a value to and from a variable-size byte array.\n *\n * See {@link Codec} to learn more about creating and composing codecs.\n *\n * @interface\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n *\n * @example\n * ```ts\n * const codec: VariableSizeCodec<number | bigint, bigint>;\n * const bytes = codec.encode(42);\n * const value = codec.decode(bytes); // 42n\n * const size = codec.getSizeFromValue(42);\n * ```\n *\n * @see {@link Codec}\n * @see {@link FixedSizeCodec}\n */\nexport type VariableSizeCodec<TFrom, TTo extends TFrom = TFrom> = VariableSizeDecoder<TTo> & VariableSizeEncoder<TFrom>;\n\n/**\n * An object that can encode and decode a value to and from a byte array.\n *\n * A `Codec` can be either:\n * - A {@link FixedSizeCodec}, where all encoded values have the same fixed size.\n * - A {@link VariableSizeCodec}, where encoded values can vary in size.\n *\n * @example\n * ```ts\n * const codec: Codec<string>;\n * const bytes = codec.encode('hello');\n * const value = codec.decode(bytes); // 'hello'\n * ```\n *\n * @remarks\n * For convenience, codecs can encode looser types than they decode.\n * That is, type {@link TFrom} can be a superset of type {@link TTo}.\n * For instance, a `Codec<bigint | number, bigint>` can encode both\n * `bigint` and `number` values, but will always decode to a `bigint`.\n *\n * ```ts\n * const codec: Codec<bigint | number, bigint>;\n * const bytes = codec.encode(42);\n * const value = codec.decode(bytes); // 42n\n * ```\n *\n * It is worth noting that codecs are the union of encoders and decoders.\n * This means that a `Codec<TFrom, TTo>` can be combined from an `Encoder<TFrom>`\n * and a `Decoder<TTo>` using the {@link combineCodec} function. This is particularly\n * useful for library authors who want to expose all three types of objects to their users.\n *\n * ```ts\n * const encoder: Encoder<bigint | number>;\n * const decoder: Decoder<bigint>;\n * const codec: Codec<bigint | number, bigint> = combineCodec(encoder, decoder);\n * ```\n *\n * Aside from combining encoders and decoders, codecs can also be created from scratch using\n * the {@link createCodec} function but it is more common to compose multiple codecs together\n * using the various helpers of the `@solana/codecs` package.\n *\n * For instance, here's how you might create a `Codec` for a `Person` object type that contains\n * a `name` string and an `age` number:\n *\n * ```ts\n * import { getStructCodec, addCodecSizePrefix, getUtf8Codec, getU32Codec } from '@solana/codecs';\n *\n * type Person = { name: string; age: number };\n * const getPersonCodec = (): Codec<Person> =>\n *     getStructCodec([\n *         ['name', addCodecSizePrefix(getUtf8Codec(), getU32Codec())],\n *         ['age', getU32Codec()],\n *     ]);\n * ```\n *\n * Note that composed `Codec` types are clever enough to understand whether\n * they are fixed-size or variable-size. In the example above, `getU32Codec()` is\n * a fixed-size codec, while `addCodecSizePrefix(getUtf8Codec(), getU32Codec())`\n * is a variable-size codec. This makes the final `Person` codec a variable-size codec.\n *\n * @see {@link FixedSizeCodec}\n * @see {@link VariableSizeCodec}\n * @see {@link combineCodec}\n * @see {@link createCodec}\n */\nexport type Codec<TFrom, TTo extends TFrom = TFrom> = FixedSizeCodec<TFrom, TTo> | VariableSizeCodec<TFrom, TTo>;\n\n/**\n * Gets the encoded size of a given value in bytes using the provided encoder.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @param value - The value to be encoded.\n * @param encoder - The encoder used to determine the encoded size.\n * @returns The size of the encoded value in bytes.\n *\n * @example\n * ```ts\n * const fixedSizeEncoder = { fixedSize: 4 };\n * getEncodedSize(123, fixedSizeEncoder); // Returns 4.\n *\n * const variableSizeEncoder = { getSizeFromValue: (value: string) => value.length };\n * getEncodedSize(\"hello\", variableSizeEncoder); // Returns 5.\n * ```\n *\n * @see {@link Encoder}\n */\nexport function getEncodedSize<TFrom>(\n    value: TFrom,\n    encoder: { fixedSize: number } | { getSizeFromValue: (value: TFrom) => number },\n): number {\n    return 'fixedSize' in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);\n}\n\n/**\n * Creates an `Encoder` by filling in the missing `encode` function using the provided `write` function and\n * either the `fixedSize` property (for {@link FixedSizeEncoder | FixedSizeEncoders}) or\n * the `getSizeFromValue` function (for {@link VariableSizeEncoder | VariableSizeEncoders}).\n *\n * Instead of manually implementing `encode`, this utility leverages the existing `write` function\n * and the size helpers to generate a complete encoder. The provided `encode` method will allocate\n * a new `Uint8Array` of the correct size and use `write` to populate it.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TSize - The fixed size of the encoded value in bytes (for fixed-size encoders).\n *\n * @param encoder - An encoder object that implements `write`, but not `encode`.\n * - If the encoder has a `fixedSize` property, it is treated as a {@link FixedSizeEncoder}.\n * - Otherwise, it is treated as a {@link VariableSizeEncoder}.\n *\n * @returns A fully functional `Encoder` with both `write` and `encode` methods.\n *\n * @example\n * Creating a custom fixed-size encoder.\n * ```ts\n * const encoder = createEncoder({\n *     fixedSize: 4,\n *     write: (value: number, bytes, offset) => {\n *         bytes.set(new Uint8Array([value]), offset);\n *         return offset + 4;\n *     },\n * });\n *\n * const bytes = encoder.encode(42);\n * // 0x2a000000\n * ```\n *\n * @example\n * Creating a custom variable-size encoder:\n * ```ts\n * const encoder = createEncoder({\n *     getSizeFromValue: (value: string) => value.length,\n *     write: (value: string, bytes, offset) => {\n *         const encodedValue = new TextEncoder().encode(value);\n *         bytes.set(encodedValue, offset);\n *         return offset + encodedValue.length;\n *     },\n * });\n *\n * const bytes = encoder.encode(\"hello\");\n * // 0x68656c6c6f\n * ```\n *\n * @remarks\n * Note that, while `createEncoder` is useful for defining more complex encoders, it is more common to compose\n * encoders together using the various helpers and primitives of the `@solana/codecs` package.\n *\n * Here are some alternative examples using codec primitives instead of `createEncoder`.\n *\n * ```ts\n * // Fixed-size encoder for unsigned 32-bit integers.\n * const encoder = getU32Encoder();\n * const bytes = encoder.encode(42);\n * // 0x2a000000\n *\n * // Variable-size encoder for 32-bytes prefixed UTF-8 strings.\n * const encoder = addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder());\n * const bytes = encoder.encode(\"hello\");\n * // 0x0500000068656c6c6f\n *\n * // Variable-size encoder for custom objects.\n * type Person = { name: string; age: number };\n * const encoder: Encoder<Person> = getStructEncoder([\n *     ['name', addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder())],\n *     ['age', getU32Encoder()],\n * ]);\n * const bytes = encoder.encode({ name: \"Bob\", age: 42 });\n * // 0x03000000426f622a000000\n * ```\n *\n * @see {@link Encoder}\n * @see {@link FixedSizeEncoder}\n * @see {@link VariableSizeEncoder}\n * @see {@link getStructEncoder}\n * @see {@link getU32Encoder}\n * @see {@link getUtf8Encoder}\n * @see {@link addEncoderSizePrefix}\n */\nexport function createEncoder<TFrom, TSize extends number>(\n    encoder: Omit<FixedSizeEncoder<TFrom, TSize>, 'encode'>,\n): FixedSizeEncoder<TFrom, TSize>;\nexport function createEncoder<TFrom>(encoder: Omit<VariableSizeEncoder<TFrom>, 'encode'>): VariableSizeEncoder<TFrom>;\nexport function createEncoder<TFrom>(\n    encoder: Omit<FixedSizeEncoder<TFrom>, 'encode'> | Omit<VariableSizeEncoder<TFrom>, 'encode'>,\n): Encoder<TFrom>;\nexport function createEncoder<TFrom>(\n    encoder: Omit<FixedSizeEncoder<TFrom>, 'encode'> | Omit<VariableSizeEncoder<TFrom>, 'encode'>,\n): Encoder<TFrom> {\n    return Object.freeze({\n        ...encoder,\n        encode: value => {\n            const bytes = new Uint8Array(getEncodedSize(value, encoder));\n            encoder.write(value, bytes, 0);\n            return bytes;\n        },\n    });\n}\n\n/**\n * Creates a `Decoder` by filling in the missing `decode` function using the provided `read` function.\n *\n * Instead of manually implementing `decode`, this utility leverages the existing `read` function\n * and the size properties to generate a complete decoder. The provided `decode` method will read\n * from a `Uint8Array` at the given offset and return the decoded value.\n *\n * If the `fixedSize` property is provided, a {@link FixedSizeDecoder} will be created, otherwise\n * a {@link VariableSizeDecoder} will be created.\n *\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes (for fixed-size decoders).\n *\n * @param decoder - A decoder object that implements `read`, but not `decode`.\n * - If the decoder has a `fixedSize` property, it is treated as a {@link FixedSizeDecoder}.\n * - Otherwise, it is treated as a {@link VariableSizeDecoder}.\n *\n * @returns A fully functional `Decoder` with both `read` and `decode` methods.\n *\n * @example\n * Creating a custom fixed-size decoder.\n * ```ts\n * const decoder = createDecoder({\n *     fixedSize: 4,\n *     read: (bytes, offset) => {\n *         const value = bytes[offset];\n *         return [value, offset + 4];\n *     },\n * });\n *\n * const value = decoder.decode(new Uint8Array([42, 0, 0, 0]));\n * // 42\n * ```\n *\n * @example\n * Creating a custom variable-size decoder:\n * ```ts\n * const decoder = createDecoder({\n *     read: (bytes, offset) => {\n *         const decodedValue = new TextDecoder().decode(bytes.subarray(offset));\n *         return [decodedValue, bytes.length];\n *     },\n * });\n *\n * const value = decoder.decode(new Uint8Array([104, 101, 108, 108, 111]));\n * // \"hello\"\n * ```\n *\n * @remarks\n * Note that, while `createDecoder` is useful for defining more complex decoders, it is more common to compose\n * decoders together using the various helpers and primitives of the `@solana/codecs` package.\n *\n * Here are some alternative examples using codec primitives instead of `createDecoder`.\n *\n * ```ts\n * // Fixed-size decoder for unsigned 32-bit integers.\n * const decoder = getU32Decoder();\n * const value = decoder.decode(new Uint8Array([42, 0, 0, 0]));\n * // 42\n *\n * // Variable-size decoder for 32-bytes prefixed UTF-8 strings.\n * const decoder = addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder());\n * const value = decoder.decode(new Uint8Array([5, 0, 0, 0, 104, 101, 108, 108, 111]));\n * // \"hello\"\n *\n * // Variable-size decoder for custom objects.\n * type Person = { name: string; age: number };\n * const decoder: Decoder<Person> = getStructDecoder([\n *     ['name', addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder())],\n *     ['age', getU32Decoder()],\n * ]);\n * const value = decoder.decode(new Uint8Array([3, 0, 0, 0, 66, 111, 98, 42, 0, 0, 0]));\n * // { name: \"Bob\", age: 42 }\n * ```\n *\n * @see {@link Decoder}\n * @see {@link FixedSizeDecoder}\n * @see {@link VariableSizeDecoder}\n * @see {@link getStructDecoder}\n * @see {@link getU32Decoder}\n * @see {@link getUtf8Decoder}\n * @see {@link addDecoderSizePrefix}\n */\nexport function createDecoder<TTo, TSize extends number>(\n    decoder: Omit<FixedSizeDecoder<TTo, TSize>, 'decode'>,\n): FixedSizeDecoder<TTo, TSize>;\nexport function createDecoder<TTo>(decoder: Omit<VariableSizeDecoder<TTo>, 'decode'>): VariableSizeDecoder<TTo>;\nexport function createDecoder<TTo>(\n    decoder: Omit<FixedSizeDecoder<TTo>, 'decode'> | Omit<VariableSizeDecoder<TTo>, 'decode'>,\n): Decoder<TTo>;\nexport function createDecoder<TTo>(\n    decoder: Omit<FixedSizeDecoder<TTo>, 'decode'> | Omit<VariableSizeDecoder<TTo>, 'decode'>,\n): Decoder<TTo> {\n    return Object.freeze({\n        ...decoder,\n        decode: (bytes, offset = 0) => decoder.read(bytes, offset)[0],\n    });\n}\n\n/**\n * Creates a `Codec` by filling in the missing `encode` and `decode` functions using the provided `write` and `read` functions.\n *\n * This utility combines the behavior of {@link createEncoder} and {@link createDecoder} to produce a fully functional `Codec`.\n * The `encode` method is derived from the `write` function, while the `decode` method is derived from the `read` function.\n *\n * If the `fixedSize` property is provided, a {@link FixedSizeCodec} will be created, otherwise\n * a {@link VariableSizeCodec} will be created.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes (for fixed-size codecs).\n *\n * @param codec - A codec object that implements `write` and `read`, but not `encode` or `decode`.\n * - If the codec has a `fixedSize` property, it is treated as a {@link FixedSizeCodec}.\n * - Otherwise, it is treated as a {@link VariableSizeCodec}.\n *\n * @returns A fully functional `Codec` with `write`, `read`, `encode`, and `decode` methods.\n *\n * @example\n * Creating a custom fixed-size codec.\n * ```ts\n * const codec = createCodec({\n *     fixedSize: 4,\n *     read: (bytes, offset) => {\n *         const value = bytes[offset];\n *         return [value, offset + 4];\n *     },\n *     write: (value: number, bytes, offset) => {\n *         bytes.set(new Uint8Array([value]), offset);\n *         return offset + 4;\n *     },\n * });\n *\n * const bytes = codec.encode(42);\n * // 0x2a000000\n * const value = codec.decode(bytes);\n * // 42\n * ```\n *\n * @example\n * Creating a custom variable-size codec:\n * ```ts\n * const codec = createCodec({\n *     getSizeFromValue: (value: string) => value.length,\n *     read: (bytes, offset) => {\n *         const decodedValue = new TextDecoder().decode(bytes.subarray(offset));\n *         return [decodedValue, bytes.length];\n *     },\n *     write: (value: string, bytes, offset) => {\n *         const encodedValue = new TextEncoder().encode(value);\n *         bytes.set(encodedValue, offset);\n *         return offset + encodedValue.length;\n *     },\n * });\n *\n * const bytes = codec.encode(\"hello\");\n * // 0x68656c6c6f\n * const value = codec.decode(bytes);\n * // \"hello\"\n * ```\n *\n * @remarks\n * This function effectively combines the behavior of {@link createEncoder} and {@link createDecoder}.\n * If you only need to encode or decode (but not both), consider using those functions instead.\n *\n * Here are some alternative examples using codec primitives instead of `createCodec`.\n *\n * ```ts\n * // Fixed-size codec for unsigned 32-bit integers.\n * const codec = getU32Codec();\n * const bytes = codec.encode(42);\n * // 0x2a000000\n * const value = codec.decode(bytes);\n * // 42\n *\n * // Variable-size codec for 32-bytes prefixed UTF-8 strings.\n * const codec = addCodecSizePrefix(getUtf8Codec(), getU32Codec());\n * const bytes = codec.encode(\"hello\");\n * // 0x0500000068656c6c6f\n * const value = codec.decode(bytes);\n * // \"hello\"\n *\n * // Variable-size codec for custom objects.\n * type Person = { name: string; age: number };\n * const codec: Codec<PersonInput, Person> = getStructCodec([\n *     ['name', addCodecSizePrefix(getUtf8Codec(), getU32Codec())],\n *     ['age', getU32Codec()],\n * ]);\n * const bytes = codec.encode({ name: \"Bob\", age: 42 });\n * // 0x03000000426f622a000000\n * const value = codec.decode(bytes);\n * // { name: \"Bob\", age: 42 }\n * ```\n *\n * @see {@link Codec}\n * @see {@link FixedSizeCodec}\n * @see {@link VariableSizeCodec}\n * @see {@link createEncoder}\n * @see {@link createDecoder}\n * @see {@link getStructCodec}\n * @see {@link getU32Codec}\n * @see {@link getUtf8Codec}\n * @see {@link addCodecSizePrefix}\n */\nexport function createCodec<TFrom, TTo extends TFrom = TFrom, TSize extends number = number>(\n    codec: Omit<FixedSizeCodec<TFrom, TTo, TSize>, 'decode' | 'encode'>,\n): FixedSizeCodec<TFrom, TTo, TSize>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec: Omit<VariableSizeCodec<TFrom, TTo>, 'decode' | 'encode'>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec:\n        | Omit<FixedSizeCodec<TFrom, TTo>, 'decode' | 'encode'>\n        | Omit<VariableSizeCodec<TFrom, TTo>, 'decode' | 'encode'>,\n): Codec<TFrom, TTo>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec:\n        | Omit<FixedSizeCodec<TFrom, TTo>, 'decode' | 'encode'>\n        | Omit<VariableSizeCodec<TFrom, TTo>, 'decode' | 'encode'>,\n): Codec<TFrom, TTo> {\n    return Object.freeze({\n        ...codec,\n        decode: (bytes, offset = 0) => codec.read(bytes, offset)[0],\n        encode: value => {\n            const bytes = new Uint8Array(getEncodedSize(value, codec));\n            codec.write(value, bytes, 0);\n            return bytes;\n        },\n    });\n}\n\n/**\n * Determines whether the given codec, encoder, or decoder is fixed-size.\n *\n * A fixed-size object is identified by the presence of a `fixedSize` property.\n * If this property exists, the object is considered a {@link FixedSizeCodec},\n * {@link FixedSizeEncoder}, or {@link FixedSizeDecoder}.\n * Otherwise, it is assumed to be a {@link VariableSizeCodec},\n * {@link VariableSizeEncoder}, or {@link VariableSizeDecoder}.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n * @returns `true` if the object is fixed-size, `false` otherwise.\n *\n * @example\n * Checking a fixed-size encoder.\n * ```ts\n * const encoder = getU32Encoder();\n * isFixedSize(encoder); // true\n * ```\n *\n * @example\n * Checking a variable-size encoder.\n * ```ts\n * const encoder = addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder());\n * isFixedSize(encoder); // false\n * ```\n *\n * @remarks\n * This function is commonly used to distinguish between fixed-size and variable-size objects at runtime.\n * If you need to enforce this distinction with type assertions, consider using {@link assertIsFixedSize}.\n *\n * @see {@link assertIsFixedSize}\n */\nexport function isFixedSize<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize> | VariableSizeEncoder<TFrom>,\n): encoder is FixedSizeEncoder<TFrom, TSize>;\nexport function isFixedSize<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize> | VariableSizeDecoder<TTo>,\n): decoder is FixedSizeDecoder<TTo, TSize>;\nexport function isFixedSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize> | VariableSizeCodec<TFrom, TTo>,\n): codec is FixedSizeCodec<TFrom, TTo, TSize>;\nexport function isFixedSize<TSize extends number>(\n    codec: { fixedSize: TSize } | { maxSize?: number },\n): codec is { fixedSize: TSize };\nexport function isFixedSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { fixedSize: number } {\n    return 'fixedSize' in codec && typeof codec.fixedSize === 'number';\n}\n\n/**\n * Asserts that the given codec, encoder, or decoder is fixed-size.\n *\n * If the object is not fixed-size (i.e., it lacks a `fixedSize` property),\n * this function throws a {@link SolanaError} with the code `SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH`.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n * @throws {SolanaError} If the object is not fixed-size.\n *\n * @example\n * Asserting a fixed-size encoder.\n * ```ts\n * const encoder = getU32Encoder();\n * assertIsFixedSize(encoder); // Passes\n * ```\n *\n * @example\n * Attempting to assert a variable-size encoder.\n * ```ts\n * const encoder = addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder());\n * assertIsFixedSize(encoder); // Throws SolanaError\n * ```\n *\n * @remarks\n * This function is the assertion-based counterpart of {@link isFixedSize}.\n * If you only need to check whether an object is fixed-size without throwing an error, use {@link isFixedSize} instead.\n *\n * @see {@link isFixedSize}\n */\nexport function assertIsFixedSize<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize> | VariableSizeEncoder<TFrom>,\n): asserts encoder is FixedSizeEncoder<TFrom, TSize>;\nexport function assertIsFixedSize<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize> | VariableSizeDecoder<TTo>,\n): asserts decoder is FixedSizeDecoder<TTo, TSize>;\nexport function assertIsFixedSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize> | VariableSizeCodec<TFrom, TTo>,\n): asserts codec is FixedSizeCodec<TFrom, TTo, TSize>;\nexport function assertIsFixedSize<TSize extends number>(\n    codec: { fixedSize: TSize } | { maxSize?: number },\n): asserts codec is { fixedSize: TSize };\nexport function assertIsFixedSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n): asserts codec is { fixedSize: number } {\n    if (!isFixedSize(codec)) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH);\n    }\n}\n\n/**\n * Determines whether the given codec, encoder, or decoder is variable-size.\n *\n * A variable-size object is identified by the absence of a `fixedSize` property.\n * If this property is missing, the object is considered a {@link VariableSizeCodec},\n * {@link VariableSizeEncoder}, or {@link VariableSizeDecoder}.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n * @returns `true` if the object is variable-size, `false` otherwise.\n *\n * @example\n * Checking a variable-size encoder.\n * ```ts\n * const encoder = addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder());\n * isVariableSize(encoder); // true\n * ```\n *\n * @example\n * Checking a fixed-size encoder.\n * ```ts\n * const encoder = getU32Encoder();\n * isVariableSize(encoder); // false\n * ```\n *\n * @remarks\n * This function is the inverse of {@link isFixedSize}.\n *\n * @see {@link isFixedSize}\n * @see {@link assertIsVariableSize}\n */\nexport function isVariableSize<TFrom>(encoder: Encoder<TFrom>): encoder is VariableSizeEncoder<TFrom>;\nexport function isVariableSize<TTo>(decoder: Decoder<TTo>): decoder is VariableSizeDecoder<TTo>;\nexport function isVariableSize<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n): codec is VariableSizeCodec<TFrom, TTo>;\nexport function isVariableSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { maxSize?: number };\nexport function isVariableSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { maxSize?: number } {\n    return !isFixedSize(codec);\n}\n\n/**\n * Asserts that the given codec, encoder, or decoder is variable-size.\n *\n * If the object is not variable-size (i.e., it has a `fixedSize` property),\n * this function throws a {@link SolanaError} with the code `SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH`.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n * @throws {SolanaError} If the object is not variable-size.\n *\n * @example\n * Asserting a variable-size encoder.\n * ```ts\n * const encoder = addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder());\n * assertIsVariableSize(encoder); // Passes\n * ```\n *\n * @example\n * Attempting to assert a fixed-size encoder.\n * ```ts\n * const encoder = getU32Encoder();\n * assertIsVariableSize(encoder); // Throws SolanaError\n * ```\n *\n * @remarks\n * This function is the assertion-based counterpart of {@link isVariableSize}.\n * If you only need to check whether an object is variable-size without throwing an error, use {@link isVariableSize} instead.\n *\n * Also note that this function is the inverse of {@link assertIsFixedSize}.\n *\n * @see {@link isVariableSize}\n * @see {@link assertIsFixedSize}\n */\nexport function assertIsVariableSize<TFrom>(encoder: Encoder<TFrom>): asserts encoder is VariableSizeEncoder<TFrom>;\nexport function assertIsVariableSize<TTo>(decoder: Decoder<TTo>): asserts decoder is VariableSizeDecoder<TTo>;\nexport function assertIsVariableSize<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n): asserts codec is VariableSizeCodec<TFrom, TTo>;\nexport function assertIsVariableSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n): asserts codec is { maxSize?: number };\nexport function assertIsVariableSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n): asserts codec is { maxSize?: number } {\n    if (!isVariableSize(codec)) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH);\n    }\n}\n", "import {\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH,\n    SolanaError,\n} from '@solana/errors';\n\nimport {\n    Codec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\n\n/**\n * Combines an `Encoder` and a `Decoder` into a `Codec`.\n *\n * That is, given a `Encoder<TFrom>` and a `Decoder<TTo>`, this function returns a `Codec<TFrom, TTo>`.\n *\n * This allows for modular composition by keeping encoding and decoding logic separate\n * while still offering a convenient way to bundle them into a single `Codec`.\n * This is particularly useful for library maintainers who want to expose `Encoders`,\n * `Decoders`, and `Codecs` separately, enabling tree-shaking of unused logic.\n *\n * The provided `Encoder` and `Decoder` must be compatible in terms of:\n * - **Fixed Size:** If both are fixed-size, they must have the same `fixedSize` value.\n * - **Variable Size:** If either has a `maxSize` attribute, it must match the other.\n *\n * If these conditions are not met, a {@link SolanaError} will be thrown.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes (for fixed-size codecs).\n *\n * @param encoder - The `Encoder` to combine.\n * @param decoder - The `Decoder` to combine.\n * @returns A `Codec` that provides both `encode` and `decode` methods.\n *\n * @throws {SolanaError}\n * - `SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH`\n *   Thrown if the encoder and decoder have mismatched size types (fixed vs. variable).\n * - `SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH`\n *   Thrown if both are fixed-size but have different `fixedSize` values.\n * - `SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH`\n *   Thrown if the `maxSize` attributes do not match.\n *\n * @example\n * Creating a fixed-size `Codec` from an encoder and a decoder.\n * ```ts\n * const encoder = getU32Encoder();\n * const decoder = getU32Decoder();\n * const codec = combineCodec(encoder, decoder);\n *\n * const bytes = codec.encode(42); // 0x2a000000\n * const value = codec.decode(bytes); // 42\n * ```\n *\n * @example\n * Creating a variable-size `Codec` from an encoder and a decoder.\n * ```ts\n * const encoder = addEncoderSizePrefix(getUtf8Encoder(), getU32Encoder());\n * const decoder = addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder());\n * const codec = combineCodec(encoder, decoder);\n *\n * const bytes = codec.encode(\"hello\"); // 0x0500000068656c6c6f\n * const value = codec.decode(bytes); // \"hello\"\n * ```\n *\n * @remarks\n * The recommended pattern for defining codecs in libraries is to expose separate functions for the encoder, decoder, and codec.\n * This allows users to import only what they need, improving tree-shaking efficiency.\n *\n * ```ts\n * type MyType = \\/* ... *\\/;\n * const getMyTypeEncoder = (): Encoder<MyType> => { \\/* ... *\\/ };\n * const getMyTypeDecoder = (): Decoder<MyType> => { \\/* ... *\\/ };\n * const getMyTypeCodec = (): Codec<MyType> =>\n *     combineCodec(getMyTypeEncoder(), getMyTypeDecoder());\n * ```\n *\n * @see {@link Codec}\n * @see {@link Encoder}\n * @see {@link Decoder}\n */\nexport function combineCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize>,\n    decoder: FixedSizeDecoder<TTo, TSize>,\n): FixedSizeCodec<TFrom, TTo, TSize>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: VariableSizeEncoder<TFrom>,\n    decoder: VariableSizeDecoder<TTo>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: Encoder<TFrom>,\n    decoder: Decoder<TTo>,\n): Codec<TFrom, TTo>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: Encoder<TFrom>,\n    decoder: Decoder<TTo>,\n): Codec<TFrom, TTo> {\n    if (isFixedSize(encoder) !== isFixedSize(decoder)) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH);\n    }\n\n    if (isFixedSize(encoder) && isFixedSize(decoder) && encoder.fixedSize !== decoder.fixedSize) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH, {\n            decoderFixedSize: decoder.fixedSize,\n            encoderFixedSize: encoder.fixedSize,\n        });\n    }\n\n    if (!isFixedSize(encoder) && !isFixedSize(decoder) && encoder.maxSize !== decoder.maxSize) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH, {\n            decoderMaxSize: decoder.maxSize,\n            encoderMaxSize: encoder.maxSize,\n        });\n    }\n\n    return {\n        ...decoder,\n        ...encoder,\n        decode: decoder.decode,\n        encode: encoder.encode,\n        read: decoder.read,\n        write: encoder.write,\n    };\n}\n", "import {\n    SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL,\n    SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES,\n    SolanaError,\n} from '@solana/errors';\n\nimport { containsBytes } from './bytes';\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\nimport { combineCodec } from './combine-codec';\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Creates an encoder that writes a `Uint8Array` sentinel after the encoded value.\n * This is useful to delimit the encoded value when being read by a decoder.\n *\n * See {@link addCodecSentinel} for more information.\n *\n * @typeParam TFrom - The type of the value to encode.\n *\n * @see {@link addCodecSentinel}\n */\nexport function addEncoderSentinel<TFrom>(\n    encoder: FixedSizeEncoder<TFrom>,\n    sentinel: ReadonlyUint8Array,\n): FixedSizeEncoder<TFrom>;\nexport function addEncoderSentinel<TFrom>(\n    encoder: Encoder<TFrom>,\n    sentinel: ReadonlyUint8Array,\n): VariableSizeEncoder<TFrom>;\nexport function addEncoderSentinel<TFrom>(encoder: Encoder<TFrom>, sentinel: ReadonlyUint8Array): Encoder<TFrom> {\n    const write = ((value, bytes, offset) => {\n        // Here we exceptionally use the `encode` function instead of the `write`\n        // function to contain the content of the encoder within its own bounds\n        // and to avoid writing the sentinel as part of the encoded value.\n        const encoderBytes = encoder.encode(value);\n        if (findSentinelIndex(encoderBytes, sentinel) >= 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL, {\n                encodedBytes: encoderBytes,\n                hexEncodedBytes: hexBytes(encoderBytes),\n                hexSentinel: hexBytes(sentinel),\n                sentinel,\n            });\n        }\n        bytes.set(encoderBytes, offset);\n        offset += encoderBytes.length;\n        bytes.set(sentinel, offset);\n        offset += sentinel.length;\n        return offset;\n    }) as Encoder<TFrom>['write'];\n\n    if (isFixedSize(encoder)) {\n        return createEncoder({ ...encoder, fixedSize: encoder.fixedSize + sentinel.length, write });\n    }\n\n    return createEncoder({\n        ...encoder,\n        ...(encoder.maxSize != null ? { maxSize: encoder.maxSize + sentinel.length } : {}),\n        getSizeFromValue: value => encoder.getSizeFromValue(value) + sentinel.length,\n        write,\n    });\n}\n\n/**\n * Creates a decoder that continues reading until\n * a given `Uint8Array` sentinel is found.\n *\n * See {@link addCodecSentinel} for more information.\n *\n * @typeParam TTo - The type of the decoded value.\n *\n * @see {@link addCodecSentinel}\n */\nexport function addDecoderSentinel<TTo>(\n    decoder: FixedSizeDecoder<TTo>,\n    sentinel: ReadonlyUint8Array,\n): FixedSizeDecoder<TTo>;\nexport function addDecoderSentinel<TTo>(decoder: Decoder<TTo>, sentinel: ReadonlyUint8Array): VariableSizeDecoder<TTo>;\nexport function addDecoderSentinel<TTo>(decoder: Decoder<TTo>, sentinel: ReadonlyUint8Array): Decoder<TTo> {\n    const read = ((bytes, offset) => {\n        const candidateBytes = offset === 0 ? bytes : bytes.slice(offset);\n        const sentinelIndex = findSentinelIndex(candidateBytes, sentinel);\n        if (sentinelIndex === -1) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES, {\n                decodedBytes: candidateBytes,\n                hexDecodedBytes: hexBytes(candidateBytes),\n                hexSentinel: hexBytes(sentinel),\n                sentinel,\n            });\n        }\n        const preSentinelBytes = candidateBytes.slice(0, sentinelIndex);\n        // Here we exceptionally use the `decode` function instead of the `read`\n        // function to contain the content of the decoder within its own bounds\n        // and ensure that the sentinel is not part of the decoded value.\n        return [decoder.decode(preSentinelBytes), offset + preSentinelBytes.length + sentinel.length];\n    }) as Decoder<TTo>['read'];\n\n    if (isFixedSize(decoder)) {\n        return createDecoder({ ...decoder, fixedSize: decoder.fixedSize + sentinel.length, read });\n    }\n\n    return createDecoder({\n        ...decoder,\n        ...(decoder.maxSize != null ? { maxSize: decoder.maxSize + sentinel.length } : {}),\n        read,\n    });\n}\n\n/**\n * Creates a Codec that writes a given `Uint8Array` sentinel after the encoded\n * value and, when decoding, continues reading until the sentinel is found.\n *\n * This sets a limit on variable-size codecs and tells us when to stop decoding.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n *\n * @example\n * ```ts\n * const codec = addCodecSentinel(getUtf8Codec(), new Uint8Array([255, 255]));\n * codec.encode('hello');\n * // 0x68656c6c6fffff\n * //   |        └-- Our sentinel.\n * //   └-- Our encoded string.\n * ```\n *\n * @remarks\n * Note that the sentinel _must not_ be present in the encoded data and\n * _must_ be present in the decoded data for this to work.\n * If this is not the case, dedicated errors will be thrown.\n *\n * ```ts\n * const sentinel = new Uint8Array([108, 108]); // 'll'\n * const codec = addCodecSentinel(getUtf8Codec(), sentinel);\n *\n * codec.encode('hello'); // Throws: sentinel is in encoded data.\n * codec.decode(new Uint8Array([1, 2, 3])); // Throws: sentinel missing in decoded data.\n * ```\n *\n * Separate {@link addEncoderSentinel} and {@link addDecoderSentinel} functions are also available.\n *\n * ```ts\n * const bytes = addEncoderSentinel(getUtf8Encoder(), sentinel).encode('hello');\n * const value = addDecoderSentinel(getUtf8Decoder(), sentinel).decode(bytes);\n * ```\n *\n * @see {@link addEncoderSentinel}\n * @see {@link addDecoderSentinel}\n */\nexport function addCodecSentinel<TFrom, TTo extends TFrom>(\n    codec: FixedSizeCodec<TFrom, TTo>,\n    sentinel: ReadonlyUint8Array,\n): FixedSizeCodec<TFrom, TTo>;\nexport function addCodecSentinel<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    sentinel: ReadonlyUint8Array,\n): VariableSizeCodec<TFrom, TTo>;\nexport function addCodecSentinel<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    sentinel: ReadonlyUint8Array,\n): Codec<TFrom, TTo> {\n    return combineCodec(addEncoderSentinel(codec, sentinel), addDecoderSentinel(codec, sentinel));\n}\n\nfunction findSentinelIndex(bytes: ReadonlyUint8Array, sentinel: ReadonlyUint8Array) {\n    return bytes.findIndex((byte, index, arr) => {\n        if (sentinel.length === 1) return byte === sentinel[0];\n        return containsBytes(arr, sentinel, index);\n    });\n}\n\nfunction hexBytes(bytes: ReadonlyUint8Array): string {\n    return bytes.reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n}\n", "import {\n    SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY,\n    SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH,\n    SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE,\n    SolanaError,\n} from '@solana/errors';\n\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Asserts that a given byte array is not empty (after the optional provided offset).\n *\n * Returns void if the byte array is not empty but throws a {@link SolanaError} otherwise.\n *\n * @param codecDescription - A description of the codec used by the assertion error.\n * @param bytes - The byte array to check.\n * @param offset - The offset from which to start checking the byte array.\n * If provided, the byte array is considered empty if it has no bytes after the offset.\n *\n * @example\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02, 0x03]);\n * assertByteArrayIsNotEmptyForCodec('myCodec', bytes); // OK\n * assertByteArrayIsNotEmptyForCodec('myCodec', bytes, 1); // OK\n * assertByteArrayIsNotEmptyForCodec('myCodec', bytes, 3); // Throws\n * ```\n */\nexport function assertByteArrayIsNotEmptyForCodec(\n    codecDescription: string,\n    bytes: ReadonlyUint8Array | Uint8Array,\n    offset = 0,\n) {\n    if (bytes.length - offset <= 0) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY, {\n            codecDescription,\n        });\n    }\n}\n\n/**\n * Asserts that a given byte array has enough bytes to decode\n * (after the optional provided offset).\n *\n * Returns void if the byte array has at least the expected number\n * of bytes but throws a {@link SolanaError} otherwise.\n *\n * @param codecDescription - A description of the codec used by the assertion error.\n * @param expected - The minimum number of bytes expected in the byte array.\n * @param bytes - The byte array to check.\n * @param offset - The offset from which to start checking the byte array.\n *\n * @example\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02, 0x03]);\n * assertByteArrayHasEnoughBytesForCodec('myCodec', 3, bytes); // OK\n * assertByteArrayHasEnoughBytesForCodec('myCodec', 4, bytes); // Throws\n * assertByteArrayHasEnoughBytesForCodec('myCodec', 2, bytes, 1); // OK\n * assertByteArrayHasEnoughBytesForCodec('myCodec', 3, bytes, 1); // Throws\n * ```\n */\nexport function assertByteArrayHasEnoughBytesForCodec(\n    codecDescription: string,\n    expected: number,\n    bytes: ReadonlyUint8Array | Uint8Array,\n    offset = 0,\n) {\n    const bytesLength = bytes.length - offset;\n    if (bytesLength < expected) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH, {\n            bytesLength,\n            codecDescription,\n            expected,\n        });\n    }\n}\n\n/**\n * Asserts that a given offset is within the byte array bounds.\n * This range is between 0 and the byte array length and is inclusive.\n * An offset equals to the byte array length is considered a valid offset\n * as it allows the post-offset of codecs to signal the end of the byte array.\n *\n * @param codecDescription - A description of the codec used by the assertion error.\n * @param offset - The offset to check.\n * @param bytesLength - The length of the byte array from which the offset should be within bounds.\n *\n * @example\n * ```ts\n * const bytes = new Uint8Array([0x01, 0x02, 0x03]);\n * assertByteArrayOffsetIsNotOutOfRange('myCodec', 0, bytes.length); // OK\n * assertByteArrayOffsetIsNotOutOfRange('myCodec', 3, bytes.length); // OK\n * assertByteArrayOffsetIsNotOutOfRange('myCodec', 4, bytes.length); // Throws\n * ```\n */\nexport function assertByteArrayOffsetIsNotOutOfRange(codecDescription: string, offset: number, bytesLength: number) {\n    if (offset < 0 || offset > bytesLength) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE, {\n            bytesLength,\n            codecDescription,\n            offset,\n        });\n    }\n}\n", "import { assertByteArrayHasEnoughBytesForCodec } from './assertions';\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\nimport { combineCodec } from './combine-codec';\n\ntype NumberEncoder = Encoder<bigint | number> | Encoder<number>;\ntype FixedSizeNumberEncoder<TSize extends number = number> =\n    | FixedSizeEncoder<bigint | number, TSize>\n    | FixedSizeEncoder<number, TSize>;\ntype NumberDecoder = Decoder<bigint> | Decoder<number>;\ntype FixedSizeNumberDecoder<TSize extends number = number> =\n    | FixedSizeDecoder<bigint, TSize>\n    | FixedSizeDecoder<number, TSize>;\ntype NumberCodec = Codec<bigint | number, bigint> | Codec<number>;\ntype FixedSizeNumberCodec<TSize extends number = number> =\n    | FixedSizeCodec<bigint | number, bigint, TSize>\n    | FixedSizeCodec<number, number, TSize>;\n\n/**\n * Stores the size of the `encoder` in bytes as a prefix using the `prefix` encoder.\n *\n * See {@link addCodecSizePrefix} for more information.\n *\n * @typeParam TFrom - The type of the value to encode.\n *\n * @see {@link addCodecSizePrefix}\n */\nexport function addEncoderSizePrefix<TFrom>(\n    encoder: FixedSizeEncoder<TFrom>,\n    prefix: FixedSizeNumberEncoder,\n): FixedSizeEncoder<TFrom>;\nexport function addEncoderSizePrefix<TFrom>(encoder: Encoder<TFrom>, prefix: NumberEncoder): VariableSizeEncoder<TFrom>;\nexport function addEncoderSizePrefix<TFrom>(encoder: Encoder<TFrom>, prefix: NumberEncoder): Encoder<TFrom> {\n    const write = ((value, bytes, offset) => {\n        // Here we exceptionally use the `encode` function instead of the `write`\n        // function to contain the content of the encoder within its own bounds.\n        const encoderBytes = encoder.encode(value);\n        offset = prefix.write(encoderBytes.length, bytes, offset);\n        bytes.set(encoderBytes, offset);\n        return offset + encoderBytes.length;\n    }) as Encoder<TFrom>['write'];\n\n    if (isFixedSize(prefix) && isFixedSize(encoder)) {\n        return createEncoder({ ...encoder, fixedSize: prefix.fixedSize + encoder.fixedSize, write });\n    }\n\n    const prefixMaxSize = isFixedSize(prefix) ? prefix.fixedSize : (prefix.maxSize ?? null);\n    const encoderMaxSize = isFixedSize(encoder) ? encoder.fixedSize : (encoder.maxSize ?? null);\n    const maxSize = prefixMaxSize !== null && encoderMaxSize !== null ? prefixMaxSize + encoderMaxSize : null;\n\n    return createEncoder({\n        ...encoder,\n        ...(maxSize !== null ? { maxSize } : {}),\n        getSizeFromValue: value => {\n            const encoderSize = getEncodedSize(value, encoder);\n            return getEncodedSize(encoderSize, prefix) + encoderSize;\n        },\n        write,\n    });\n}\n\n/**\n * Bounds the size of the nested `decoder` by reading its encoded `prefix`.\n *\n * See {@link addCodecSizePrefix} for more information.\n *\n * @typeParam TTo - The type of the decoded value.\n *\n * @see {@link addCodecSizePrefix}\n */\nexport function addDecoderSizePrefix<TTo>(\n    decoder: FixedSizeDecoder<TTo>,\n    prefix: FixedSizeNumberDecoder,\n): FixedSizeDecoder<TTo>;\nexport function addDecoderSizePrefix<TTo>(decoder: Decoder<TTo>, prefix: NumberDecoder): VariableSizeDecoder<TTo>;\nexport function addDecoderSizePrefix<TTo>(decoder: Decoder<TTo>, prefix: NumberDecoder): Decoder<TTo> {\n    const read = ((bytes, offset) => {\n        const [bigintSize, decoderOffset] = prefix.read(bytes, offset);\n        const size = Number(bigintSize);\n        offset = decoderOffset;\n        // Slice the byte array to the contained size if necessary.\n        if (offset > 0 || bytes.length > size) {\n            bytes = bytes.slice(offset, offset + size);\n        }\n        assertByteArrayHasEnoughBytesForCodec('addDecoderSizePrefix', size, bytes);\n        // Here we exceptionally use the `decode` function instead of the `read`\n        // function to contain the content of the decoder within its own bounds.\n        return [decoder.decode(bytes), offset + size];\n    }) as Decoder<TTo>['read'];\n\n    if (isFixedSize(prefix) && isFixedSize(decoder)) {\n        return createDecoder({ ...decoder, fixedSize: prefix.fixedSize + decoder.fixedSize, read });\n    }\n\n    const prefixMaxSize = isFixedSize(prefix) ? prefix.fixedSize : (prefix.maxSize ?? null);\n    const decoderMaxSize = isFixedSize(decoder) ? decoder.fixedSize : (decoder.maxSize ?? null);\n    const maxSize = prefixMaxSize !== null && decoderMaxSize !== null ? prefixMaxSize + decoderMaxSize : null;\n    return createDecoder({ ...decoder, ...(maxSize !== null ? { maxSize } : {}), read });\n}\n\n/**\n * Stores the byte size of any given codec as an encoded number prefix.\n *\n * This sets a limit on variable-size codecs and tells us when to stop decoding.\n * When encoding, the size of the encoded data is stored before the encoded data itself.\n * When decoding, the size is read first to know how many bytes to read next.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n *\n * @example\n * For example, say we want to bound a variable-size base-58 string using a `u32` size prefix.\n * Here’s how you can use the `addCodecSizePrefix` function to achieve that.\n *\n * ```ts\n * const getU32Base58Codec = () => addCodecSizePrefix(getBase58Codec(), getU32Codec());\n *\n * getU32Base58Codec().encode('hello world');\n * // 0x0b00000068656c6c6f20776f726c64\n * //   |       └-- Our encoded base-58 string.\n * //   └-- Our encoded u32 size prefix.\n * ```\n *\n * @remarks\n * Separate {@link addEncoderSizePrefix} and {@link addDecoderSizePrefix} functions are also available.\n *\n * ```ts\n * const bytes = addEncoderSizePrefix(getBase58Encoder(), getU32Encoder()).encode('hello');\n * const value = addDecoderSizePrefix(getBase58Decoder(), getU32Decoder()).decode(bytes);\n * ```\n *\n * @see {@link addEncoderSizePrefix}\n * @see {@link addDecoderSizePrefix}\n */\nexport function addCodecSizePrefix<TFrom, TTo extends TFrom>(\n    codec: FixedSizeCodec<TFrom, TTo>,\n    prefix: FixedSizeNumberCodec,\n): FixedSizeCodec<TFrom, TTo>;\nexport function addCodecSizePrefix<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    prefix: NumberCodec,\n): VariableSizeCodec<TFrom, TTo>;\nexport function addCodecSizePrefix<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    prefix: NumberCodec,\n): Codec<TFrom, TTo> {\n    return combineCodec(addEncoderSizePrefix(codec, prefix), addDecoderSizePrefix(codec, prefix));\n}\n", "import { assertByteArrayHasEnoughBytesForCodec } from './assertions';\nimport { fixBytes } from './bytes';\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    Offset,\n} from './codec';\nimport { combineCodec } from './combine-codec';\n\n/**\n * Creates a fixed-size encoder from a given encoder.\n *\n * The resulting encoder ensures that encoded values always have the specified number of bytes.\n * If the original encoded value is larger than `fixedBytes`, it is truncated.\n * If it is smaller, it is padded with trailing zeroes.\n *\n * For more details, see {@link fixCodecSize}.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n *\n * @param encoder - The encoder to wrap into a fixed-size encoder.\n * @param fixedBytes - The fixed number of bytes to write.\n * @returns A `FixedSizeEncoder` that ensures a consistent output size.\n *\n * @example\n * ```ts\n * const encoder = fixEncoderSize(getUtf8Encoder(), 4);\n * encoder.encode(\"Hello\"); // 0x48656c6c (truncated)\n * encoder.encode(\"Hi\");    // 0x48690000 (padded)\n * encoder.encode(\"Hiya\");  // 0x48697961 (same length)\n * ```\n *\n * @remarks\n * If you need a full codec with both encoding and decoding, use {@link fixCodecSize}.\n *\n * @see {@link fixCodecSize}\n * @see {@link fixDecoderSize}\n */\nexport function fixEncoderSize<TFrom, TSize extends number>(\n    encoder: Encoder<TFrom>,\n    fixedBytes: TSize,\n): FixedSizeEncoder<TFrom, TSize> {\n    return createEncoder({\n        fixedSize: fixedBytes,\n        write: (value: TFrom, bytes: Uint8Array, offset: Offset) => {\n            // Here we exceptionally use the `encode` function instead of the `write`\n            // function as using the nested `write` function on a fixed-sized byte\n            // array may result in a out-of-bounds error on the nested encoder.\n            const variableByteArray = encoder.encode(value);\n            const fixedByteArray =\n                variableByteArray.length > fixedBytes ? variableByteArray.slice(0, fixedBytes) : variableByteArray;\n            bytes.set(fixedByteArray, offset);\n            return offset + fixedBytes;\n        },\n    });\n}\n\n/**\n * Creates a fixed-size decoder from a given decoder.\n *\n * The resulting decoder always reads exactly `fixedBytes` bytes from the input.\n * If the nested decoder is also fixed-size, the bytes are truncated or padded as needed.\n *\n * For more details, see {@link fixCodecSize}.\n *\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n *\n * @param decoder - The decoder to wrap into a fixed-size decoder.\n * @param fixedBytes - The fixed number of bytes to read.\n * @returns A `FixedSizeDecoder` that ensures a consistent input size.\n *\n * @example\n * ```ts\n * const decoder = fixDecoderSize(getUtf8Decoder(), 4);\n * decoder.decode(new Uint8Array([72, 101, 108, 108, 111])); // \"Hell\" (truncated)\n * decoder.decode(new Uint8Array([72, 105, 0, 0]));          // \"Hi\" (zeroes ignored)\n * decoder.decode(new Uint8Array([72, 105, 121, 97]));       // \"Hiya\" (same length)\n * ```\n *\n * @remarks\n * If you need a full codec with both encoding and decoding, use {@link fixCodecSize}.\n *\n * @see {@link fixCodecSize}\n * @see {@link fixEncoderSize}\n */\nexport function fixDecoderSize<TTo, TSize extends number>(\n    decoder: Decoder<TTo>,\n    fixedBytes: TSize,\n): FixedSizeDecoder<TTo, TSize> {\n    return createDecoder({\n        fixedSize: fixedBytes,\n        read: (bytes, offset) => {\n            assertByteArrayHasEnoughBytesForCodec('fixCodecSize', fixedBytes, bytes, offset);\n            // Slice the byte array to the fixed size if necessary.\n            if (offset > 0 || bytes.length > fixedBytes) {\n                bytes = bytes.slice(offset, offset + fixedBytes);\n            }\n            // If the nested decoder is fixed-size, pad and truncate the byte array accordingly.\n            if (isFixedSize(decoder)) {\n                bytes = fixBytes(bytes, decoder.fixedSize);\n            }\n            // Decode the value using the nested decoder.\n            const [value] = decoder.read(bytes, 0);\n            return [value, offset + fixedBytes];\n        },\n    });\n}\n\n/**\n * Creates a fixed-size codec from a given codec.\n *\n * The resulting codec ensures that both encoding and decoding operate on a fixed number of bytes.\n * When encoding:\n * - If the encoded value is larger than `fixedBytes`, it is truncated.\n * - If it is smaller, it is padded with trailing zeroes.\n * - If it is exactly `fixedBytes`, it remains unchanged.\n *\n * When decoding:\n * - Exactly `fixedBytes` bytes are read from the input.\n * - If the nested decoder has a smaller fixed size, bytes are truncated or padded as necessary.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n *\n * @param codec - The codec to wrap into a fixed-size codec.\n * @param fixedBytes - The fixed number of bytes to read/write.\n * @returns A `FixedSizeCodec` that ensures both encoding and decoding conform to a fixed size.\n *\n * @example\n * ```ts\n * const codec = fixCodecSize(getUtf8Codec(), 4);\n *\n * const bytes1 = codec.encode(\"Hello\"); // 0x48656c6c (truncated)\n * const value1 = codec.decode(bytes1);  // \"Hell\"\n *\n * const bytes2 = codec.encode(\"Hi\");    // 0x48690000 (padded)\n * const value2 = codec.decode(bytes2);  // \"Hi\"\n *\n * const bytes3 = codec.encode(\"Hiya\");  // 0x48697961 (same length)\n * const value3 = codec.decode(bytes3);  // \"Hiya\"\n * ```\n *\n * @remarks\n * If you only need to enforce a fixed size for encoding, use {@link fixEncoderSize}.\n * If you only need to enforce a fixed size for decoding, use {@link fixDecoderSize}.\n *\n * ```ts\n * const bytes = fixEncoderSize(getUtf8Encoder(), 4).encode(\"Hiya\");\n * const value = fixDecoderSize(getUtf8Decoder(), 4).decode(bytes);\n * ```\n *\n * @see {@link fixEncoderSize}\n * @see {@link fixDecoderSize}\n */\nexport function fixCodecSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: Codec<TFrom, TTo>,\n    fixedBytes: TSize,\n): FixedSizeCodec<TFrom, TTo, TSize> {\n    return combineCodec(fixEncoderSize(codec, fixedBytes), fixDecoderSize(codec, fixedBytes));\n}\n", "import { assertByteArrayOffsetIsNotOutOfRange } from './assertions';\nimport { Codec, createDecoder, createEncoder, Decoder, Encoder, Offset } from './codec';\nimport { combineCodec } from './combine-codec';\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyEncoder = Encoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyDecoder = Decoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyCodec = Codec<any>;\n\n/**\n * Configuration object for modifying the offset of an encoder, decoder, or codec.\n *\n * This type defines optional functions for adjusting the **pre-offset** (before encoding/decoding)\n * and the **post-offset** (after encoding/decoding). These functions allow precise control\n * over where data is written or read within a byte array.\n *\n * @property preOffset - A function that modifies the offset before encoding or decoding.\n * @property postOffset - A function that modifies the offset after encoding or decoding.\n *\n * @example\n * Moving the pre-offset forward by 2 bytes.\n * ```ts\n * const config: OffsetConfig = {\n *     preOffset: ({ preOffset }) => preOffset + 2,\n * };\n * ```\n *\n * @example\n * Moving the post-offset forward by 2 bytes.\n * ```ts\n * const config: OffsetConfig = {\n *     postOffset: ({ postOffset }) => postOffset + 2,\n * };\n * ```\n *\n * @example\n * Using both pre-offset and post-offset together.\n * ```ts\n * const config: OffsetConfig = {\n *     preOffset: ({ preOffset }) => preOffset + 2,\n *     postOffset: ({ postOffset }) => postOffset + 4,\n * };\n * ```\n *\n * @see {@link offsetEncoder}\n * @see {@link offsetDecoder}\n * @see {@link offsetCodec}\n */\ntype OffsetConfig = {\n    postOffset?: PostOffsetFunction;\n    preOffset?: PreOffsetFunction;\n};\n\n/**\n * Scope provided to the `preOffset` and `postOffset` functions,\n * containing contextual information about the current encoding or decoding process.\n *\n * The pre-offset function modifies where encoding or decoding begins,\n * while the post-offset function modifies where the next operation continues.\n *\n * @property bytes - The entire byte array being encoded or decoded.\n * @property preOffset - The original offset before encoding or decoding starts.\n * @property wrapBytes - A helper function that wraps offsets around the byte array length.\n *\n * @example\n * Using `wrapBytes` to wrap a negative offset to the end of the byte array.\n * ```ts\n * const config: OffsetConfig = {\n *     preOffset: ({ wrapBytes }) => wrapBytes(-4), // Moves to last 4 bytes\n * };\n * ```\n *\n * @example\n * Adjusting the offset dynamically based on the byte array size.\n * ```ts\n * const config: OffsetConfig = {\n *     preOffset: ({ bytes }) => bytes.length > 10 ? 4 : 2,\n * };\n * ```\n *\n * @see {@link PreOffsetFunction}\n * @see {@link PostOffsetFunction}\n */\ntype PreOffsetFunctionScope = {\n    /** The entire byte array. */\n    bytes: ReadonlyUint8Array | Uint8Array;\n    /** The original offset prior to encode or decode. */\n    preOffset: Offset;\n    /** Wraps the offset to the byte array length. */\n    wrapBytes: (offset: Offset) => Offset;\n};\n\n/**\n * A function that modifies the pre-offset before encoding or decoding.\n *\n * This function is used to adjust the starting position before writing\n * or reading data in a byte array.\n *\n * @param scope - The current encoding or decoding context.\n * @returns The new offset at which encoding or decoding should start.\n *\n * @example\n * Skipping the first 2 bytes before writing or reading.\n * ```ts\n * const preOffset: PreOffsetFunction = ({ preOffset }) => preOffset + 2;\n * ```\n *\n * @example\n * Wrapping the offset to ensure it stays within bounds.\n * ```ts\n * const preOffset: PreOffsetFunction = ({ wrapBytes, preOffset }) => wrapBytes(preOffset + 10);\n * ```\n *\n * @see {@link OffsetConfig}\n * @see {@link PreOffsetFunctionScope}\n */\ntype PreOffsetFunction = (scope: PreOffsetFunctionScope) => Offset;\n\n/**\n * A function that modifies the post-offset after encoding or decoding.\n *\n * This function adjusts where the next encoder or decoder should start\n * after the current operation has completed.\n *\n * @param scope - The current encoding or decoding context, including the modified pre-offset\n * and the original post-offset.\n * @returns The new offset at which the next operation should begin.\n *\n * @example\n * Moving the post-offset forward by 4 bytes.\n * ```ts\n * const postOffset: PostOffsetFunction = ({ postOffset }) => postOffset + 4;\n * ```\n *\n * @example\n * Wrapping the post-offset within the byte array length.\n * ```ts\n * const postOffset: PostOffsetFunction = ({ wrapBytes, postOffset }) => wrapBytes(postOffset);\n * ```\n *\n * @example\n * Ensuring a minimum spacing of 8 bytes between values.\n * ```ts\n * const postOffset: PostOffsetFunction = ({ postOffset, newPreOffset }) =>\n *     Math.max(postOffset, newPreOffset + 8);\n * ```\n *\n * @see {@link OffsetConfig}\n * @see {@link PreOffsetFunctionScope}\n */\ntype PostOffsetFunction = (\n    scope: PreOffsetFunctionScope & {\n        /** The modified offset used to encode or decode. */\n        newPreOffset: Offset;\n        /** The original offset returned by the encoder or decoder. */\n        postOffset: Offset;\n    },\n) => Offset;\n\n/**\n * Moves the offset of a given encoder before and/or after encoding.\n *\n * This function allows an encoder to write its encoded value at a different offset\n * than the one originally provided. It supports both pre-offset adjustments\n * (before encoding) and post-offset adjustments (after encoding).\n *\n * The pre-offset function determines where encoding should start, while the\n * post-offset function adjusts where the next encoder should continue writing.\n *\n * For more details, see {@link offsetCodec}.\n *\n * @typeParam TFrom - The type of the value to encode.\n *\n * @param encoder - The encoder to adjust.\n * @param config - An object specifying how the offset should be modified.\n * @returns A new encoder with adjusted offsets.\n *\n * @example\n * Moving the pre-offset forward by 2 bytes.\n * ```ts\n * const encoder = offsetEncoder(getU32Encoder(), {\n *     preOffset: ({ preOffset }) => preOffset + 2,\n * });\n * const bytes = new Uint8Array(10);\n * encoder.write(42, bytes, 0); // Actually written at offset 2\n * ```\n *\n * @example\n * Moving the post-offset forward by 2 bytes.\n * ```ts\n * const encoder = offsetEncoder(getU32Encoder(), {\n *     postOffset: ({ postOffset }) => postOffset + 2,\n * });\n * const bytes = new Uint8Array(10);\n * const nextOffset = encoder.write(42, bytes, 0); // Next encoder starts at offset 6 instead of 4\n * ```\n *\n * @example\n * Using `wrapBytes` to ensure an offset wraps around the byte array length.\n * ```ts\n * const encoder = offsetEncoder(getU32Encoder(), {\n *     preOffset: ({ wrapBytes }) => wrapBytes(-4), // Moves offset to last 4 bytes of the array\n * });\n * const bytes = new Uint8Array(10);\n * encoder.write(42, bytes, 0); // Writes at bytes.length - 4\n * ```\n *\n * @remarks\n * If you need both encoding and decoding offsets to be adjusted, use {@link offsetCodec}.\n *\n * @see {@link offsetCodec}\n * @see {@link offsetDecoder}\n */\nexport function offsetEncoder<TEncoder extends AnyEncoder>(encoder: TEncoder, config: OffsetConfig): TEncoder {\n    return createEncoder({\n        ...encoder,\n        write: (value, bytes, preOffset) => {\n            const wrapBytes = (offset: Offset) => modulo(offset, bytes.length);\n            const newPreOffset = config.preOffset ? config.preOffset({ bytes, preOffset, wrapBytes }) : preOffset;\n            assertByteArrayOffsetIsNotOutOfRange('offsetEncoder', newPreOffset, bytes.length);\n            const postOffset = encoder.write(value, bytes, newPreOffset);\n            const newPostOffset = config.postOffset\n                ? config.postOffset({ bytes, newPreOffset, postOffset, preOffset, wrapBytes })\n                : postOffset;\n            assertByteArrayOffsetIsNotOutOfRange('offsetEncoder', newPostOffset, bytes.length);\n            return newPostOffset;\n        },\n    }) as TEncoder;\n}\n\n/**\n * Moves the offset of a given decoder before and/or after decoding.\n *\n * This function allows a decoder to read its input from a different offset\n * than the one originally provided. It supports both pre-offset adjustments\n * (before decoding) and post-offset adjustments (after decoding).\n *\n * The pre-offset function determines where decoding should start, while the\n * post-offset function adjusts where the next decoder should continue reading.\n *\n * For more details, see {@link offsetCodec}.\n *\n * @typeParam TTo - The type of the decoded value.\n *\n * @param decoder - The decoder to adjust.\n * @param config - An object specifying how the offset should be modified.\n * @returns A new decoder with adjusted offsets.\n *\n * @example\n * Moving the pre-offset forward by 2 bytes.\n * ```ts\n * const decoder = offsetDecoder(getU32Decoder(), {\n *     preOffset: ({ preOffset }) => preOffset + 2,\n * });\n * const bytes = new Uint8Array([0, 0, 42, 0]); // Value starts at offset 2\n * decoder.read(bytes, 0); // Actually reads from offset 2\n * ```\n *\n * @example\n * Moving the post-offset forward by 2 bytes.\n * ```ts\n * const decoder = offsetDecoder(getU32Decoder(), {\n *     postOffset: ({ postOffset }) => postOffset + 2,\n * });\n * const bytes = new Uint8Array([42, 0, 0, 0]);\n * const [value, nextOffset] = decoder.read(bytes, 0); // Next decoder starts at offset 6 instead of 4\n * ```\n *\n * @example\n * Using `wrapBytes` to read from the last 4 bytes of an array.\n * ```ts\n * const decoder = offsetDecoder(getU32Decoder(), {\n *     preOffset: ({ wrapBytes }) => wrapBytes(-4), // Moves offset to last 4 bytes of the array\n * });\n * const bytes = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 42]); // Value stored at the last 4 bytes\n * decoder.read(bytes, 0); // Reads from bytes.length - 4\n * ```\n *\n * @remarks\n * If you need both encoding and decoding offsets to be adjusted, use {@link offsetCodec}.\n *\n * @see {@link offsetCodec}\n * @see {@link offsetEncoder}\n */\nexport function offsetDecoder<TDecoder extends AnyDecoder>(decoder: TDecoder, config: OffsetConfig): TDecoder {\n    return createDecoder({\n        ...decoder,\n        read: (bytes, preOffset) => {\n            const wrapBytes = (offset: Offset) => modulo(offset, bytes.length);\n            const newPreOffset = config.preOffset ? config.preOffset({ bytes, preOffset, wrapBytes }) : preOffset;\n            assertByteArrayOffsetIsNotOutOfRange('offsetDecoder', newPreOffset, bytes.length);\n            const [value, postOffset] = decoder.read(bytes, newPreOffset);\n            const newPostOffset = config.postOffset\n                ? config.postOffset({ bytes, newPreOffset, postOffset, preOffset, wrapBytes })\n                : postOffset;\n            assertByteArrayOffsetIsNotOutOfRange('offsetDecoder', newPostOffset, bytes.length);\n            return [value, newPostOffset];\n        },\n    }) as TDecoder;\n}\n\n/**\n * Moves the offset of a given codec before and/or after encoding and decoding.\n *\n * This function allows a codec to encode and decode values at custom offsets\n * within a byte array. It modifies both the **pre-offset** (where encoding/decoding starts)\n * and the **post-offset** (where the next operation should continue).\n *\n * This is particularly useful when working with structured binary formats\n * that require skipping reserved bytes, inserting padding, or aligning fields at\n * specific locations.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n *\n * @param codec - The codec to adjust.\n * @param config - An object specifying how the offset should be modified.\n * @returns A new codec with adjusted offsets.\n *\n * @example\n * Moving the pre-offset forward by 2 bytes when encoding and decoding.\n * ```ts\n * const codec = offsetCodec(getU32Codec(), {\n *     preOffset: ({ preOffset }) => preOffset + 2,\n * });\n * const bytes = new Uint8Array(10);\n * codec.write(42, bytes, 0); // Actually written at offset 2\n * codec.read(bytes, 0);      // Actually read from offset 2\n * ```\n *\n * @example\n * Moving the post-offset forward by 2 bytes when encoding and decoding.\n * ```ts\n * const codec = offsetCodec(getU32Codec(), {\n *     postOffset: ({ postOffset }) => postOffset + 2,\n * });\n * const bytes = new Uint8Array(10);\n * codec.write(42, bytes, 0);\n * // Next encoding starts at offset 6 instead of 4\n * codec.read(bytes, 0);\n * // Next decoding starts at offset 6 instead of 4\n * ```\n *\n * @example\n * Using `wrapBytes` to loop around negative offsets.\n * ```ts\n * const codec = offsetCodec(getU32Codec(), {\n *     preOffset: ({ wrapBytes }) => wrapBytes(-4), // Moves offset to last 4 bytes\n * });\n * const bytes = new Uint8Array(10);\n * codec.write(42, bytes, 0); // Writes at bytes.length - 4\n * codec.read(bytes, 0); // Reads from bytes.length - 4\n * ```\n *\n * @remarks\n * If you only need to adjust offsets for encoding, use {@link offsetEncoder}.\n * If you only need to adjust offsets for decoding, use {@link offsetDecoder}.\n *\n * ```ts\n * const bytes = new Uint8Array(10);\n * offsetEncoder(getU32Encoder(), { preOffset: ({ preOffset }) => preOffset + 2 }).write(42, bytes, 0);\n * const [value] = offsetDecoder(getU32Decoder(), { preOffset: ({ preOffset }) => preOffset + 2 }).read(bytes, 0);\n * ```\n *\n * @see {@link offsetEncoder}\n * @see {@link offsetDecoder}\n */\nexport function offsetCodec<TCodec extends AnyCodec>(codec: TCodec, config: OffsetConfig): TCodec {\n    return combineCodec(offsetEncoder(codec, config), offsetDecoder(codec, config)) as TCodec;\n}\n\n/** A modulo function that handles negative dividends and zero divisors. */\nfunction modulo(dividend: number, divisor: number) {\n    if (divisor === 0) return 0;\n    return ((dividend % divisor) + divisor) % divisor;\n}\n", "import { SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH, SolanaError } from '@solana/errors';\n\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n} from './codec';\nimport { combineCodec } from './combine-codec';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyEncoder = Encoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyDecoder = Decoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyCodec = Codec<any>;\n\n/**\n * Updates the size of a given encoder.\n *\n * This function modifies the size of an encoder using a provided transformation function.\n * For fixed-size encoders, it updates the `fixedSize` property, and for variable-size\n * encoders, it adjusts the size calculation based on the encoded value.\n *\n * If the new size is negative, an error will be thrown.\n *\n * For more details, see {@link resizeCodec}.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TSize - The original fixed size of the encoded value.\n * @typeParam TNewSize - The new fixed size after resizing.\n *\n * @param encoder - The encoder whose size will be updated.\n * @param resize - A function that takes the current size and returns the new size.\n * @returns A new encoder with the updated size.\n *\n * @example\n * Increasing the size of a `u16` encoder by 2 bytes.\n * ```ts\n * const encoder = resizeEncoder(getU16Encoder(), size => size + 2);\n * encoder.encode(0xffff); // 0xffff0000 (two extra bytes added)\n * ```\n *\n * @example\n * Shrinking a `u32` encoder to only use 2 bytes.\n * ```ts\n * const encoder = resizeEncoder(getU32Encoder(), () => 2);\n * encoder.fixedSize; // 2\n * ```\n *\n * @see {@link resizeCodec}\n * @see {@link resizeDecoder}\n */\nexport function resizeEncoder<TFrom, TSize extends number, TNewSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize>,\n    resize: (size: TSize) => TNewSize,\n): FixedSizeEncoder<TFrom, TNewSize>;\nexport function resizeEncoder<TEncoder extends AnyEncoder>(\n    encoder: TEncoder,\n    resize: (size: number) => number,\n): TEncoder;\nexport function resizeEncoder<TEncoder extends AnyEncoder>(\n    encoder: TEncoder,\n    resize: (size: number) => number,\n): TEncoder {\n    if (isFixedSize(encoder)) {\n        const fixedSize = resize(encoder.fixedSize);\n        if (fixedSize < 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH, {\n                bytesLength: fixedSize,\n                codecDescription: 'resizeEncoder',\n            });\n        }\n        return createEncoder({ ...encoder, fixedSize }) as TEncoder;\n    }\n    return createEncoder({\n        ...encoder,\n        getSizeFromValue: value => {\n            const newSize = resize(encoder.getSizeFromValue(value));\n            if (newSize < 0) {\n                throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH, {\n                    bytesLength: newSize,\n                    codecDescription: 'resizeEncoder',\n                });\n            }\n            return newSize;\n        },\n    }) as TEncoder;\n}\n\n/**\n * Updates the size of a given decoder.\n *\n * This function modifies the size of a decoder using a provided transformation function.\n * For fixed-size decoders, it updates the `fixedSize` property to reflect the new size.\n * Variable-size decoders remain unchanged, as their size is determined dynamically.\n *\n * If the new size is negative, an error will be thrown.\n *\n * For more details, see {@link resizeCodec}.\n *\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The original fixed size of the decoded value.\n * @typeParam TNewSize - The new fixed size after resizing.\n *\n * @param decoder - The decoder whose size will be updated.\n * @param resize - A function that takes the current size and returns the new size.\n * @returns A new decoder with the updated size.\n *\n * @example\n * Expanding a `u16` decoder to read 4 bytes instead of 2.\n * ```ts\n * const decoder = resizeDecoder(getU16Decoder(), size => size + 2);\n * decoder.fixedSize; // 4\n * ```\n *\n * @example\n * Shrinking a `u32` decoder to only read 2 bytes.\n * ```ts\n * const decoder = resizeDecoder(getU32Decoder(), () => 2);\n * decoder.fixedSize; // 2\n * ```\n *\n * @see {@link resizeCodec}\n * @see {@link resizeEncoder}\n */\nexport function resizeDecoder<TFrom, TSize extends number, TNewSize extends number>(\n    decoder: FixedSizeDecoder<TFrom, TSize>,\n    resize: (size: TSize) => TNewSize,\n): FixedSizeDecoder<TFrom, TNewSize>;\nexport function resizeDecoder<TDecoder extends AnyDecoder>(\n    decoder: TDecoder,\n    resize: (size: number) => number,\n): TDecoder;\nexport function resizeDecoder<TDecoder extends AnyDecoder>(\n    decoder: TDecoder,\n    resize: (size: number) => number,\n): TDecoder {\n    if (isFixedSize(decoder)) {\n        const fixedSize = resize(decoder.fixedSize);\n        if (fixedSize < 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH, {\n                bytesLength: fixedSize,\n                codecDescription: 'resizeDecoder',\n            });\n        }\n        return createDecoder({ ...decoder, fixedSize }) as TDecoder;\n    }\n    return decoder;\n}\n\n/**\n * Updates the size of a given codec.\n *\n * This function modifies the size of both the codec using a provided\n * transformation function. It is useful for adjusting the allocated byte size for\n * encoding and decoding without altering the underlying data structure.\n *\n * If the new size is negative, an error will be thrown.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The original fixed size of the encoded/decoded value (for fixed-size codecs).\n * @typeParam TNewSize - The new fixed size after resizing (for fixed-size codecs).\n *\n * @param codec - The codec whose size will be updated.\n * @param resize - A function that takes the current size and returns the new size.\n * @returns A new codec with the updated size.\n *\n * @example\n * Expanding a `u16` codec from 2 to 4 bytes.\n * ```ts\n * const codec = resizeCodec(getU16Codec(), size => size + 2);\n * const bytes = codec.encode(0xffff); // 0xffff0000 (two extra bytes added)\n * const value = codec.decode(bytes);  // 0xffff (reads original two bytes)\n * ```\n *\n * @example\n * Shrinking a `u32` codec to only use 2 bytes.\n * ```ts\n * const codec = resizeCodec(getU32Codec(), () => 2);\n * codec.fixedSize; // 2\n * ```\n *\n * @remarks\n * If you only need to resize an encoder, use {@link resizeEncoder}.\n * If you only need to resize a decoder, use {@link resizeDecoder}.\n *\n * ```ts\n * const bytes = resizeEncoder(getU32Encoder(), (size) => size + 2).encode(0xffff);\n * const value = resizeDecoder(getU32Decoder(), (size) => size + 2).decode(bytes);\n * ```\n *\n * @see {@link resizeEncoder}\n * @see {@link resizeDecoder}\n */\nexport function resizeCodec<TFrom, TTo extends TFrom, TSize extends number, TNewSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize>,\n    resize: (size: TSize) => TNewSize,\n): FixedSizeCodec<TFrom, TTo, TNewSize>;\nexport function resizeCodec<TCodec extends AnyCodec>(codec: TCodec, resize: (size: number) => number): TCodec;\nexport function resizeCodec<TCodec extends AnyCodec>(codec: TCodec, resize: (size: number) => number): TCodec {\n    return combineCodec(resizeEncoder(codec, resize), resizeDecoder(codec, resize)) as TCodec;\n}\n", "import { Codec, Decoder, Encoder, Offset } from './codec';\nimport { combineCodec } from './combine-codec';\nimport { offsetDecoder, offsetEncoder } from './offset-codec';\nimport { resizeDecoder, resizeEncoder } from './resize-codec';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyEncoder = Encoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyDecoder = Decoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyCodec = Codec<any>;\n\n/**\n * Adds left padding to the given encoder, shifting the encoded value forward\n * by `offset` bytes whilst increasing the size of the encoder accordingly.\n *\n * For more details, see {@link padLeftCodec}.\n *\n * @typeParam TFrom - The type of the value to encode.\n *\n * @param encoder - The encoder to pad.\n * @param offset - The number of padding bytes to add before encoding.\n * @returns A new encoder with left padding applied.\n *\n * @example\n * ```ts\n * const encoder = padLeftEncoder(getU16Encoder(), 2);\n * const bytes = encoder.encode(0xffff); // 0x0000ffff (0xffff written at offset 2)\n * ```\n *\n * @see {@link padLeftCodec}\n * @see {@link padLeftDecoder}\n */\nexport function padLeftEncoder<TEncoder extends AnyEncoder>(encoder: TEncoder, offset: Offset): TEncoder {\n    return offsetEncoder(\n        resizeEncoder(encoder, size => size + offset),\n        { preOffset: ({ preOffset }) => preOffset + offset },\n    );\n}\n\n/**\n * Adds right padding to the given encoder, extending the encoded value by `offset`\n * bytes whilst increasing the size of the encoder accordingly.\n *\n * For more details, see {@link padRightCodec}.\n *\n * @typeParam TFrom - The type of the value to encode.\n *\n * @param encoder - The encoder to pad.\n * @param offset - The number of padding bytes to add after encoding.\n * @returns A new encoder with right padding applied.\n *\n * @example\n * ```ts\n * const encoder = padRightEncoder(getU16Encoder(), 2);\n * const bytes = encoder.encode(0xffff); // 0xffff0000 (two extra bytes added at the end)\n * ```\n *\n * @see {@link padRightCodec}\n * @see {@link padRightDecoder}\n */\nexport function padRightEncoder<TEncoder extends AnyEncoder>(encoder: TEncoder, offset: Offset): TEncoder {\n    return offsetEncoder(\n        resizeEncoder(encoder, size => size + offset),\n        { postOffset: ({ postOffset }) => postOffset + offset },\n    );\n}\n\n/**\n * Adds left padding to the given decoder, shifting the decoding position forward\n * by `offset` bytes whilst increasing the size of the decoder accordingly.\n *\n * For more details, see {@link padLeftCodec}.\n *\n * @typeParam TTo - The type of the decoded value.\n *\n * @param decoder - The decoder to pad.\n * @param offset - The number of padding bytes to skip before decoding.\n * @returns A new decoder with left padding applied.\n *\n * @example\n * ```ts\n * const decoder = padLeftDecoder(getU16Decoder(), 2);\n * const value = decoder.decode(new Uint8Array([0, 0, 0x12, 0x34])); // 0xffff (reads from offset 2)\n * ```\n *\n * @see {@link padLeftCodec}\n * @see {@link padLeftEncoder}\n */\nexport function padLeftDecoder<TDecoder extends AnyDecoder>(decoder: TDecoder, offset: Offset): TDecoder {\n    return offsetDecoder(\n        resizeDecoder(decoder, size => size + offset),\n        { preOffset: ({ preOffset }) => preOffset + offset },\n    );\n}\n\n/**\n * Adds right padding to the given decoder, extending the post-offset by `offset`\n * bytes whilst increasing the size of the decoder accordingly.\n *\n * For more details, see {@link padRightCodec}.\n *\n * @typeParam TTo - The type of the decoded value.\n *\n * @param decoder - The decoder to pad.\n * @param offset - The number of padding bytes to skip after decoding.\n * @returns A new decoder with right padding applied.\n *\n * @example\n * ```ts\n * const decoder = padRightDecoder(getU16Decoder(), 2);\n * const value = decoder.decode(new Uint8Array([0x12, 0x34, 0, 0])); // 0xffff (ignores trailing bytes)\n * ```\n *\n * @see {@link padRightCodec}\n * @see {@link padRightEncoder}\n */\nexport function padRightDecoder<TDecoder extends AnyDecoder>(decoder: TDecoder, offset: Offset): TDecoder {\n    return offsetDecoder(\n        resizeDecoder(decoder, size => size + offset),\n        { postOffset: ({ postOffset }) => postOffset + offset },\n    );\n}\n\n/**\n * Adds left padding to the given codec, shifting the encoding and decoding positions\n * forward by `offset` bytes whilst increasing the size of the codec accordingly.\n *\n * This ensures that values are read and written at a later position in the byte array,\n * while the padding bytes remain unused.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n *\n * @param codec - The codec to pad.\n * @param offset - The number of padding bytes to add before encoding and decoding.\n * @returns A new codec with left padding applied.\n *\n * @example\n * ```ts\n * const codec = padLeftCodec(getU16Codec(), 2);\n * const bytes = codec.encode(0xffff); // 0x0000ffff (0xffff written at offset 2)\n * const value = codec.decode(bytes);  // 0xffff (reads from offset 2)\n * ```\n *\n * @remarks\n * If you only need to apply padding for encoding, use {@link padLeftEncoder}.\n * If you only need to apply padding for decoding, use {@link padLeftDecoder}.\n *\n * ```ts\n * const bytes = padLeftEncoder(getU16Encoder(), 2).encode(0xffff);\n * const value = padLeftDecoder(getU16Decoder(), 2).decode(bytes);\n * ```\n *\n * @see {@link padLeftEncoder}\n * @see {@link padLeftDecoder}\n */\nexport function padLeftCodec<TCodec extends AnyCodec>(codec: TCodec, offset: Offset): TCodec {\n    return combineCodec(padLeftEncoder(codec, offset), padLeftDecoder(codec, offset)) as TCodec;\n}\n\n/**\n * Adds right padding to the given codec, extending the encoded and decoded value\n * by `offset` bytes whilst increasing the size of the codec accordingly.\n *\n * The extra bytes remain unused, ensuring that the next operation starts further\n * along the byte array.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n *\n * @param codec - The codec to pad.\n * @param offset - The number of padding bytes to add after encoding and decoding.\n * @returns A new codec with right padding applied.\n *\n * @example\n * ```ts\n * const codec = padRightCodec(getU16Codec(), 2);\n * const bytes = codec.encode(0xffff); // 0xffff0000 (two extra bytes added)\n * const value = codec.decode(bytes);  // 0xffff (ignores padding bytes)\n * ```\n *\n * @remarks\n * If you only need to apply padding for encoding, use {@link padRightEncoder}.\n * If you only need to apply padding for decoding, use {@link padRightDecoder}.\n *\n * ```ts\n * const bytes = padRightEncoder(getU16Encoder(), 2).encode(0xffff);\n * const value = padRightDecoder(getU16Decoder(), 2).decode(bytes);\n * ```\n *\n * @see {@link padRightEncoder}\n * @see {@link padRightDecoder}\n */\nexport function padRightCodec<TCodec extends AnyCodec>(codec: TCodec, offset: Offset): TCodec {\n    return combineCodec(padRightEncoder(codec, offset), padRightDecoder(codec, offset)) as TCodec;\n}\n", "import {\n    assertIsFixedSize,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n} from './codec';\nimport { combineCodec } from './combine-codec';\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\nfunction copySourceToTargetInReverse(\n    source: ReadonlyUint8Array,\n    target_WILL_MUTATE: Uint8Array,\n    sourceOffset: number,\n    sourceLength: number,\n    targetOffset: number = 0,\n) {\n    while (sourceOffset < --sourceLength) {\n        const leftValue = source[sourceOffset];\n        target_WILL_MUTATE[sourceOffset + targetOffset] = source[sourceLength];\n        target_WILL_MUTATE[sourceLength + targetOffset] = leftValue;\n        sourceOffset++;\n    }\n    if (sourceOffset === sourceLength) {\n        target_WILL_MUTATE[sourceOffset + targetOffset] = source[sourceOffset];\n    }\n}\n\n/**\n * Reverses the bytes of a fixed-size encoder.\n *\n * Given a `FixedSizeEncoder`, this function returns a new `FixedSizeEncoder` that\n * reverses the bytes within the fixed-size byte array when encoding.\n *\n * This can be useful to modify endianness or for other byte-order transformations.\n *\n * For more details, see {@link reverseCodec}.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TSize - The fixed size of the encoded value in bytes.\n *\n * @param encoder - The fixed-size encoder to reverse.\n * @returns A new encoder that writes bytes in reverse order.\n *\n * @example\n * Encoding a `u16` value in reverse order.\n * ```ts\n * const encoder = reverseEncoder(getU16Encoder({ endian: Endian.Big }));\n * const bytes = encoder.encode(0x1234); // 0x3412 (bytes are flipped)\n * ```\n *\n * @see {@link reverseCodec}\n * @see {@link reverseDecoder}\n */\nexport function reverseEncoder<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize>,\n): FixedSizeEncoder<TFrom, TSize> {\n    assertIsFixedSize(encoder);\n    return createEncoder({\n        ...encoder,\n        write: (value: TFrom, bytes, offset) => {\n            const newOffset = encoder.write(value, bytes, offset);\n            copySourceToTargetInReverse(\n                bytes /* source */,\n                bytes /* target_WILL_MUTATE */,\n                offset /* sourceOffset */,\n                offset + encoder.fixedSize /* sourceLength */,\n            );\n            return newOffset;\n        },\n    });\n}\n\n/**\n * Reverses the bytes of a fixed-size decoder.\n *\n * Given a `FixedSizeDecoder`, this function returns a new `FixedSizeDecoder` that\n * reverses the bytes within the fixed-size byte array before decoding.\n *\n * This can be useful to modify endianness or for other byte-order transformations.\n *\n * For more details, see {@link reverseCodec}.\n *\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the decoded value in bytes.\n *\n * @param decoder - The fixed-size decoder to reverse.\n * @returns A new decoder that reads bytes in reverse order.\n *\n * @example\n * Decoding a reversed `u16` value.\n * ```ts\n * const decoder = reverseDecoder(getU16Decoder({ endian: Endian.Big }));\n * const value = decoder.decode(new Uint8Array([0x34, 0x12])); // 0x1234 (bytes are flipped back)\n * ```\n *\n * @see {@link reverseCodec}\n * @see {@link reverseEncoder}\n */\nexport function reverseDecoder<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize>,\n): FixedSizeDecoder<TTo, TSize> {\n    assertIsFixedSize(decoder);\n    return createDecoder({\n        ...decoder,\n        read: (bytes, offset) => {\n            const reversedBytes = bytes.slice();\n            copySourceToTargetInReverse(\n                bytes /* source */,\n                reversedBytes /* target_WILL_MUTATE */,\n                offset /* sourceOffset */,\n                offset + decoder.fixedSize /* sourceLength */,\n            );\n            return decoder.read(reversedBytes, offset);\n        },\n    });\n}\n\n/**\n * Reverses the bytes of a fixed-size codec.\n *\n * Given a `FixedSizeCodec`, this function returns a new `FixedSizeCodec` that\n * reverses the bytes within the fixed-size byte array during encoding and decoding.\n *\n * This can be useful to modify endianness or for other byte-order transformations.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value.\n * @typeParam TSize - The fixed size of the encoded/decoded value in bytes.\n *\n * @param codec - The fixed-size codec to reverse.\n * @returns A new codec that encodes and decodes bytes in reverse order.\n *\n * @example\n * Reversing a `u16` codec.\n * ```ts\n * const codec = reverseCodec(getU16Codec({ endian: Endian.Big }));\n * const bytes = codec.encode(0x1234); // 0x3412 (bytes are flipped)\n * const value = codec.decode(bytes);  // 0x1234 (bytes are flipped back)\n * ```\n *\n * @remarks\n * If you only need to reverse an encoder, use {@link reverseEncoder}.\n * If you only need to reverse a decoder, use {@link reverseDecoder}.\n *\n * ```ts\n * const bytes = reverseEncoder(getU16Encoder()).encode(0x1234);\n * const value = reverseDecoder(getU16Decoder()).decode(bytes);\n * ```\n *\n * @see {@link reverseEncoder}\n * @see {@link reverseDecoder}\n */\nexport function reverseCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize>,\n): FixedSizeCodec<TFrom, TTo, TSize> {\n    return combineCodec(reverseEncoder(codec), reverseDecoder(codec));\n}\n", "import {\n    Code<PERSON>,\n    createCodec,\n    createDecoder,\n    createEncoder,\n    <PERSON>oder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isVariableSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Transforms an encoder by mapping its input values.\n *\n * This function takes an existing `Encoder<A>` and returns an `Encoder<B>`, allowing values of type `B`\n * to be converted into values of type `A` before encoding. The transformation is applied via the `unmap` function.\n *\n * This is useful for handling type conversions, applying default values, or structuring data before encoding.\n *\n * For more details, see {@link transformCodec}.\n *\n * @typeParam TOldFrom - The original type expected by the encoder.\n * @typeParam TNewFrom - The new type that will be transformed before encoding.\n *\n * @param encoder - The encoder to transform.\n * @param unmap - A function that converts values of `TNew<PERSON>rom` into `TOldFrom` before encoding.\n * @returns A new encoder that accepts `TN<PERSON><PERSON>rom` values and transforms them before encoding.\n *\n * @example\n * Encoding a string by counting its characters and storing the length as a `u32`.\n * ```ts\n * const encoder = transformEncoder(getU32Encoder(), (value: string) => value.length);\n * encoder.encode(\"hello\"); // 0x05000000 (stores length 5)\n * ```\n *\n * @see {@link transformCodec}\n * @see {@link transformDecoder}\n */\nexport function transformEncoder<TOldFrom, TNewFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TOldFrom, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): FixedSizeEncoder<TNewFrom, TSize>;\nexport function transformEncoder<TOldFrom, TNewFrom>(\n    encoder: VariableSizeEncoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): VariableSizeEncoder<TNewFrom>;\nexport function transformEncoder<TOldFrom, TNewFrom>(\n    encoder: Encoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Encoder<TNewFrom>;\nexport function transformEncoder<TOldFrom, TNewFrom>(\n    encoder: Encoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Encoder<TNewFrom> {\n    return createEncoder({\n        ...(isVariableSize(encoder)\n            ? { ...encoder, getSizeFromValue: (value: TNewFrom) => encoder.getSizeFromValue(unmap(value)) }\n            : encoder),\n        write: (value: TNewFrom, bytes, offset) => encoder.write(unmap(value), bytes, offset),\n    });\n}\n\n/**\n * Transforms a decoder by mapping its output values.\n *\n * This function takes an existing `Decoder<A>` and returns a `Decoder<B>`, allowing values of type `A`\n * to be converted into values of type `B` after decoding. The transformation is applied via the `map` function.\n *\n * This is useful for post-processing, type conversions, or enriching decoded data.\n *\n * For more details, see {@link transformCodec}.\n *\n * @typeParam TOldTo - The original type returned by the decoder.\n * @typeParam TNewTo - The new type that will be transformed after decoding.\n *\n * @param decoder - The decoder to transform.\n * @param map - A function that converts values of `TOldTo` into `TNewTo` after decoding.\n * @returns A new decoder that decodes into `TNewTo`.\n *\n * @example\n * Decoding a stored `u32` length into a string of `'x'` characters.\n * ```ts\n * const decoder = transformDecoder(getU32Decoder(), (length) => 'x'.repeat(length));\n * decoder.decode(new Uint8Array([0x05, 0x00, 0x00, 0x00])); // \"xxxxx\"\n * ```\n *\n * @see {@link transformCodec}\n * @see {@link transformEncoder}\n */\nexport function transformDecoder<TOldTo, TNewTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TOldTo, TSize>,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): FixedSizeDecoder<TNewTo, TSize>;\nexport function transformDecoder<TOldTo, TNewTo>(\n    decoder: VariableSizeDecoder<TOldTo>,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): VariableSizeDecoder<TNewTo>;\nexport function transformDecoder<TOldTo, TNewTo>(\n    decoder: Decoder<TOldTo>,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): Decoder<TNewTo>;\nexport function transformDecoder<TOldTo, TNewTo>(\n    decoder: Decoder<TOldTo>,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): Decoder<TNewTo> {\n    return createDecoder({\n        ...decoder,\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            const [value, newOffset] = decoder.read(bytes, offset);\n            return [map(value, bytes, offset), newOffset];\n        },\n    });\n}\n\n/**\n * Transforms a codec by mapping its input and output values.\n *\n * This function takes an existing `Codec<A, B>` and returns a `Codec<C, D>`, allowing:\n * - Values of type `C` to be transformed into `A` before encoding.\n * - Values of type `B` to be transformed into `D` after decoding.\n *\n * This is useful for adapting codecs to work with different representations, handling default values, or\n * converting between primitive and structured types.\n *\n * @typeParam TOldFrom - The original type expected by the codec.\n * @typeParam TNewFrom - The new type that will be transformed before encoding.\n * @typeParam TOldTo - The original type returned by the codec.\n * @typeParam TNewTo - The new type that will be transformed after decoding.\n *\n * @param codec - The codec to transform.\n * @param unmap - A function that converts values of `TNewFrom` into `TOldFrom` before encoding.\n * @param map - A function that converts values of `TOldTo` into `TNewTo` after decoding (optional).\n * @returns A new codec that encodes `TNewFrom` and decodes into `TNewTo`.\n *\n * @example\n * Mapping a `u32` codec to encode string lengths and decode them into `'x'` characters.\n * ```ts\n * const codec = transformCodec(\n *     getU32Codec(),\n *     (value: string) => value.length, // Encode string length\n *     (length) => 'x'.repeat(length)  // Decode length into a string of 'x's\n * );\n *\n * const bytes = codec.encode(\"hello\"); // 0x05000000 (stores length 5)\n * const value = codec.decode(bytes);   // \"xxxxx\"\n * ```\n *\n * @remarks\n * If only input transformation is needed, use {@link transformEncoder}.\n * If only output transformation is needed, use {@link transformDecoder}.\n *\n * ```ts\n * const bytes = transformEncoder(getU32Encoder(), (value: string) => value.length).encode(\"hello\");\n * const value = transformDecoder(getU32Decoder(), (length) => 'x'.repeat(length)).decode(bytes);\n * ```\n *\n * @see {@link transformEncoder}\n * @see {@link transformDecoder}\n */\nexport function transformCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom, TSize extends number>(\n    codec: FixedSizeCodec<TOldFrom, TTo, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): FixedSizeCodec<TNewFrom, TTo, TSize>;\nexport function transformCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom>(\n    codec: VariableSizeCodec<TOldFrom, TTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): VariableSizeCodec<TNewFrom, TTo>;\nexport function transformCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom>(\n    codec: Codec<TOldFrom, TTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Codec<TNewFrom, TTo>;\nexport function transformCodec<\n    TOldFrom,\n    TNewFrom,\n    TOldTo extends TOldFrom,\n    TNewTo extends TNewFrom,\n    TSize extends number,\n>(\n    codec: FixedSizeCodec<TOldFrom, TOldTo, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): FixedSizeCodec<TNewFrom, TNewTo, TSize>;\nexport function transformCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: VariableSizeCodec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): VariableSizeCodec<TNewFrom, TNewTo>;\nexport function transformCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: Codec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): Codec<TNewFrom, TNewTo>;\nexport function transformCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: Codec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map?: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): Codec<TNewFrom, TNewTo> {\n    return createCodec({\n        ...transformEncoder(codec, unmap),\n        read: map ? transformDecoder(codec, map).read : (codec.read as unknown as Decoder<TNewTo>['read']),\n    });\n}\n"]}