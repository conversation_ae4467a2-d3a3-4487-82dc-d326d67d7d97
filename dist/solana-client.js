"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SolanaClient = void 0;
const web3_js_1 = require("@solana/web3.js");
const spl_token_1 = require("@solana/spl-token");
class SolanaClient {
    constructor(network = 'devnet') {
        this.network = network;
        this.connection = this.createConnection(network);
    }
    /**
     * 创建网络连接
     */
    createConnection(network) {
        const rpcUrls = {
            'devnet': 'https://api.devnet.solana.com',
            'testnet': 'https://api.testnet.solana.com',
            'mainnet-beta': 'https://api.mainnet-beta.solana.com'
        };
        return new web3_js_1.Connection(rpcUrls[network], 'confirmed');
    }
    /**
     * 获取当前网络
     */
    getNetwork() {
        return this.network;
    }
    /**
     * 切换网络
     */
    switchNetwork(network) {
        this.network = network;
        this.connection = this.createConnection(network);
    }
    /**
     * 获取SOL余额
     */
    async getSolBalance(publicKey) {
        try {
            const balance = await this.connection.getBalance(publicKey);
            return balance / web3_js_1.LAMPORTS_PER_SOL;
        }
        catch (error) {
            throw new Error(`Failed to get SOL balance: ${error}`);
        }
    }
    /**
     * 获取SPL代币余额
     */
    async getSplTokenBalance(walletPublicKey, mintAddress) {
        try {
            const tokenAccounts = await this.connection.getTokenAccountsByOwner(walletPublicKey, { mint: mintAddress });
            if (tokenAccounts.value.length === 0) {
                return 0;
            }
            const tokenAccount = tokenAccounts.value[0];
            const accountInfo = await this.connection.getTokenAccountBalance(tokenAccount.pubkey);
            return parseFloat(accountInfo.value.uiAmount?.toString() || '0');
        }
        catch (error) {
            console.error('Error getting SPL token balance:', error);
            return 0;
        }
    }
    /**
     * 获取所有SPL代币余额
     */
    async getAllTokenBalances(walletPublicKey) {
        try {
            const tokenAccounts = await this.connection.getTokenAccountsByOwner(walletPublicKey, { programId: new web3_js_1.PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') });
            const balances = [];
            for (const tokenAccount of tokenAccounts.value) {
                try {
                    const accountInfo = await this.connection.getTokenAccountBalance(tokenAccount.pubkey);
                    if (accountInfo.value.uiAmount && accountInfo.value.uiAmount > 0) {
                        const accountData = await (0, spl_token_1.getAccount)(this.connection, tokenAccount.pubkey);
                        balances.push({
                            mint: accountData.mint.toBase58(),
                            amount: parseInt(accountInfo.value.amount),
                            decimals: accountInfo.value.decimals,
                            uiAmount: accountInfo.value.uiAmount
                        });
                    }
                }
                catch (error) {
                    // 跳过无法读取的代币账户
                    continue;
                }
            }
            return balances;
        }
        catch (error) {
            throw new Error(`Failed to get token balances: ${error}`);
        }
    }
    /**
     * 请求空投（仅限devnet和testnet）
     */
    async requestAirdrop(publicKey, amount) {
        if (this.network === 'mainnet-beta') {
            throw new Error('Airdrop is not available on mainnet');
        }
        try {
            const signature = await this.connection.requestAirdrop(publicKey, amount * web3_js_1.LAMPORTS_PER_SOL);
            await this.connection.confirmTransaction(signature);
            return signature;
        }
        catch (error) {
            throw new Error(`Airdrop failed: ${error}`);
        }
    }
    /**
     * 发送SOL
     */
    async sendSol(fromWallet, toAddress, amount) {
        try {
            const toPublicKey = new web3_js_1.PublicKey(toAddress);
            const transaction = new web3_js_1.Transaction().add(web3_js_1.SystemProgram.transfer({
                fromPubkey: fromWallet.keypair.publicKey,
                toPubkey: toPublicKey,
                lamports: amount * web3_js_1.LAMPORTS_PER_SOL,
            }));
            const signature = await (0, web3_js_1.sendAndConfirmTransaction)(this.connection, transaction, [fromWallet.keypair]);
            return signature;
        }
        catch (error) {
            throw new Error(`SOL transfer failed: ${error}`);
        }
    }
    /**
     * 发送SPL代币
     */
    async sendSplToken(fromWallet, toAddress, mintAddress, amount) {
        try {
            const toPublicKey = new web3_js_1.PublicKey(toAddress);
            const mintPublicKey = new web3_js_1.PublicKey(mintAddress);
            // 获取或创建发送方的关联代币账户
            const fromTokenAccount = await (0, spl_token_1.getOrCreateAssociatedTokenAccount)(this.connection, fromWallet.keypair, mintPublicKey, fromWallet.keypair.publicKey);
            // 获取或创建接收方的关联代币账户
            const toTokenAccount = await (0, spl_token_1.getOrCreateAssociatedTokenAccount)(this.connection, fromWallet.keypair, mintPublicKey, toPublicKey);
            // 获取代币信息以确定小数位数
            const mintInfo = await (0, spl_token_1.getMint)(this.connection, mintPublicKey);
            const transferAmount = amount * Math.pow(10, mintInfo.decimals);
            // 执行转账
            const signature = await (0, spl_token_1.transfer)(this.connection, fromWallet.keypair, fromTokenAccount.address, toTokenAccount.address, fromWallet.keypair, transferAmount);
            return signature;
        }
        catch (error) {
            throw new Error(`SPL token transfer failed: ${error}`);
        }
    }
    /**
     * 获取交易详情
     */
    async getTransaction(signature) {
        try {
            const transaction = await this.connection.getTransaction(signature);
            return transaction;
        }
        catch (error) {
            throw new Error(`Failed to get transaction: ${error}`);
        }
    }
    /**
     * 等待交易确认
     */
    async waitForConfirmation(signature) {
        await this.connection.confirmTransaction(signature);
    }
    /**
     * 获取网络状态
     */
    async getNetworkStatus() {
        try {
            const blockHeight = await this.connection.getBlockHeight();
            const slot = await this.connection.getSlot();
            return {
                network: this.network,
                blockHeight,
                slot,
                rpcUrl: this.connection.rpcEndpoint
            };
        }
        catch (error) {
            throw new Error(`Failed to get network status: ${error}`);
        }
    }
}
exports.SolanaClient = SolanaClient;
//# sourceMappingURL=solana-client.js.map