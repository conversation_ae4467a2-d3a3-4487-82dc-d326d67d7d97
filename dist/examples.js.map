{"version": 3, "file": "examples.js", "sourceRoot": "", "sources": ["../src/examples.ts"], "names": [], "mappings": ";;AAOA,sCAiBC;AAKD,4DA6BC;AAKD,gDAuCC;AAKD,0CAoCC;AAKD,wCAuBC;AA3KD,6CAA4C;AAC5C,qCAAwC;AACxC,2DAAuD;AAEvD;;GAEG;AACI,KAAK,UAAU,aAAa;IACjC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAEnC,QAAQ;IACR,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACzB,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;IAClC,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IAElD,WAAW;IACX,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC5B,MAAM,cAAc,GAAG,qBAAY,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IACvE,OAAO,CAAC,GAAG,CAAC,aAAa,cAAc,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IACxD,OAAO,CAAC,GAAG,CAAC,YAAY,MAAM,CAAC,UAAU,EAAE,KAAK,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAEjF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,wBAAwB,CAAC,MAAoB;IACjE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAEnC,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,QAAQ,CAAC,CAAC;IAEjD,SAAS;IACT,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC1B,MAAM,cAAc,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC5E,OAAO,CAAC,GAAG,CAAC,UAAU,cAAc,QAAQ,CAAC,CAAC;IAE9C,OAAO;IACP,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,YAAY,SAAS,EAAE,CAAC,CAAC;QAErC,OAAO;QACP,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,MAAM,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAE/C,UAAU;QACV,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,QAAQ,CAAC,CAAC;QAE7C,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC;QACrC,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,kBAAkB,CAAC,UAAwB;IAC/D,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IAEjC,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,QAAQ,CAAC,CAAC;IAEjD,UAAU;IACV,MAAM,QAAQ,GAAG,IAAI,qBAAY,EAAE,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC3B,OAAO,CAAC,GAAG,CAAC,aAAa,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAEpD,UAAU;IACV,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACzB,MAAM,WAAW,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC7E,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACzE,OAAO,CAAC,GAAG,CAAC,aAAa,WAAW,MAAM,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,QAAQ,CAAC,CAAC;IAE5C,OAAO;IACP,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,OAAO,CACvC,UAAU,EACV,QAAQ,CAAC,OAAO,CAAC,SAAS,EAC1B,GAAG,CACJ,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,YAAY,SAAS,EAAE,CAAC,CAAC;QAErC,UAAU;QACV,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,MAAM,cAAc,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAChF,MAAM,YAAY,GAAG,MAAM,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,aAAa,cAAc,MAAM,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,aAAa,YAAY,QAAQ,CAAC,CAAC;QAE/C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,MAAoB;IACxD,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAEnC,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,QAAQ,CAAC,CAAC;IAEjD,oBAAoB;IACpB,MAAM,QAAQ,GAAG,IAAI,mBAAS,CAAC,8CAA8C,CAAC,CAAC;IAE/E,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACvF,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO,IAAI,CAAC,CAAC;QAEvC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,UAAU;YACV,MAAM,QAAQ,GAAG,IAAI,qBAAY,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,aAAa,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAEpD,OAAO;YACP,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YAC7B,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,YAAY,CAC5C,MAAM,EACN,QAAQ,CAAC,OAAO,CAAC,SAAS,EAC1B,QAAQ,EACR,CAAC,CACF,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,YAAY,SAAS,IAAI,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,KAAK,IAAI,CAAC,CAAC;IAC5C,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,cAAc;IAClC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACvC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAE1C,IAAI,CAAC;QACH,YAAY;QACZ,MAAM,MAAM,GAAG,MAAM,aAAa,EAAE,CAAC;QAErC,aAAa;QACb,MAAM,cAAc,GAAG,MAAM,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAE9D,IAAI,cAAc,EAAE,CAAC;YACnB,aAAa;YACb,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAExD,eAAe;YACf,MAAM,eAAe,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACpC,CAAC;AACH,CAAC;AAED,YAAY;AACZ,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,cAAc,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC"}