"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SolanaOperations = void 0;
const web3_js_1 = require("@solana/web3.js");
const spl_token_1 = require("@solana/spl-token");
class SolanaOperations {
    constructor(cluster = 'devnet') {
        this.connection = new web3_js_1.Connection((0, web3_js_1.clusterApiUrl)(cluster), 'confirmed');
    }
    /**
     * 获取连接对象
     */
    getConnection() {
        return this.connection;
    }
    /**
     * 获取账户余额（SOL）
     */
    async getBalance(publicKey) {
        const balance = await this.connection.getBalance(publicKey);
        return balance / web3_js_1.LAMPORTS_PER_SOL;
    }
    /**
     * 请求空投（仅限devnet和testnet）
     */
    async requestAirdrop(publicKey, amount) {
        try {
            const signature = await this.connection.requestAirdrop(publicKey, amount * web3_js_1.LAMPORTS_PER_SOL);
            // 等待交易确认
            await this.connection.confirmTransaction(signature);
            return signature;
        }
        catch (error) {
            throw new Error(`Airdrop failed: ${error}`);
        }
    }
    /**
     * 发送SOL
     */
    async sendSol(fromWallet, toPublicKey, amount) {
        try {
            const transaction = new web3_js_1.Transaction().add(web3_js_1.SystemProgram.transfer({
                fromPubkey: fromWallet.keypair.publicKey,
                toPubkey: toPublicKey,
                lamports: amount * web3_js_1.LAMPORTS_PER_SOL,
            }));
            const signature = await (0, web3_js_1.sendAndConfirmTransaction)(this.connection, transaction, [fromWallet.keypair]);
            return signature;
        }
        catch (error) {
            throw new Error(`SOL transfer failed: ${error}`);
        }
    }
    /**
     * 获取SPL代币余额
     */
    async getSplTokenBalance(walletPublicKey, mintAddress) {
        try {
            const tokenAccounts = await this.connection.getTokenAccountsByOwner(walletPublicKey, { mint: mintAddress });
            if (tokenAccounts.value.length === 0) {
                return 0;
            }
            const tokenAccount = tokenAccounts.value[0];
            const accountInfo = await this.connection.getTokenAccountBalance(tokenAccount.pubkey);
            return parseFloat(accountInfo.value.uiAmount?.toString() || '0');
        }
        catch (error) {
            console.error('Error getting SPL token balance:', error);
            return 0;
        }
    }
    /**
     * 发送SPL代币
     */
    async sendSplToken(fromWallet, toPublicKey, mintAddress, amount) {
        try {
            // 获取或创建发送方的关联代币账户
            const fromTokenAccount = await (0, spl_token_1.getOrCreateAssociatedTokenAccount)(this.connection, fromWallet.keypair, mintAddress, fromWallet.keypair.publicKey);
            // 获取或创建接收方的关联代币账户
            const toTokenAccount = await (0, spl_token_1.getOrCreateAssociatedTokenAccount)(this.connection, fromWallet.keypair, mintAddress, toPublicKey);
            // 获取代币信息以确定小数位数
            const mintInfo = await (0, spl_token_1.getMint)(this.connection, mintAddress);
            const transferAmount = amount * Math.pow(10, mintInfo.decimals);
            // 执行转账
            const signature = await (0, spl_token_1.transfer)(this.connection, fromWallet.keypair, fromTokenAccount.address, toTokenAccount.address, fromWallet.keypair, transferAmount);
            return signature;
        }
        catch (error) {
            throw new Error(`SPL token transfer failed: ${error}`);
        }
    }
    /**
     * 获取交易详情
     */
    async getTransaction(signature) {
        try {
            const transaction = await this.connection.getTransaction(signature);
            return transaction;
        }
        catch (error) {
            throw new Error(`Failed to get transaction: ${error}`);
        }
    }
    /**
     * 等待交易确认
     */
    async waitForConfirmation(signature) {
        await this.connection.confirmTransaction(signature);
    }
}
exports.SolanaOperations = SolanaOperations;
//# sourceMappingURL=solana-operations.js.map