"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SolanaOperations = exports.SolanaWallet = void 0;
const web3_js_1 = require("@solana/web3.js");
const wallet_1 = require("./wallet");
Object.defineProperty(exports, "SolanaWallet", { enumerable: true, get: function () { return wallet_1.SolanaWallet; } });
const solana_operations_1 = require("./solana-operations");
Object.defineProperty(exports, "SolanaOperations", { enumerable: true, get: function () { return solana_operations_1.SolanaOperations; } });
async function main() {
    console.log('🚀 Solana DevNet 基本示例');
    console.log('========================\n');
    // 初始化Solana操作类
    const solanaOps = new solana_operations_1.SolanaOperations('devnet');
    try {
        // 1. 生成新钱包和地址
        console.log('1. 生成新钱包...');
        const wallet1 = new wallet_1.SolanaWallet();
        console.log(`   地址: ${wallet1.getAddress()}`);
        console.log(`   助记词: ${wallet1.getMnemonic()}`);
        console.log(`   私钥: ${wallet1.getPrivateKey()}\n`);
        // 2. 从助记词恢复钱包
        console.log('2. 从助记词恢复钱包...');
        const wallet2 = wallet_1.SolanaWallet.fromMnemonic(wallet1.getMnemonic());
        console.log(`   恢复的地址: ${wallet2.getAddress()}`);
        console.log(`   地址匹配: ${wallet1.getAddress() === wallet2.getAddress()}\n`);
        // 3. 检查初始余额
        console.log('3. 检查初始余额...');
        const initialBalance = await solanaOps.getBalance(wallet1.keypair.publicKey);
        console.log(`   钱包1余额: ${initialBalance} SOL\n`);
        // 4. 请求空投
        console.log('4. 请求空投 1 SOL...');
        try {
            const airdropSignature = await solanaOps.requestAirdrop(wallet1.keypair.publicKey, 1);
            console.log(`   空投交易签名: ${airdropSignature}`);
            // 等待确认
            await solanaOps.waitForConfirmation(airdropSignature);
            console.log('   空投已确认');
            // 检查余额
            const balanceAfterAirdrop = await solanaOps.getBalance(wallet1.keypair.publicKey);
            console.log(`   空投后余额: ${balanceAfterAirdrop} SOL\n`);
        }
        catch (error) {
            console.error(`   空投失败: ${error}\n`);
        }
        // 5. 创建第二个钱包用于转账测试
        console.log('5. 创建第二个钱包...');
        const wallet3 = new wallet_1.SolanaWallet();
        console.log(`   钱包3地址: ${wallet3.getAddress()}\n`);
        // 6. 发送SOL
        console.log('6. 发送 0.1 SOL 到钱包3...');
        try {
            const transferSignature = await solanaOps.sendSol(wallet1, wallet3.keypair.publicKey, 0.1);
            console.log(`   转账交易签名: ${transferSignature}`);
            // 检查余额
            const wallet1Balance = await solanaOps.getBalance(wallet1.keypair.publicKey);
            const wallet3Balance = await solanaOps.getBalance(wallet3.keypair.publicKey);
            console.log(`   钱包1余额: ${wallet1Balance} SOL`);
            console.log(`   钱包3余额: ${wallet3Balance} SOL\n`);
        }
        catch (error) {
            console.error(`   SOL转账失败: ${error}\n`);
        }
        // 7. SPL代币示例（使用USDC测试代币）
        console.log('7. SPL代币示例...');
        // USDC devnet mint address
        const usdcMintAddress = new web3_js_1.PublicKey('4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU');
        try {
            console.log('   检查USDC余额...');
            const usdcBalance1 = await solanaOps.getSplTokenBalance(wallet1.keypair.publicKey, usdcMintAddress);
            const usdcBalance3 = await solanaOps.getSplTokenBalance(wallet3.keypair.publicKey, usdcMintAddress);
            console.log(`   钱包1 USDC余额: ${usdcBalance1}`);
            console.log(`   钱包3 USDC余额: ${usdcBalance3}`);
            // 注意：在实际使用中，你需要先获得一些测试USDC代币
            // 这里只是演示如何检查余额和转账的代码结构
            if (usdcBalance1 > 0) {
                console.log('   尝试发送 1 USDC...');
                const splTransferSignature = await solanaOps.sendSplToken(wallet1, wallet3.keypair.publicKey, usdcMintAddress, 1);
                console.log(`   SPL代币转账签名: ${splTransferSignature}`);
            }
            else {
                console.log('   钱包1没有USDC代币，跳过转账测试');
            }
        }
        catch (error) {
            console.error(`   SPL代币操作失败: ${error}`);
        }
        console.log('\n✅ 示例完成！');
    }
    catch (error) {
        console.error('❌ 发生错误:', error);
    }
}
// 运行示例
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=index.js.map