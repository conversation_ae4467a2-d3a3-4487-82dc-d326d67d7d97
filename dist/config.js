"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const os = __importStar(require("os"));
const chalk_1 = __importDefault(require("chalk"));
class ConfigManager {
    constructor() {
        this.configDir = path.join(os.homedir(), '.solana-wallet-cli');
        this.configFile = path.join(this.configDir, 'config.json');
        this.ensureConfigDir();
        this.loadConfig();
    }
    /**
     * 确保配置目录存在
     */
    ensureConfigDir() {
        if (!fs.existsSync(this.configDir)) {
            fs.mkdirSync(this.configDir, { recursive: true });
        }
    }
    /**
     * 加载配置
     */
    loadConfig() {
        if (fs.existsSync(this.configFile)) {
            try {
                const data = fs.readFileSync(this.configFile, 'utf8');
                this.config = JSON.parse(data);
            }
            catch (error) {
                console.warn(chalk_1.default.yellow('⚠️  Failed to load config, using defaults'));
                this.config = this.getDefaultConfig();
            }
        }
        else {
            this.config = this.getDefaultConfig();
        }
    }
    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            wallets: [],
            defaultNetwork: 'devnet'
        };
    }
    /**
     * 保存配置
     */
    saveConfig() {
        try {
            fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Failed to save config: ${error}`));
        }
    }
    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    /**
     * 添加新钱包
     */
    addWallet(address, mnemonic, privateKey, network, alias) {
        const id = this.generateId();
        const wallet = {
            id,
            address,
            mnemonic,
            privateKey,
            createdAt: new Date().toISOString(),
            network,
            alias
        };
        this.config.wallets.push(wallet);
        this.config.currentWallet = id; // 设置为当前钱包
        this.saveConfig();
        console.log(chalk_1.default.green(`💾 Wallet saved with ID: ${id}`));
        if (alias) {
            console.log(chalk_1.default.cyan(`🏷️  Alias: ${alias}`));
        }
        return id;
    }
    /**
     * 获取当前钱包
     */
    getCurrentWallet() {
        if (!this.config.currentWallet) {
            return this.getLatestWallet();
        }
        const wallet = this.config.wallets.find(w => w.id === this.config.currentWallet);
        return wallet || this.getLatestWallet();
    }
    /**
     * 获取最新钱包
     */
    getLatestWallet() {
        if (this.config.wallets.length === 0) {
            return null;
        }
        return this.config.wallets.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];
    }
    /**
     * 获取所有钱包
     */
    getAllWallets() {
        return this.config.wallets.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }
    /**
     * 根据ID获取钱包
     */
    getWalletById(id) {
        return this.config.wallets.find(w => w.id === id) || null;
    }
    /**
     * 根据地址获取钱包
     */
    getWalletByAddress(address) {
        return this.config.wallets.find(w => w.address === address) || null;
    }
    /**
     * 根据别名获取钱包
     */
    getWalletByAlias(alias) {
        return this.config.wallets.find(w => w.alias === alias) || null;
    }
    /**
     * 设置当前钱包
     */
    setCurrentWallet(id) {
        const wallet = this.getWalletById(id);
        if (wallet) {
            this.config.currentWallet = id;
            this.saveConfig();
            console.log(chalk_1.default.green(`✅ Current wallet set to: ${wallet.address}`));
            return true;
        }
        return false;
    }
    /**
     * 删除钱包
     */
    deleteWallet(id) {
        const index = this.config.wallets.findIndex(w => w.id === id);
        if (index !== -1) {
            const wallet = this.config.wallets[index];
            this.config.wallets.splice(index, 1);
            // 如果删除的是当前钱包，重置当前钱包
            if (this.config.currentWallet === id) {
                this.config.currentWallet = this.config.wallets.length > 0 ? this.config.wallets[0].id : undefined;
            }
            this.saveConfig();
            console.log(chalk_1.default.green(`🗑️  Wallet deleted: ${wallet.address}`));
            return true;
        }
        return false;
    }
    /**
     * 设置钱包别名
     */
    setWalletAlias(id, alias) {
        const wallet = this.getWalletById(id);
        if (wallet) {
            wallet.alias = alias;
            this.saveConfig();
            console.log(chalk_1.default.green(`🏷️  Alias set for ${wallet.address}: ${alias}`));
            return true;
        }
        return false;
    }
    /**
     * 清除所有钱包
     */
    clearAllWallets() {
        this.config.wallets = [];
        this.config.currentWallet = undefined;
        this.saveConfig();
        console.log(chalk_1.default.green('🧹 All wallets cleared'));
    }
    /**
     * 获取配置文件路径
     */
    getConfigPath() {
        return this.configFile;
    }
    /**
     * 导出钱包数据
     */
    exportWallets() {
        return { ...this.config };
    }
    /**
     * 导入钱包数据
     */
    importWallets(data) {
        this.config = data;
        this.saveConfig();
        console.log(chalk_1.default.green(`📥 Imported ${data.wallets.length} wallets`));
    }
    /**
     * 获取统计信息
     */
    getStats() {
        const current = this.getCurrentWallet();
        const latest = this.getLatestWallet();
        return {
            total: this.config.wallets.length,
            current: current?.address || null,
            latest: latest?.address || null
        };
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=config.js.map