import { Connection, PublicKey } from '@solana/web3.js';
import { SolanaWallet } from './wallet';
export declare class SolanaOperations {
    private connection;
    constructor(cluster?: 'devnet' | 'testnet' | 'mainnet-beta');
    /**
     * 获取连接对象
     */
    getConnection(): Connection;
    /**
     * 获取账户余额（SOL）
     */
    getBalance(publicKey: PublicKey): Promise<number>;
    /**
     * 请求空投（仅限devnet和testnet）
     */
    requestAirdrop(publicKey: PublicKey, amount: number): Promise<string>;
    /**
     * 发送SOL
     */
    sendSol(fromWallet: SolanaWallet, toPublicKey: PublicKey, amount: number): Promise<string>;
    /**
     * 获取SPL代币余额
     */
    getSplTokenBalance(walletPublicKey: PublicKey, mintAddress: PublicKey): Promise<number>;
    /**
     * 发送SPL代币
     */
    sendSplToken(fromWallet: SolanaWallet, toPublicKey: PublicKey, mintAddress: PublicKey, amount: number): Promise<string>;
    /**
     * 获取交易详情
     */
    getTransaction(signature: string): Promise<import("@solana/web3.js").TransactionResponse | null>;
    /**
     * 等待交易确认
     */
    waitForConfirmation(signature: string): Promise<void>;
}
//# sourceMappingURL=solana-operations.d.ts.map