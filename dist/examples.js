"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.walletExample = walletExample;
exports.balanceAndAirdropExample = balanceAndAirdropExample;
exports.solTransferExample = solTransferExample;
exports.splTokenExample = splTokenExample;
exports.runAllExamples = runAllExamples;
const web3_js_1 = require("@solana/web3.js");
const wallet_1 = require("./wallet");
const solana_operations_1 = require("./solana-operations");
/**
 * 示例1: 钱包创建和管理
 */
async function walletExample() {
    console.log('=== 钱包创建和管理示例 ===\n');
    // 创建新钱包
    console.log('1. 创建新钱包:');
    const wallet = new wallet_1.SolanaWallet();
    console.log(`   地址: ${wallet.getAddress()}`);
    console.log(`   助记词: ${wallet.getMnemonic()}`);
    console.log(`   私钥: ${wallet.getPrivateKey()}\n`);
    // 从助记词恢复钱包
    console.log('2. 从助记词恢复钱包:');
    const restoredWallet = wallet_1.SolanaWallet.fromMnemonic(wallet.getMnemonic());
    console.log(`   恢复的地址: ${restoredWallet.getAddress()}`);
    console.log(`   地址匹配: ${wallet.getAddress() === restoredWallet.getAddress()}\n`);
    return wallet;
}
/**
 * 示例2: 余额查询和空投
 */
async function balanceAndAirdropExample(wallet) {
    console.log('=== 余额查询和空投示例 ===\n');
    const solanaOps = new solana_operations_1.SolanaOperations('devnet');
    // 查询初始余额
    console.log('1. 查询初始余额:');
    const initialBalance = await solanaOps.getBalance(wallet.keypair.publicKey);
    console.log(`   余额: ${initialBalance} SOL\n`);
    // 请求空投
    console.log('2. 请求空投 2 SOL:');
    try {
        const signature = await solanaOps.requestAirdrop(wallet.keypair.publicKey, 2);
        console.log(`   交易签名: ${signature}`);
        // 等待确认
        console.log('   等待交易确认...');
        await solanaOps.waitForConfirmation(signature);
        // 查询空投后余额
        const newBalance = await solanaOps.getBalance(wallet.keypair.publicKey);
        console.log(`   空投后余额: ${newBalance} SOL\n`);
        return true;
    }
    catch (error) {
        console.error(`   空投失败: ${error}\n`);
        return false;
    }
}
/**
 * 示例3: SOL转账
 */
async function solTransferExample(fromWallet) {
    console.log('=== SOL转账示例 ===\n');
    const solanaOps = new solana_operations_1.SolanaOperations('devnet');
    // 创建接收方钱包
    const toWallet = new wallet_1.SolanaWallet();
    console.log('1. 创建接收方钱包:');
    console.log(`   接收方地址: ${toWallet.getAddress()}\n`);
    // 查询转账前余额
    console.log('2. 转账前余额:');
    const fromBalance = await solanaOps.getBalance(fromWallet.keypair.publicKey);
    const toBalance = await solanaOps.getBalance(toWallet.keypair.publicKey);
    console.log(`   发送方余额: ${fromBalance} SOL`);
    console.log(`   接收方余额: ${toBalance} SOL\n`);
    // 执行转账
    console.log('3. 发送 0.5 SOL:');
    try {
        const signature = await solanaOps.sendSol(fromWallet, toWallet.keypair.publicKey, 0.5);
        console.log(`   交易签名: ${signature}`);
        // 查询转账后余额
        console.log('4. 转账后余额:');
        const newFromBalance = await solanaOps.getBalance(fromWallet.keypair.publicKey);
        const newToBalance = await solanaOps.getBalance(toWallet.keypair.publicKey);
        console.log(`   发送方余额: ${newFromBalance} SOL`);
        console.log(`   接收方余额: ${newToBalance} SOL\n`);
        return toWallet;
    }
    catch (error) {
        console.error(`   转账失败: ${error}\n`);
        return null;
    }
}
/**
 * 示例4: SPL代币操作
 */
async function splTokenExample(wallet) {
    console.log('=== SPL代币操作示例 ===\n');
    const solanaOps = new solana_operations_1.SolanaOperations('devnet');
    // 使用USDC devnet测试代币
    const usdcMint = new web3_js_1.PublicKey('4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU');
    console.log('1. 检查USDC代币余额:');
    try {
        const balance = await solanaOps.getSplTokenBalance(wallet.keypair.publicKey, usdcMint);
        console.log(`   USDC余额: ${balance}\n`);
        if (balance > 0) {
            // 创建接收方钱包
            const toWallet = new wallet_1.SolanaWallet();
            console.log('2. 创建接收方钱包:');
            console.log(`   接收方地址: ${toWallet.getAddress()}\n`);
            // 发送代币
            console.log('3. 发送 1 USDC:');
            const signature = await solanaOps.sendSplToken(wallet, toWallet.keypair.publicKey, usdcMint, 1);
            console.log(`   交易签名: ${signature}\n`);
        }
        else {
            console.log('   没有USDC代币可供转账\n');
            console.log('   提示: 你可以从以下地址获取测试USDC:');
            console.log('   https://spl-token-faucet.com/\n');
        }
    }
    catch (error) {
        console.error(`   SPL代币操作失败: ${error}\n`);
    }
}
/**
 * 运行所有示例
 */
async function runAllExamples() {
    console.log('🚀 Solana DevNet 完整示例\n');
    console.log('========================\n');
    try {
        // 示例1: 钱包管理
        const wallet = await walletExample();
        // 示例2: 余额和空投
        const airdropSuccess = await balanceAndAirdropExample(wallet);
        if (airdropSuccess) {
            // 示例3: SOL转账
            const receiverWallet = await solTransferExample(wallet);
            // 示例4: SPL代币操作
            await splTokenExample(wallet);
        }
        console.log('✅ 所有示例完成！');
    }
    catch (error) {
        console.error('❌ 示例执行失败:', error);
    }
}
// 如果直接运行此文件
if (require.main === module) {
    runAllExamples().catch(console.error);
}
//# sourceMappingURL=examples.js.map