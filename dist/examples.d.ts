import { SolanaWallet } from './wallet';
/**
 * 示例1: 钱包创建和管理
 */
export declare function walletExample(): Promise<SolanaWallet>;
/**
 * 示例2: 余额查询和空投
 */
export declare function balanceAndAirdropExample(wallet: SolanaWallet): Promise<boolean>;
/**
 * 示例3: SOL转账
 */
export declare function solTransferExample(fromWallet: SolanaWallet): Promise<SolanaWallet | null>;
/**
 * 示例4: SPL代币操作
 */
export declare function splTokenExample(wallet: SolanaWallet): Promise<void>;
/**
 * 运行所有示例
 */
export declare function runAllExamples(): Promise<void>;
//# sourceMappingURL=examples.d.ts.map