import { PublicKey } from '@solana/web3.js';
import { SolanaWallet } from './wallet';
export type NetworkType = 'devnet' | 'testnet' | 'mainnet-beta';
export interface TokenBalance {
    mint: string;
    amount: number;
    decimals: number;
    uiAmount: number;
}
export declare class SolanaClient {
    private connection;
    private network;
    constructor(network?: NetworkType);
    /**
     * 创建网络连接
     */
    private createConnection;
    /**
     * 获取当前网络
     */
    getNetwork(): NetworkType;
    /**
     * 切换网络
     */
    switchNetwork(network: NetworkType): void;
    /**
     * 获取SOL余额
     */
    getSolBalance(publicKey: PublicKey): Promise<number>;
    /**
     * 获取SPL代币余额
     */
    getSplTokenBalance(walletPublicKey: PublicKey, mintAddress: PublicKey): Promise<number>;
    /**
     * 获取所有SPL代币余额
     */
    getAllTokenBalances(walletPublicKey: PublicKey): Promise<TokenBalance[]>;
    /**
     * 请求空投（仅限devnet和testnet）
     */
    requestAirdrop(publicKey: PublicKey, amount: number): Promise<string>;
    /**
     * 发送SOL
     */
    sendSol(fromWallet: SolanaWallet, toAddress: string, amount: number): Promise<string>;
    /**
     * 发送SPL代币
     */
    sendSplToken(fromWallet: SolanaWallet, toAddress: string, mintAddress: string, amount: number): Promise<string>;
    /**
     * 获取交易详情
     */
    getTransaction(signature: string): Promise<import("@solana/web3.js").TransactionResponse | null>;
    /**
     * 等待交易确认
     */
    waitForConfirmation(signature: string): Promise<void>;
    /**
     * 获取网络状态
     */
    getNetworkStatus(): Promise<{
        network: NetworkType;
        blockHeight: number;
        slot: number;
        rpcUrl: string;
    }>;
}
