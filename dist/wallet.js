"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SolanaWallet = void 0;
const web3_js_1 = require("@solana/web3.js");
const bip39 = __importStar(require("bip39"));
const ed25519_hd_key_1 = require("ed25519-hd-key");
class SolanaWallet {
    constructor(mnemonic) {
        if (mnemonic) {
            this.mnemonic = mnemonic;
            this.keypair = this.deriveKeypairFromMnemonic(mnemonic);
        }
        else {
            this.mnemonic = this.generateMnemonic();
            this.keypair = this.deriveKeypairFromMnemonic(this.mnemonic);
        }
    }
    /**
     * 生成新的助记词
     */
    generateMnemonic() {
        return bip39.generateMnemonic();
    }
    /**
     * 从助记词派生密钥对
     */
    deriveKeypairFromMnemonic(mnemonic) {
        if (!bip39.validateMnemonic(mnemonic)) {
            throw new Error('Invalid mnemonic phrase');
        }
        const seed = bip39.mnemonicToSeedSync(mnemonic);
        const derivedSeed = (0, ed25519_hd_key_1.derivePath)("m/44'/501'/0'/0'", seed.toString('hex')).key;
        return web3_js_1.Keypair.fromSeed(derivedSeed);
    }
    /**
     * 获取钱包地址
     */
    getAddress() {
        return this.keypair.publicKey.toBase58();
    }
    /**
     * 获取助记词
     */
    getMnemonic() {
        return this.mnemonic;
    }
    /**
     * 获取私钥（Base58格式）
     */
    getPrivateKey() {
        return Buffer.from(this.keypair.secretKey).toString('base64');
    }
    /**
     * 获取公钥
     */
    getPublicKey() {
        return this.keypair.publicKey.toBase58();
    }
    /**
     * 获取完整钱包信息
     */
    getWalletInfo() {
        return {
            address: this.getAddress(),
            mnemonic: this.getMnemonic(),
            privateKey: this.getPrivateKey(),
            publicKey: this.getPublicKey()
        };
    }
    /**
     * 从私钥创建钱包
     */
    static fromPrivateKey(privateKey) {
        const secretKey = Buffer.from(privateKey, 'base64');
        const keypair = web3_js_1.Keypair.fromSecretKey(secretKey);
        const wallet = Object.create(SolanaWallet.prototype);
        wallet.keypair = keypair;
        wallet.mnemonic = '';
        return wallet;
    }
    /**
     * 从助记词创建钱包
     */
    static fromMnemonic(mnemonic) {
        return new SolanaWallet(mnemonic);
    }
    /**
     * 验证助记词
     */
    static validateMnemonic(mnemonic) {
        return bip39.validateMnemonic(mnemonic);
    }
    /**
     * 验证地址格式
     */
    static validateAddress(address) {
        try {
            new web3_js_1.PublicKey(address);
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.SolanaWallet = SolanaWallet;
//# sourceMappingURL=wallet.js.map