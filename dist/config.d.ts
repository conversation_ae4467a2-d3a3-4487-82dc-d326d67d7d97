export interface WalletRecord {
    id: string;
    address: string;
    mnemonic: string;
    privateKey: string;
    createdAt: string;
    network?: string;
    alias?: string;
}
export interface Config {
    wallets: WalletRecord[];
    currentWallet?: string;
    defaultNetwork: string;
}
export declare class ConfigManager {
    private configDir;
    private configFile;
    private config;
    constructor();
    /**
     * 确保配置目录存在
     */
    private ensureConfigDir;
    /**
     * 加载配置
     */
    private loadConfig;
    /**
     * 获取默认配置
     */
    private getDefaultConfig;
    /**
     * 保存配置
     */
    private saveConfig;
    /**
     * 生成唯一ID
     */
    private generateId;
    /**
     * 添加新钱包
     */
    addWallet(address: string, mnemonic: string, privateKey: string, network?: string, alias?: string): string;
    /**
     * 获取当前钱包
     */
    getCurrentWallet(): WalletRecord | null;
    /**
     * 获取最新钱包
     */
    getLatestWallet(): WalletRecord | null;
    /**
     * 获取所有钱包
     */
    getAllWallets(): WalletRecord[];
    /**
     * 根据ID获取钱包
     */
    getWalletById(id: string): WalletRecord | null;
    /**
     * 根据地址获取钱包
     */
    getWalletByAddress(address: string): WalletRecord | null;
    /**
     * 根据别名获取钱包
     */
    getWalletByAlias(alias: string): WalletRecord | null;
    /**
     * 设置当前钱包
     */
    setCurrentWallet(id: string): boolean;
    /**
     * 删除钱包
     */
    deleteWallet(id: string): boolean;
    /**
     * 设置钱包别名
     */
    setWalletAlias(id: string, alias: string): boolean;
    /**
     * 清除所有钱包
     */
    clearAllWallets(): void;
    /**
     * 获取配置文件路径
     */
    getConfigPath(): string;
    /**
     * 导出钱包数据
     */
    exportWallets(): Config;
    /**
     * 导入钱包数据
     */
    importWallets(data: Config): void;
    /**
     * 获取统计信息
     */
    getStats(): {
        total: number;
        current: string | null;
        latest: string | null;
    };
}
