#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.program = void 0;
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const web3_js_1 = require("@solana/web3.js");
const wallet_1 = require("./wallet");
const solana_client_1 = require("./solana-client");
const config_1 = require("./config");
const program = new commander_1.Command();
exports.program = program;
// 全局配置
let client;
let wallet;
let configManager;
/**
 * 初始化客户端和配置管理器
 */
function initClient(network) {
    client = new solana_client_1.SolanaClient(network);
    configManager = new config_1.ConfigManager();
    console.log(chalk_1.default.blue(`🌐 Connected to ${network.toUpperCase()}`));
}
/**
 * 加载钱包
 */
function loadWallet(mnemonic, privateKey, address) {
    if (privateKey) {
        wallet = wallet_1.SolanaWallet.fromPrivateKey(privateKey);
        console.log(chalk_1.default.green(`🔑 Wallet loaded from private key`));
    }
    else if (mnemonic) {
        if (!wallet_1.SolanaWallet.validateMnemonic(mnemonic)) {
            console.error(chalk_1.default.red('❌ Invalid mnemonic phrase'));
            process.exit(1);
        }
        wallet = wallet_1.SolanaWallet.fromMnemonic(mnemonic);
        console.log(chalk_1.default.green(`🔑 Wallet loaded from mnemonic`));
    }
    else if (address) {
        if (!wallet_1.SolanaWallet.validateAddress(address)) {
            console.error(chalk_1.default.red('❌ Invalid address format'));
            process.exit(1);
        }
        // 对于只读操作，我们不需要完整的钱包，只需要地址
        console.log(chalk_1.default.blue(`📍 Using address: ${formatAddress(address)}`));
        return address;
    }
    else {
        // 尝试从配置中加载最新钱包
        const savedWallet = configManager.getCurrentWallet();
        if (savedWallet) {
            wallet = wallet_1.SolanaWallet.fromMnemonic(savedWallet.mnemonic);
            console.log(chalk_1.default.green(`🔑 Loaded saved wallet: ${formatAddress(savedWallet.address)}`));
            if (savedWallet.alias) {
                console.log(chalk_1.default.cyan(`🏷️  Alias: ${savedWallet.alias}`));
            }
        }
        else {
            wallet = new wallet_1.SolanaWallet();
            console.log(chalk_1.default.green(`🔑 New wallet created`));
        }
    }
    return null;
}
/**
 * 格式化金额显示
 */
function formatAmount(amount, symbol = 'SOL') {
    return `${amount.toFixed(6)} ${symbol}`;
}
/**
 * 格式化地址显示
 */
function formatAddress(address) {
    return `${address.slice(0, 8)}...${address.slice(-8)}`;
}
// 设置程序信息
program
    .name('solana-wallet')
    .description('Solana Wallet CLI Tool')
    .version('1.0.0');
// 全局选项
program
    .option('-n, --network <network>', 'Network to use (devnet, testnet, mainnet-beta)', 'devnet')
    .option('-m, --mnemonic <mnemonic>', 'Mnemonic phrase to import wallet')
    .option('-k, --private-key <key>', 'Private key to import wallet')
    .option('-a, --address <address>', 'Wallet address to use for read-only operations');
// 创建新钱包
program
    .command('create')
    .description('Create a new wallet')
    .option('--alias <alias>', 'Set an alias for the wallet')
    .option('--no-save', 'Do not save the wallet to config')
    .action((options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network);
    const newWallet = new wallet_1.SolanaWallet();
    const walletInfo = newWallet.getWalletInfo();
    console.log(chalk_1.default.yellow('\n🎉 New Wallet Created!'));
    console.log(chalk_1.default.cyan('📍 Address:'), walletInfo.address);
    console.log(chalk_1.default.cyan('🔤 Mnemonic:'), walletInfo.mnemonic);
    console.log(chalk_1.default.cyan('🔐 Private Key:'), walletInfo.privateKey);
    // 保存钱包到配置（除非指定不保存）
    if (options.save !== false) {
        const walletId = configManager.addWallet(walletInfo.address, walletInfo.mnemonic, walletInfo.privateKey, globalOptions.network, options.alias);
        console.log(chalk_1.default.green(`💾 Wallet saved and set as current`));
    }
    console.log(chalk_1.default.red('\n⚠️  IMPORTANT: Save your mnemonic and private key securely!'));
});
// 显示钱包信息
program
    .command('info')
    .description('Show wallet information')
    .action(() => {
    const options = program.opts();
    initClient(options.network);
    if (options.address) {
        // 只显示地址信息
        if (!wallet_1.SolanaWallet.validateAddress(options.address)) {
            console.error(chalk_1.default.red('❌ Invalid address format'));
            process.exit(1);
        }
        console.log(chalk_1.default.yellow('\n💼 Address Information'));
        console.log(chalk_1.default.cyan('📍 Address:'), options.address);
        console.log(chalk_1.default.gray('ℹ️  Read-only mode: No private key or mnemonic available'));
    }
    else {
        loadWallet(options.mnemonic, options.privateKey);
        const walletInfo = wallet.getWalletInfo();
        console.log(chalk_1.default.yellow('\n💼 Wallet Information'));
        console.log(chalk_1.default.cyan('📍 Address:'), walletInfo.address);
        if (walletInfo.mnemonic) {
            console.log(chalk_1.default.cyan('🔤 Mnemonic:'), walletInfo.mnemonic);
        }
        console.log(chalk_1.default.cyan('🔐 Private Key:'), walletInfo.privateKey);
    }
});
// 查询余额
program
    .command('balance')
    .description('Check wallet balance')
    .option('-t, --token <mint>', 'SPL token mint address')
    .option('--addr <address>', 'Address to check (overrides global address)')
    .action(async (options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network);
    let targetAddress;
    // 优先级：命令行 --addr > 全局 -a > 钱包地址 > 保存的钱包地址
    if (options.addr) {
        if (!wallet_1.SolanaWallet.validateAddress(options.addr)) {
            console.error(chalk_1.default.red('❌ Invalid address format'));
            process.exit(1);
        }
        targetAddress = options.addr;
    }
    else if (globalOptions.address) {
        if (!wallet_1.SolanaWallet.validateAddress(globalOptions.address)) {
            console.error(chalk_1.default.red('❌ Invalid address format'));
            process.exit(1);
        }
        targetAddress = globalOptions.address;
    }
    else if (globalOptions.mnemonic || globalOptions.privateKey) {
        loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
        targetAddress = wallet.getAddress();
    }
    else {
        // 尝试使用保存的钱包
        const savedWallet = configManager.getCurrentWallet();
        if (savedWallet) {
            targetAddress = savedWallet.address;
            console.log(chalk_1.default.blue(`📍 Using saved wallet: ${formatAddress(savedWallet.address)}`));
            if (savedWallet.alias) {
                console.log(chalk_1.default.cyan(`🏷️  Alias: ${savedWallet.alias}`));
            }
        }
        else {
            console.error(chalk_1.default.red('❌ No wallet found. Please provide mnemonic, private key, address, or create a wallet first.'));
            process.exit(1);
        }
    }
    const publicKey = new web3_js_1.PublicKey(targetAddress);
    try {
        console.log(chalk_1.default.yellow(`\n💰 Balance for ${formatAddress(targetAddress)}`));
        if (options.token) {
            // 查询特定SPL代币余额
            const tokenMint = new web3_js_1.PublicKey(options.token);
            const balance = await client.getSplTokenBalance(publicKey, tokenMint);
            console.log(chalk_1.default.green(`🪙 Token Balance: ${formatAmount(balance, 'tokens')}`));
        }
        else {
            // 查询SOL余额
            const solBalance = await client.getSolBalance(publicKey);
            console.log(chalk_1.default.green(`💎 SOL Balance: ${formatAmount(solBalance)}`));
            // 查询所有SPL代币余额
            console.log(chalk_1.default.blue('\n🪙 SPL Token Balances:'));
            const tokenBalances = await client.getAllTokenBalances(publicKey);
            if (tokenBalances.length === 0) {
                console.log(chalk_1.default.gray('   No SPL tokens found'));
            }
            else {
                tokenBalances.forEach(token => {
                    console.log(chalk_1.default.cyan(`   ${formatAddress(token.mint)}: ${formatAmount(token.uiAmount, 'tokens')}`));
                });
            }
        }
    }
    catch (error) {
        console.error(chalk_1.default.red(`❌ Failed to get balance: ${error}`));
        process.exit(1);
    }
});
// 请求空投
program
    .command('airdrop')
    .description('Request SOL airdrop (devnet/testnet only)')
    .argument('<amount>', 'Amount of SOL to request')
    .option('--to <address>', 'Address to receive airdrop (default: current wallet)')
    .action(async (amount, options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network);
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
        console.error(chalk_1.default.red('❌ Invalid amount'));
        process.exit(1);
    }
    let targetAddress;
    let targetPublicKey;
    // 确定目标地址
    if (options.to) {
        if (!wallet_1.SolanaWallet.validateAddress(options.to)) {
            console.error(chalk_1.default.red('❌ Invalid target address'));
            process.exit(1);
        }
        targetAddress = options.to;
        targetPublicKey = new web3_js_1.PublicKey(options.to);
    }
    else if (globalOptions.address) {
        if (!wallet_1.SolanaWallet.validateAddress(globalOptions.address)) {
            console.error(chalk_1.default.red('❌ Invalid address format'));
            process.exit(1);
        }
        targetAddress = globalOptions.address;
        targetPublicKey = new web3_js_1.PublicKey(globalOptions.address);
    }
    else if (globalOptions.mnemonic || globalOptions.privateKey) {
        loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
        targetAddress = wallet.getAddress();
        targetPublicKey = wallet.keypair.publicKey;
    }
    else {
        // 尝试使用保存的钱包
        const savedWallet = configManager.getCurrentWallet();
        if (savedWallet) {
            targetAddress = savedWallet.address;
            targetPublicKey = new web3_js_1.PublicKey(savedWallet.address);
            console.log(chalk_1.default.blue(`📍 Using saved wallet: ${formatAddress(savedWallet.address)}`));
            if (savedWallet.alias) {
                console.log(chalk_1.default.cyan(`🏷️  Alias: ${savedWallet.alias}`));
            }
        }
        else {
            console.error(chalk_1.default.red('❌ No wallet found. Please provide address or create a wallet first.'));
            process.exit(1);
        }
    }
    try {
        console.log(chalk_1.default.blue(`\n🚁 Requesting ${formatAmount(amountNum)} airdrop to ${formatAddress(targetAddress)}...`));
        const signature = await client.requestAirdrop(targetPublicKey, amountNum);
        console.log(chalk_1.default.green(`✅ Airdrop successful!`));
        console.log(chalk_1.default.cyan(`📝 Transaction: ${signature}`));
        // 显示新余额
        const newBalance = await client.getSolBalance(targetPublicKey);
        console.log(chalk_1.default.green(`💰 New Balance: ${formatAmount(newBalance)}`));
    }
    catch (error) {
        console.error(chalk_1.default.red(`❌ Airdrop failed: ${error}`));
        process.exit(1);
    }
});
// 发送SOL
program
    .command('send-sol')
    .description('Send SOL to another address')
    .argument('<to>', 'Recipient address')
    .argument('<amount>', 'Amount of SOL to send')
    .option('--from <address>', 'Sender address (requires corresponding private key/mnemonic)')
    .action(async (to, amount, options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network);
    if (!wallet_1.SolanaWallet.validateAddress(to)) {
        console.error(chalk_1.default.red('❌ Invalid recipient address'));
        process.exit(1);
    }
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
        console.error(chalk_1.default.red('❌ Invalid amount'));
        process.exit(1);
    }
    // 确定发送方钱包
    let senderWallet;
    let senderAddress;
    if (options.from) {
        // 如果指定了发送方地址，必须有对应的私钥或助记词
        if (!globalOptions.mnemonic && !globalOptions.privateKey) {
            console.error(chalk_1.default.red('❌ Sender address specified but no private key or mnemonic provided'));
            process.exit(1);
        }
        loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
        if (wallet.getAddress() !== options.from) {
            console.error(chalk_1.default.red('❌ Provided private key/mnemonic does not match sender address'));
            process.exit(1);
        }
        senderWallet = wallet;
        senderAddress = options.from;
    }
    else if (globalOptions.address) {
        console.error(chalk_1.default.red('❌ Cannot send from read-only address. Please provide private key or mnemonic'));
        process.exit(1);
    }
    else {
        loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
        senderWallet = wallet;
        senderAddress = wallet.getAddress();
    }
    try {
        // 检查余额
        const balance = await client.getSolBalance(senderWallet.keypair.publicKey);
        if (balance < amountNum) {
            console.error(chalk_1.default.red(`❌ Insufficient balance. Current: ${formatAmount(balance)}`));
            process.exit(1);
        }
        console.log(chalk_1.default.blue(`\n💸 Sending ${formatAmount(amountNum)} from ${formatAddress(senderAddress)} to ${formatAddress(to)}...`));
        const signature = await client.sendSol(senderWallet, to, amountNum);
        console.log(chalk_1.default.green(`✅ Transfer successful!`));
        console.log(chalk_1.default.cyan(`📝 Transaction: ${signature}`));
        // 显示新余额
        const newBalance = await client.getSolBalance(senderWallet.keypair.publicKey);
        console.log(chalk_1.default.green(`💰 New Balance: ${formatAmount(newBalance)}`));
    }
    catch (error) {
        console.error(chalk_1.default.red(`❌ Transfer failed: ${error}`));
        process.exit(1);
    }
});
// 发送SPL代币
program
    .command('send-token')
    .description('Send SPL token to another address')
    .argument('<to>', 'Recipient address')
    .argument('<mint>', 'Token mint address')
    .argument('<amount>', 'Amount of tokens to send')
    .option('--from <address>', 'Sender address (requires corresponding private key/mnemonic)')
    .action(async (to, mint, amount, options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network);
    if (!wallet_1.SolanaWallet.validateAddress(to)) {
        console.error(chalk_1.default.red('❌ Invalid recipient address'));
        process.exit(1);
    }
    if (!wallet_1.SolanaWallet.validateAddress(mint)) {
        console.error(chalk_1.default.red('❌ Invalid token mint address'));
        process.exit(1);
    }
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
        console.error(chalk_1.default.red('❌ Invalid amount'));
        process.exit(1);
    }
    // 确定发送方钱包
    let senderWallet;
    let senderAddress;
    if (options.from) {
        // 如果指定了发送方地址，必须有对应的私钥或助记词
        if (!globalOptions.mnemonic && !globalOptions.privateKey) {
            console.error(chalk_1.default.red('❌ Sender address specified but no private key or mnemonic provided'));
            process.exit(1);
        }
        loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
        if (wallet.getAddress() !== options.from) {
            console.error(chalk_1.default.red('❌ Provided private key/mnemonic does not match sender address'));
            process.exit(1);
        }
        senderWallet = wallet;
        senderAddress = options.from;
    }
    else if (globalOptions.address) {
        console.error(chalk_1.default.red('❌ Cannot send from read-only address. Please provide private key or mnemonic'));
        process.exit(1);
    }
    else {
        loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
        senderWallet = wallet;
        senderAddress = wallet.getAddress();
    }
    try {
        // 检查代币余额
        const tokenBalance = await client.getSplTokenBalance(senderWallet.keypair.publicKey, new web3_js_1.PublicKey(mint));
        if (tokenBalance < amountNum) {
            console.error(chalk_1.default.red(`❌ Insufficient token balance. Current: ${formatAmount(tokenBalance, 'tokens')}`));
            process.exit(1);
        }
        console.log(chalk_1.default.blue(`\n🪙 Sending ${formatAmount(amountNum, 'tokens')} from ${formatAddress(senderAddress)} to ${formatAddress(to)}...`));
        const signature = await client.sendSplToken(senderWallet, to, mint, amountNum);
        console.log(chalk_1.default.green(`✅ Token transfer successful!`));
        console.log(chalk_1.default.cyan(`📝 Transaction: ${signature}`));
        // 显示新余额
        const newBalance = await client.getSplTokenBalance(senderWallet.keypair.publicKey, new web3_js_1.PublicKey(mint));
        console.log(chalk_1.default.green(`🪙 New Token Balance: ${formatAmount(newBalance, 'tokens')}`));
    }
    catch (error) {
        console.error(chalk_1.default.red(`❌ Token transfer failed: ${error}`));
        process.exit(1);
    }
});
// 网络状态
program
    .command('status')
    .description('Show network status')
    .action(async () => {
    const options = program.opts();
    initClient(options.network);
    try {
        const status = await client.getNetworkStatus();
        console.log(chalk_1.default.yellow('\n🌐 Network Status'));
        console.log(chalk_1.default.cyan('Network:'), status.network.toUpperCase());
        console.log(chalk_1.default.cyan('RPC URL:'), status.rpcUrl);
        console.log(chalk_1.default.cyan('Block Height:'), status.blockHeight);
        console.log(chalk_1.default.cyan('Slot:'), status.slot);
    }
    catch (error) {
        console.error(chalk_1.default.red(`❌ Failed to get network status: ${error}`));
        process.exit(1);
    }
});
// 验证地址
program
    .command('validate')
    .description('Validate Solana address or mnemonic')
    .option('--addr <address>', 'Address to validate')
    .option('--phrase <mnemonic>', 'Mnemonic to validate')
    .action((options) => {
    if (options.addr) {
        const isValid = wallet_1.SolanaWallet.validateAddress(options.addr);
        console.log(chalk_1.default.cyan('Address:'), options.addr);
        console.log(chalk_1.default.cyan('Valid:'), isValid ? chalk_1.default.green('✅ Yes') : chalk_1.default.red('❌ No'));
    }
    if (options.phrase) {
        const isValid = wallet_1.SolanaWallet.validateMnemonic(options.phrase);
        console.log(chalk_1.default.cyan('Mnemonic:'), options.phrase);
        console.log(chalk_1.default.cyan('Valid:'), isValid ? chalk_1.default.green('✅ Yes') : chalk_1.default.red('❌ No'));
    }
    if (!options.addr && !options.phrase) {
        console.error(chalk_1.default.red('❌ Please provide either --addr or --phrase option'));
        process.exit(1);
    }
});
// 交易查询
program
    .command('tx')
    .description('Get transaction details')
    .argument('<signature>', 'Transaction signature')
    .action(async (signature) => {
    const options = program.opts();
    initClient(options.network);
    try {
        console.log(chalk_1.default.blue(`\n🔍 Looking up transaction: ${signature}`));
        const tx = await client.getTransaction(signature);
        if (!tx) {
            console.log(chalk_1.default.yellow('❓ Transaction not found'));
            return;
        }
        console.log(chalk_1.default.yellow('\n📋 Transaction Details'));
        console.log(chalk_1.default.cyan('Signature:'), signature);
        console.log(chalk_1.default.cyan('Slot:'), tx.slot);
        console.log(chalk_1.default.cyan('Block Time:'), tx.blockTime ? new Date(tx.blockTime * 1000).toISOString() : 'Unknown');
        console.log(chalk_1.default.cyan('Fee:'), `${(tx.meta?.fee || 0) / web3_js_1.LAMPORTS_PER_SOL} SOL`);
        console.log(chalk_1.default.cyan('Status:'), tx.meta?.err ? chalk_1.default.red('Failed') : chalk_1.default.green('Success'));
        if (tx.meta?.err) {
            console.log(chalk_1.default.red('Error:'), JSON.stringify(tx.meta.err));
        }
    }
    catch (error) {
        console.error(chalk_1.default.red(`❌ Failed to get transaction: ${error}`));
        process.exit(1);
    }
});
// 钱包管理命令
program
    .command('wallets')
    .description('Manage saved wallets')
    .option('-l, --list', 'List all saved wallets')
    .option('-c, --current', 'Show current wallet')
    .option('-s, --set <id>', 'Set current wallet by ID')
    .option('-d, --delete <id>', 'Delete wallet by ID')
    .option('-a, --alias <id> <alias>', 'Set alias for wallet')
    .option('--clear', 'Clear all saved wallets')
    .option('--export', 'Export all wallets to JSON')
    .option('--import <file>', 'Import wallets from JSON file')
    .action((options) => {
    initClient('devnet'); // 网络不重要，只是为了初始化配置管理器
    if (options.list) {
        const wallets = configManager.getAllWallets();
        if (wallets.length === 0) {
            console.log(chalk_1.default.yellow('📭 No saved wallets found'));
            return;
        }
        console.log(chalk_1.default.yellow('\n💼 Saved Wallets:'));
        const current = configManager.getCurrentWallet();
        wallets.forEach((wallet, index) => {
            const isCurrent = current?.id === wallet.id;
            const prefix = isCurrent ? chalk_1.default.green('→') : ' ';
            const alias = wallet.alias ? chalk_1.default.cyan(` (${wallet.alias})`) : '';
            const network = wallet.network ? chalk_1.default.gray(` [${wallet.network}]`) : '';
            console.log(`${prefix} ${index + 1}. ${formatAddress(wallet.address)}${alias}${network}`);
            console.log(`     ID: ${wallet.id}`);
            console.log(`     Created: ${new Date(wallet.createdAt).toLocaleString()}`);
            console.log('');
        });
        const stats = configManager.getStats();
        console.log(chalk_1.default.blue(`📊 Total: ${stats.total} wallets`));
        if (stats.current) {
            console.log(chalk_1.default.green(`🎯 Current: ${formatAddress(stats.current)}`));
        }
    }
    if (options.current) {
        const current = configManager.getCurrentWallet();
        if (current) {
            console.log(chalk_1.default.yellow('\n🎯 Current Wallet:'));
            console.log(chalk_1.default.cyan('📍 Address:'), current.address);
            console.log(chalk_1.default.cyan('🆔 ID:'), current.id);
            if (current.alias) {
                console.log(chalk_1.default.cyan('🏷️  Alias:'), current.alias);
            }
            if (current.network) {
                console.log(chalk_1.default.cyan('🌐 Network:'), current.network);
            }
            console.log(chalk_1.default.cyan('📅 Created:'), new Date(current.createdAt).toLocaleString());
        }
        else {
            console.log(chalk_1.default.yellow('📭 No current wallet set'));
        }
    }
    if (options.set) {
        if (!configManager.setCurrentWallet(options.set)) {
            console.error(chalk_1.default.red(`❌ Wallet with ID ${options.set} not found`));
            process.exit(1);
        }
    }
    if (options.delete) {
        if (!configManager.deleteWallet(options.delete)) {
            console.error(chalk_1.default.red(`❌ Wallet with ID ${options.delete} not found`));
            process.exit(1);
        }
    }
    if (options.alias) {
        const [id, alias] = options.alias;
        if (!configManager.setWalletAlias(id, alias)) {
            console.error(chalk_1.default.red(`❌ Wallet with ID ${id} not found`));
            process.exit(1);
        }
    }
    if (options.clear) {
        configManager.clearAllWallets();
    }
    if (options.export) {
        const data = configManager.exportWallets();
        console.log(JSON.stringify(data, null, 2));
    }
    if (options.import) {
        try {
            const fs = require('fs');
            const data = JSON.parse(fs.readFileSync(options.import, 'utf8'));
            configManager.importWallets(data);
        }
        catch (error) {
            console.error(chalk_1.default.red(`❌ Failed to import wallets: ${error}`));
            process.exit(1);
        }
    }
});
// 解析命令行参数
program.parse();
//# sourceMappingURL=cli.js.map