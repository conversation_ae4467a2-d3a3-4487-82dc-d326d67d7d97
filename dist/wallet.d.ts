import { Keypair } from '@solana/web3.js';
export declare class SolanaWallet {
    keypair: Keypair;
    mnemonic: string;
    constructor(mnemonic?: string);
    /**
     * 生成新的助记词
     */
    private generateMnemonic;
    /**
     * 从助记词派生密钥对
     */
    private deriveKeypairFromMnemonic;
    /**
     * 获取公钥地址
     */
    getAddress(): string;
    /**
     * 获取助记词
     */
    getMnemonic(): string;
    /**
     * 获取私钥（Base58格式）
     */
    getPrivateKey(): string;
    /**
     * 从私钥创建钱包
     */
    static fromPrivateKey(privateKey: string): SolanaWallet;
    /**
     * 从助记词创建钱包
     */
    static fromMnemonic(mnemonic: string): SolanaWallet;
}
//# sourceMappingURL=wallet.d.ts.map