import { Keypair } from '@solana/web3.js';
export interface WalletInfo {
    address: string;
    mnemonic: string;
    privateKey: string;
    publicKey: string;
}
export declare class SolanaWallet {
    keypair: Keypair;
    mnemonic: string;
    constructor(mnemonic?: string);
    /**
     * 生成新的助记词
     */
    private generateMnemonic;
    /**
     * 从助记词派生密钥对
     */
    private deriveKeypairFromMnemonic;
    /**
     * 获取钱包地址
     */
    getAddress(): string;
    /**
     * 获取助记词
     */
    getMnemonic(): string;
    /**
     * 获取私钥（Base58格式）
     */
    getPrivateKey(): string;
    /**
     * 获取公钥
     */
    getPublicKey(): string;
    /**
     * 获取完整钱包信息
     */
    getWalletInfo(): WalletInfo;
    /**
     * 从私钥创建钱包
     */
    static fromPrivateKey(privateKey: string): SolanaWallet;
    /**
     * 从助记词创建钱包
     */
    static fromMnemonic(mnemonic: string): SolanaWallet;
    /**
     * 验证助记词
     */
    static validateMnemonic(mnemonic: string): boolean;
    /**
     * 验证地址格式
     */
    static validateAddress(address: string): boolean;
}
