# Solana Wallet CLI 使用示例

## 基本功能演示

### 1. 创建新钱包

```bash
npm run dev create
```

输出示例：
```
🌐 Connected to DEVNET

🎉 New Wallet Created!
📍 Address: 9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG
🔤 Mnemonic: sing live february cook tired viable try toddler liberty amused front humor
🔐 Private Key: 7pbNjByR7nIl4zd7gn8+BrG507yofkqdaVJ1DdJEGpd5Qgkv7nJodw9+HVtaz7H8jNNRloC8LxGbN/H7uk289Q==

⚠️  IMPORTANT: Save your mnemonic and private key securely!
```

### 2. 查看钱包信息

```bash
npm run dev -- -m "sing live february cook tired viable try toddler liberty amused front humor" info
```

输出示例：
```
🌐 Connected to DEVNET
🔑 Wallet loaded from mnemonic

💼 Wallet Information
📍 Address: 9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG
🔤 Mnemonic: sing live february cook tired viable try toddler liberty amused front humor
🔐 Private Key: 7pbNjByR7nIl4zd7gn8+BrG507yofkqdaVJ1DdJEGpd5Qgkv7nJodw9+HVtaz7H8jNNRloC8LxGbN/H7uk289Q==
```

### 3. 验证地址和助记词

验证地址：
```bash
npm run dev -- validate --addr "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"
```

验证助记词：
```bash
npm run dev -- validate --phrase "sing live february cook tired viable try toddler liberty amused front humor"
```

### 4. 网络切换示例

在不同网络上创建钱包：

DevNet（默认）：
```bash
npm run dev create
```

TestNet：
```bash
npm run dev -- -n testnet create
```

MainNet：
```bash
npm run dev -- -n mainnet-beta create
```

## 完整工作流程示例

### 场景1：DevNet测试流程

```bash
# 1. 创建新钱包
npm run dev create

# 2. 使用生成的助记词查看信息
npm run dev -- -m "your-generated-mnemonic" info

# 3. 查看初始余额
npm run dev -- -m "your-generated-mnemonic" balance

# 4. 请求空投
npm run dev -- -m "your-generated-mnemonic" airdrop 1

# 5. 再次查看余额
npm run dev -- -m "your-generated-mnemonic" balance

# 6. 创建第二个钱包作为接收方
npm run dev create

# 7. 发送SOL到第二个钱包
npm run dev -- -m "sender-mnemonic" send-sol "receiver-address" 0.1

# 8. 查看接收方余额
npm run dev -- balance -a "receiver-address"
```

### 场景2：跨网络操作

```bash
# 在DevNet上创建钱包
npm run dev -- -n devnet create

# 在TestNet上使用相同助记词
npm run dev -- -n testnet -m "your-mnemonic" info

# 在MainNet上查看余额（如果有的话）
npm run dev -- -n mainnet-beta -m "your-mnemonic" balance
```

## 命令参考

### 全局选项
- `-n, --network <network>`: 选择网络 (devnet, testnet, mainnet-beta)
- `-m, --mnemonic <mnemonic>`: 使用助记词导入钱包
- `-k, --private-key <key>`: 使用私钥导入钱包

### 命令列表

| 命令 | 描述 | 示例 |
|------|------|------|
| `create` | 创建新钱包 | `npm run dev create` |
| `info` | 显示钱包信息 | `npm run dev -- -m "mnemonic" info` |
| `balance` | 查询余额 | `npm run dev -- -m "mnemonic" balance` |
| `airdrop <amount>` | 请求空投 | `npm run dev -- -m "mnemonic" airdrop 1` |
| `send-sol <to> <amount>` | 发送SOL | `npm run dev -- -m "mnemonic" send-sol "address" 0.1` |
| `send-token <to> <mint> <amount>` | 发送SPL代币 | `npm run dev -- -m "mnemonic" send-token "address" "mint" 10` |
| `status` | 网络状态 | `npm run dev status` |
| `validate` | 验证地址/助记词 | `npm run dev -- validate --addr "address"` |
| `tx <signature>` | 查询交易 | `npm run dev -- tx "signature"` |

### Balance命令选项
- `-t, --token <mint>`: 查询特定SPL代币余额
- `-a, --address <address>`: 查询指定地址余额

### Validate命令选项
- `--addr <address>`: 验证地址
- `--phrase <mnemonic>`: 验证助记词

## 实际使用技巧

### 1. 使用环境变量存储敏感信息

创建 `.env` 文件：
```bash
WALLET_MNEMONIC="your twelve word mnemonic phrase here"
WALLET_PRIVATE_KEY="your-private-key"
```

然后在脚本中使用：
```bash
npm run dev -- -m "$WALLET_MNEMONIC" balance
```

### 2. 批量操作脚本

创建 `scripts/batch-operations.sh`：
```bash
#!/bin/bash

MNEMONIC="your mnemonic here"

echo "=== Checking balances on all networks ==="
echo "DevNet:"
npm run dev -- -n devnet -m "$MNEMONIC" balance

echo "TestNet:"
npm run dev -- -n testnet -m "$MNEMONIC" balance

echo "MainNet:"
npm run dev -- -n mainnet-beta -m "$MNEMONIC" balance
```

### 3. 安全实践

1. **永远不要在命令历史中暴露助记词**：
   ```bash
   # 不好的做法
   npm run dev -- -m "actual mnemonic words" balance
   
   # 好的做法
   read -s MNEMONIC
   npm run dev -- -m "$MNEMONIC" balance
   ```

2. **使用别名简化命令**：
   ```bash
   # 添加到 ~/.bashrc 或 ~/.zshrc
   alias sol-wallet="npm run dev --"
   alias sol-devnet="npm run dev -- -n devnet"
   alias sol-testnet="npm run dev -- -n testnet"
   alias sol-mainnet="npm run dev -- -n mainnet-beta"
   ```

3. **测试前验证**：
   ```bash
   # 总是先验证地址
   npm run dev -- validate --addr "target-address"
   
   # 然后进行小额测试
   npm run dev -- -m "$MNEMONIC" send-sol "target-address" 0.001
   ```

## 故障排除

### 常见错误和解决方案

1. **网络连接失败**：
   ```
   ❌ Failed to get balance: Error: Failed to get SOL balance: TypeError: fetch failed
   ```
   解决方案：检查网络连接，尝试不同的网络

2. **空投失败**：
   ```
   ❌ Airdrop failed: Error: airdrop request failed
   ```
   解决方案：
   - 确保使用DevNet或TestNet
   - 尝试更小的金额
   - 等待一段时间后重试

3. **余额不足**：
   ```
   ❌ Insufficient balance. Current: 0.000000 SOL
   ```
   解决方案：先请求空投或从其他地址转入SOL

4. **无效地址**：
   ```
   ❌ Invalid recipient address
   ```
   解决方案：使用validate命令验证地址格式

### 调试技巧

1. **查看详细错误信息**：
   大多数错误会显示详细的错误信息，仔细阅读可以找到问题所在

2. **分步测试**：
   - 先测试网络连接：`npm run dev status`
   - 再测试钱包：`npm run dev -- -m "mnemonic" info`
   - 最后测试操作：`npm run dev -- -m "mnemonic" balance`

3. **使用不同网络测试**：
   如果一个网络有问题，尝试其他网络
