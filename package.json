{"name": "solana-wallet-cli", "version": "1.0.0", "main": "dist/index.js", "bin": {"solana-wallet": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["solana", "wallet", "cli", "blockchain", "typescript"], "author": "", "license": "ISC", "description": "A command-line Solana wallet tool built with TypeScript", "dependencies": {"@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.2", "@types/node": "^24.0.10", "bip39": "^3.1.0", "chalk": "^5.4.1", "commander": "^14.0.0", "ed25519-hd-key": "^1.3.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}