# Solana DevNet TypeScript 使用指南

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 运行示例

#### 基本钱包功能测试（离线）
```bash
npx ts-node src/test-basic.ts
```

#### 完整离线演示
```bash
npx ts-node src/demo-offline.ts
```

#### 网络连接测试
```bash
npx ts-node src/test-network.ts
```

#### 完整在线示例（需要网络连接）
```bash
npm run dev
# 或
npx ts-node src/index.ts
```

## 核心功能

### 1. 钱包管理

#### 创建新钱包
```typescript
import { SolanaWallet } from './wallet';

// 生成新钱包
const wallet = new SolanaWallet();
console.log('地址:', wallet.getAddress());
console.log('助记词:', wallet.getMnemonic());
console.log('私钥:', wallet.getPrivateKey());
```

#### 从助记词恢复钱包
```typescript
const mnemonic = "your twelve word mnemonic phrase here";
const wallet = SolanaWallet.fromMnemonic(mnemonic);
```

#### 从私钥恢复钱包
```typescript
const privateKey = "your-base64-private-key";
const wallet = SolanaWallet.fromPrivateKey(privateKey);
```

### 2. Solana网络操作

#### 初始化连接
```typescript
import { SolanaOperations } from './solana-operations';

const solanaOps = new SolanaOperations('devnet');
```

#### 查询余额
```typescript
const balance = await solanaOps.getBalance(wallet.keypair.publicKey);
console.log(`余额: ${balance} SOL`);
```

#### 请求空投（仅限DevNet/TestNet）
```typescript
const signature = await solanaOps.requestAirdrop(wallet.keypair.publicKey, 1);
await solanaOps.waitForConfirmation(signature);
```

#### 发送SOL
```typescript
const toPublicKey = new PublicKey('目标地址');
const signature = await solanaOps.sendSol(wallet, toPublicKey, 0.1);
```

#### 发送SPL代币
```typescript
const mintAddress = new PublicKey('代币合约地址');
const signature = await solanaOps.sendSplToken(
  wallet, 
  toPublicKey, 
  mintAddress, 
  10
);
```

## 文件结构说明

```
src/
├── wallet.ts              # 钱包管理类
├── solana-operations.ts    # Solana网络操作类
├── index.ts               # 基本示例
├── examples.ts            # 详细示例
├── demo-offline.ts        # 离线演示
├── test-basic.ts          # 基本功能测试
└── test-network.ts        # 网络连接测试
```

## 常用命令

### 开发命令
```bash
# 开发模式运行
npm run dev

# 编译TypeScript
npm run build

# 运行编译后的代码
npm start
```

### 测试命令
```bash
# 基本功能测试
npx ts-node src/test-basic.ts

# 网络测试
npx ts-node src/test-network.ts

# 离线演示
npx ts-node src/demo-offline.ts

# 完整示例
npx ts-node src/examples.ts
```

## 网络配置

### DevNet（推荐用于开发）
- 免费空投
- 快速确认
- 重置频繁

### TestNet（推荐用于测试）
- 更稳定
- 接近主网环境

### MainNet（生产环境）
- 真实SOL
- 需要支付真实费用

## 获取测试代币

### SOL代币
1. 使用代码中的空投功能
2. 访问 [Solana Faucet](https://faucet.solana.com/)

### SPL代币
1. 访问 [SPL Token Faucet](https://spl-token-faucet.com/)
2. 输入你的钱包地址
3. 选择要获取的代币类型

## 常见问题

### Q: 空投失败怎么办？
A: DevNet空投有限制，可以：
1. 等待一段时间后重试
2. 使用在线水龙头
3. 尝试更小的金额

### Q: 网络连接失败？
A: 可能的解决方案：
1. 检查网络连接
2. 尝试不同的RPC端点
3. 使用VPN
4. 先运行离线演示

### Q: 交易失败？
A: 检查以下项目：
1. 账户余额是否足够
2. 网络是否拥堵
3. 交易费用是否足够
4. 目标地址是否有效

### Q: SPL代币转账失败？
A: 确保：
1. 有足够的代币余额
2. 有足够的SOL支付交易费用
3. 代币合约地址正确
4. 接收方地址有效

## 安全建议

1. **私钥安全**
   - 永远不要分享私钥
   - 不要在代码中硬编码私钥
   - 使用环境变量存储敏感信息

2. **助记词安全**
   - 安全备份助记词
   - 不要在线存储助记词
   - 使用硬件钱包（生产环境）

3. **网络安全**
   - 验证RPC端点的可信度
   - 在MainNet操作前充分测试
   - 小额测试后再进行大额操作

4. **代码安全**
   - 验证所有输入
   - 处理所有可能的错误
   - 实现适当的重试机制

## 进阶功能

### 自定义RPC端点
```typescript
const customOps = new SolanaOperations('devnet');
// 在构造函数中可以修改为自定义端点
```

### 批量操作
```typescript
// 可以扩展类来支持批量转账等操作
```

### 交易监控
```typescript
// 使用getTransaction方法监控交易状态
const txInfo = await solanaOps.getTransaction(signature);
```

## 扩展开发

这个项目提供了基础框架，你可以基于此扩展：

1. 添加更多SPL代币支持
2. 实现NFT操作
3. 添加DeFi协议集成
4. 实现多签钱包
5. 添加交易历史查询

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
