# 地址参数使用指南

本文档详细说明如何使用地址参数进行各种操作，无需每次都输入助记词或私钥。

## 概述

新版本支持以下几种使用方式：

1. **只读操作**：使用 `-a, --address` 参数进行查询操作
2. **指定发送方**：在转账时使用 `--from` 参数指定发送方地址
3. **指定接收方**：在空投时使用 `--to` 参数指定接收方地址

## 只读操作

### 查看地址信息

```bash
# 查看指定地址的基本信息
npm run dev -- -a "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG" info
```

输出：
```
🌐 Connected to DEVNET

💼 Address Information
📍 Address: 9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG
ℹ️  Read-only mode: No private key or mnemonic available
```

### 查询余额

```bash
# 查询指定地址的SOL和所有代币余额
npm run dev -- -a "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG" balance

# 查询指定地址的特定代币余额
npm run dev -- -a "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG" balance -t "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"

# 在不同网络上查询
npm run dev -- -n mainnet-beta -a "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG" balance
```

## 空投操作

### 空投到指定地址

```bash
# 空投到指定地址（不需要该地址的私钥）
npm run dev -- airdrop 1 --to "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"

# 使用全局地址参数
npm run dev -- -a "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG" airdrop 1

# 在TestNet上空投
npm run dev -- -n testnet airdrop 0.5 --to "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"
```

## 转账操作

### 发送SOL

```bash
# 基本转账（需要发送方的私钥/助记词）
npm run dev -- -m "your mnemonic" send-sol "recipient-address" 0.1

# 指定发送方地址（验证地址匹配）
npm run dev -- -m "your mnemonic" send-sol "recipient-address" 0.1 --from "sender-address"

# 使用私钥进行转账
npm run dev -- -k "your-private-key" send-sol "recipient-address" 0.1
```

### 发送SPL代币

```bash
# 基本代币转账
npm run dev -- -m "your mnemonic" send-token "recipient-address" "token-mint" 10

# 指定发送方地址
npm run dev -- -m "your mnemonic" send-token "recipient-address" "token-mint" 10 --from "sender-address"
```

## 实际使用场景

### 场景1：监控多个地址

```bash
#!/bin/bash

# 定义要监控的地址列表
ADDRESSES=(
    "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"
    "8ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEH"
    "7ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEI"
)

echo "=== 监控地址余额 ==="
for addr in "${ADDRESSES[@]}"; do
    echo "地址: $addr"
    npm run dev -- -a "$addr" balance
    echo "---"
done
```

### 场景2：批量空投

```bash
#!/bin/bash

# 批量空投到多个地址
RECIPIENTS=(
    "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"
    "8ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEH"
)

for recipient in "${RECIPIENTS[@]}"; do
    echo "空投到: $recipient"
    npm run dev -- airdrop 0.1 --to "$recipient"
    sleep 2  # 避免请求过快
done
```

### 场景3：地址验证和信息查看

```bash
#!/bin/bash

ADDRESS="9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"

# 1. 验证地址格式
echo "=== 验证地址 ==="
npm run dev -- validate --addr "$ADDRESS"

# 2. 查看地址信息
echo "=== 地址信息 ==="
npm run dev -- -a "$ADDRESS" info

# 3. 查看余额
echo "=== 余额信息 ==="
npm run dev -- -a "$ADDRESS" balance

# 4. 在不同网络上查看
echo "=== MainNet余额 ==="
npm run dev -- -n mainnet-beta -a "$ADDRESS" balance
```

## 命令对比

### 传统方式 vs 地址参数方式

| 操作 | 传统方式 | 地址参数方式 |
|------|----------|--------------|
| 查看余额 | `npm run dev -- -m "mnemonic" balance` | `npm run dev -- -a "address" balance` |
| 查看信息 | `npm run dev -- -m "mnemonic" info` | `npm run dev -- -a "address" info` |
| 空投 | `npm run dev -- -m "mnemonic" airdrop 1` | `npm run dev -- airdrop 1 --to "address"` |
| 转账 | `npm run dev -- -m "mnemonic" send-sol "to" 0.1` | `npm run dev -- -m "mnemonic" send-sol "to" 0.1 --from "from"` |

## 安全注意事项

### 只读操作
- 使用 `-a` 参数进行只读操作是安全的
- 不会暴露私钥或助记词
- 适合监控和查询场景

### 转账操作
- 转账仍需要私钥或助记词
- `--from` 参数用于验证地址匹配，不能替代私钥
- 如果提供的私钥/助记词与 `--from` 地址不匹配会报错

### 空投操作
- 可以空投到任何有效地址
- 不需要目标地址的私钥
- 适合测试和开发场景

## 错误处理

### 常见错误和解决方案

1. **地址格式错误**
   ```
   ❌ Invalid address format
   ```
   解决：使用 `validate --addr` 命令验证地址格式

2. **只读地址尝试转账**
   ```
   ❌ Cannot send from read-only address. Please provide private key or mnemonic
   ```
   解决：提供对应的私钥或助记词

3. **地址不匹配**
   ```
   ❌ Provided private key/mnemonic does not match sender address
   ```
   解决：确保私钥/助记词对应正确的发送方地址

## 最佳实践

1. **监控脚本**：使用地址参数创建监控脚本，避免在脚本中存储私钥
2. **批量查询**：利用地址参数批量查询多个地址的余额
3. **测试环境**：在测试环境中使用地址参数进行空投和测试
4. **安全分离**：将查询操作和转账操作分离，提高安全性

## 总结

地址参数功能提供了更灵活的使用方式：

- **查询操作**：无需私钥，使用地址即可查询余额和信息
- **空投操作**：可以空投到任何地址，无需目标地址的私钥
- **转账操作**：支持指定发送方地址，增加验证和灵活性
- **安全性**：只读操作不暴露敏感信息，转账操作仍需要私钥验证

这些功能特别适合开发、测试、监控和批量操作场景。
