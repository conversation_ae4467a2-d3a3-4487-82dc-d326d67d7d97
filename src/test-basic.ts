import { SolanaWallet } from './wallet';

async function testBasicWalletFunctions() {
  console.log('🧪 测试基本钱包功能');
  console.log('==================\n');

  try {
    // 1. 测试钱包生成
    console.log('1. 生成新钱包...');
    const wallet1 = new SolanaWallet();
    console.log(`   ✅ 地址: ${wallet1.getAddress()}`);
    console.log(`   ✅ 助记词: ${wallet1.getMnemonic()}`);
    console.log(`   ✅ 私钥长度: ${wallet1.getPrivateKey().length} 字符\n`);

    // 2. 测试从助记词恢复
    console.log('2. 从助记词恢复钱包...');
    const mnemonic = wallet1.getMnemonic();
    const wallet2 = SolanaWallet.fromMnemonic(mnemonic);
    console.log(`   ✅ 原地址: ${wallet1.getAddress()}`);
    console.log(`   ✅ 恢复地址: ${wallet2.getAddress()}`);
    console.log(`   ✅ 地址匹配: ${wallet1.getAddress() === wallet2.getAddress()}\n`);

    // 3. 测试多个钱包生成
    console.log('3. 生成多个不同钱包...');
    const wallet3 = new SolanaWallet();
    const wallet4 = new SolanaWallet();
    console.log(`   ✅ 钱包3地址: ${wallet3.getAddress()}`);
    console.log(`   ✅ 钱包4地址: ${wallet4.getAddress()}`);
    console.log(`   ✅ 地址不同: ${wallet3.getAddress() !== wallet4.getAddress()}\n`);

    // 4. 测试助记词验证
    console.log('4. 测试助记词验证...');
    const validMnemonic = wallet1.getMnemonic();
    const words = validMnemonic.split(' ');
    console.log(`   ✅ 助记词单词数: ${words.length}`);
    console.log(`   ✅ 助记词示例: ${words.slice(0, 3).join(' ')}...`);

    // 5. 测试私钥恢复
    console.log('5. 从私钥恢复钱包...');
    const privateKey = wallet1.getPrivateKey();
    const wallet5 = SolanaWallet.fromPrivateKey(privateKey);
    console.log(`   ✅ 原地址: ${wallet1.getAddress()}`);
    console.log(`   ✅ 私钥恢复地址: ${wallet5.getAddress()}`);
    console.log(`   ✅ 地址匹配: ${wallet1.getAddress() === wallet5.getAddress()}\n`);

    console.log('✅ 所有基本钱包功能测试通过！');
    
    return wallet1;
  } catch (error) {
    console.error('❌ 钱包功能测试失败:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testBasicWalletFunctions().catch(console.error);
}

export { testBasicWalletFunctions };
