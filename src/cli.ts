#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { SolanaWallet } from './wallet';
import { SolanaClient, NetworkType } from './solana-client';

const program = new Command();

// 全局配置
let client: SolanaClient;
let wallet: SolanaWallet;

/**
 * 初始化客户端
 */
function initClient(network: NetworkType) {
  client = new SolanaClient(network);
  console.log(chalk.blue(`🌐 Connected to ${network.toUpperCase()}`));
}

/**
 * 加载钱包
 */
function loadWallet(mnemonic?: string, privateKey?: string, address?: string) {
  if (privateKey) {
    wallet = SolanaWallet.fromPrivateKey(privateKey);
    console.log(chalk.green(`🔑 Wallet loaded from private key`));
  } else if (mnemonic) {
    if (!SolanaWallet.validateMnemonic(mnemonic)) {
      console.error(chalk.red('❌ Invalid mnemonic phrase'));
      process.exit(1);
    }
    wallet = SolanaWallet.fromMnemonic(mnemonic);
    console.log(chalk.green(`🔑 Wallet loaded from mnemonic`));
  } else if (address) {
    if (!SolanaWallet.validateAddress(address)) {
      console.error(chalk.red('❌ Invalid address format'));
      process.exit(1);
    }
    // 对于只读操作，我们不需要完整的钱包，只需要地址
    console.log(chalk.blue(`📍 Using address: ${formatAddress(address)}`));
    return address;
  } else {
    wallet = new SolanaWallet();
    console.log(chalk.green(`🔑 New wallet created`));
  }
  return null;
}

/**
 * 格式化金额显示
 */
function formatAmount(amount: number, symbol: string = 'SOL'): string {
  return `${amount.toFixed(6)} ${symbol}`;
}

/**
 * 格式化地址显示
 */
function formatAddress(address: string): string {
  return `${address.slice(0, 8)}...${address.slice(-8)}`;
}

// 设置程序信息
program
  .name('solana-wallet')
  .description('Solana Wallet CLI Tool')
  .version('1.0.0');

// 全局选项
program
  .option('-n, --network <network>', 'Network to use (devnet, testnet, mainnet-beta)', 'devnet')
  .option('-m, --mnemonic <mnemonic>', 'Mnemonic phrase to import wallet')
  .option('-k, --private-key <key>', 'Private key to import wallet')
  .option('-a, --address <address>', 'Wallet address to use for read-only operations');

// 创建新钱包
program
  .command('create')
  .description('Create a new wallet')
  .action(() => {
    const options = program.opts();
    initClient(options.network as NetworkType);
    
    const newWallet = new SolanaWallet();
    const walletInfo = newWallet.getWalletInfo();
    
    console.log(chalk.yellow('\n🎉 New Wallet Created!'));
    console.log(chalk.cyan('📍 Address:'), walletInfo.address);
    console.log(chalk.cyan('🔤 Mnemonic:'), walletInfo.mnemonic);
    console.log(chalk.cyan('🔐 Private Key:'), walletInfo.privateKey);
    console.log(chalk.red('\n⚠️  IMPORTANT: Save your mnemonic and private key securely!'));
  });

// 显示钱包信息
program
  .command('info')
  .description('Show wallet information')
  .action(() => {
    const options = program.opts();
    initClient(options.network as NetworkType);

    if (options.address) {
      // 只显示地址信息
      if (!SolanaWallet.validateAddress(options.address)) {
        console.error(chalk.red('❌ Invalid address format'));
        process.exit(1);
      }
      console.log(chalk.yellow('\n💼 Address Information'));
      console.log(chalk.cyan('📍 Address:'), options.address);
      console.log(chalk.gray('ℹ️  Read-only mode: No private key or mnemonic available'));
    } else {
      loadWallet(options.mnemonic, options.privateKey);

      const walletInfo = wallet.getWalletInfo();

      console.log(chalk.yellow('\n💼 Wallet Information'));
      console.log(chalk.cyan('📍 Address:'), walletInfo.address);
      if (walletInfo.mnemonic) {
        console.log(chalk.cyan('🔤 Mnemonic:'), walletInfo.mnemonic);
      }
      console.log(chalk.cyan('🔐 Private Key:'), walletInfo.privateKey);
    }
  });

// 查询余额
program
  .command('balance')
  .description('Check wallet balance')
  .option('-t, --token <mint>', 'SPL token mint address')
  .option('--addr <address>', 'Address to check (overrides global address)')
  .action(async (options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network as NetworkType);

    let targetAddress: string;

    // 优先级：命令行 --addr > 全局 -a > 钱包地址
    if (options.addr) {
      if (!SolanaWallet.validateAddress(options.addr)) {
        console.error(chalk.red('❌ Invalid address format'));
        process.exit(1);
      }
      targetAddress = options.addr;
    } else if (globalOptions.address) {
      if (!SolanaWallet.validateAddress(globalOptions.address)) {
        console.error(chalk.red('❌ Invalid address format'));
        process.exit(1);
      }
      targetAddress = globalOptions.address;
    } else {
      loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
      targetAddress = wallet.getAddress();
    }
    
    const publicKey = new PublicKey(targetAddress);
    
    try {
      console.log(chalk.yellow(`\n💰 Balance for ${formatAddress(targetAddress)}`));
      
      if (options.token) {
        // 查询特定SPL代币余额
        const tokenMint = new PublicKey(options.token);
        const balance = await client.getSplTokenBalance(publicKey, tokenMint);
        console.log(chalk.green(`🪙 Token Balance: ${formatAmount(balance, 'tokens')}`));
      } else {
        // 查询SOL余额
        const solBalance = await client.getSolBalance(publicKey);
        console.log(chalk.green(`💎 SOL Balance: ${formatAmount(solBalance)}`));
        
        // 查询所有SPL代币余额
        console.log(chalk.blue('\n🪙 SPL Token Balances:'));
        const tokenBalances = await client.getAllTokenBalances(publicKey);
        
        if (tokenBalances.length === 0) {
          console.log(chalk.gray('   No SPL tokens found'));
        } else {
          tokenBalances.forEach(token => {
            console.log(chalk.cyan(`   ${formatAddress(token.mint)}: ${formatAmount(token.uiAmount, 'tokens')}`));
          });
        }
      }
    } catch (error) {
      console.error(chalk.red(`❌ Failed to get balance: ${error}`));
      process.exit(1);
    }
  });

// 请求空投
program
  .command('airdrop')
  .description('Request SOL airdrop (devnet/testnet only)')
  .argument('<amount>', 'Amount of SOL to request')
  .option('--to <address>', 'Address to receive airdrop (default: current wallet)')
  .action(async (amount, options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network as NetworkType);

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      console.error(chalk.red('❌ Invalid amount'));
      process.exit(1);
    }

    let targetAddress: string;
    let targetPublicKey: PublicKey;

    // 确定目标地址
    if (options.to) {
      if (!SolanaWallet.validateAddress(options.to)) {
        console.error(chalk.red('❌ Invalid target address'));
        process.exit(1);
      }
      targetAddress = options.to;
      targetPublicKey = new PublicKey(options.to);
    } else if (globalOptions.address) {
      if (!SolanaWallet.validateAddress(globalOptions.address)) {
        console.error(chalk.red('❌ Invalid address format'));
        process.exit(1);
      }
      targetAddress = globalOptions.address;
      targetPublicKey = new PublicKey(globalOptions.address);
    } else {
      loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
      targetAddress = wallet.getAddress();
      targetPublicKey = wallet.keypair.publicKey;
    }

    try {
      console.log(chalk.blue(`\n🚁 Requesting ${formatAmount(amountNum)} airdrop to ${formatAddress(targetAddress)}...`));
      const signature = await client.requestAirdrop(targetPublicKey, amountNum);
      console.log(chalk.green(`✅ Airdrop successful!`));
      console.log(chalk.cyan(`📝 Transaction: ${signature}`));

      // 显示新余额
      const newBalance = await client.getSolBalance(targetPublicKey);
      console.log(chalk.green(`💰 New Balance: ${formatAmount(newBalance)}`));
    } catch (error) {
      console.error(chalk.red(`❌ Airdrop failed: ${error}`));
      process.exit(1);
    }
  });

// 发送SOL
program
  .command('send-sol')
  .description('Send SOL to another address')
  .argument('<to>', 'Recipient address')
  .argument('<amount>', 'Amount of SOL to send')
  .option('--from <address>', 'Sender address (requires corresponding private key/mnemonic)')
  .action(async (to, amount, options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network as NetworkType);

    if (!SolanaWallet.validateAddress(to)) {
      console.error(chalk.red('❌ Invalid recipient address'));
      process.exit(1);
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      console.error(chalk.red('❌ Invalid amount'));
      process.exit(1);
    }

    // 确定发送方钱包
    let senderWallet: SolanaWallet;
    let senderAddress: string;

    if (options.from) {
      // 如果指定了发送方地址，必须有对应的私钥或助记词
      if (!globalOptions.mnemonic && !globalOptions.privateKey) {
        console.error(chalk.red('❌ Sender address specified but no private key or mnemonic provided'));
        process.exit(1);
      }
      loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
      if (wallet.getAddress() !== options.from) {
        console.error(chalk.red('❌ Provided private key/mnemonic does not match sender address'));
        process.exit(1);
      }
      senderWallet = wallet;
      senderAddress = options.from;
    } else if (globalOptions.address) {
      console.error(chalk.red('❌ Cannot send from read-only address. Please provide private key or mnemonic'));
      process.exit(1);
    } else {
      loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
      senderWallet = wallet;
      senderAddress = wallet.getAddress();
    }

    try {
      // 检查余额
      const balance = await client.getSolBalance(senderWallet.keypair.publicKey);
      if (balance < amountNum) {
        console.error(chalk.red(`❌ Insufficient balance. Current: ${formatAmount(balance)}`));
        process.exit(1);
      }

      console.log(chalk.blue(`\n💸 Sending ${formatAmount(amountNum)} from ${formatAddress(senderAddress)} to ${formatAddress(to)}...`));
      const signature = await client.sendSol(senderWallet, to, amountNum);
      console.log(chalk.green(`✅ Transfer successful!`));
      console.log(chalk.cyan(`📝 Transaction: ${signature}`));

      // 显示新余额
      const newBalance = await client.getSolBalance(senderWallet.keypair.publicKey);
      console.log(chalk.green(`💰 New Balance: ${formatAmount(newBalance)}`));
    } catch (error) {
      console.error(chalk.red(`❌ Transfer failed: ${error}`));
      process.exit(1);
    }
  });

// 发送SPL代币
program
  .command('send-token')
  .description('Send SPL token to another address')
  .argument('<to>', 'Recipient address')
  .argument('<mint>', 'Token mint address')
  .argument('<amount>', 'Amount of tokens to send')
  .option('--from <address>', 'Sender address (requires corresponding private key/mnemonic)')
  .action(async (to, mint, amount, options) => {
    const globalOptions = program.opts();
    initClient(globalOptions.network as NetworkType);

    if (!SolanaWallet.validateAddress(to)) {
      console.error(chalk.red('❌ Invalid recipient address'));
      process.exit(1);
    }

    if (!SolanaWallet.validateAddress(mint)) {
      console.error(chalk.red('❌ Invalid token mint address'));
      process.exit(1);
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      console.error(chalk.red('❌ Invalid amount'));
      process.exit(1);
    }

    // 确定发送方钱包
    let senderWallet: SolanaWallet;
    let senderAddress: string;

    if (options.from) {
      // 如果指定了发送方地址，必须有对应的私钥或助记词
      if (!globalOptions.mnemonic && !globalOptions.privateKey) {
        console.error(chalk.red('❌ Sender address specified but no private key or mnemonic provided'));
        process.exit(1);
      }
      loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
      if (wallet.getAddress() !== options.from) {
        console.error(chalk.red('❌ Provided private key/mnemonic does not match sender address'));
        process.exit(1);
      }
      senderWallet = wallet;
      senderAddress = options.from;
    } else if (globalOptions.address) {
      console.error(chalk.red('❌ Cannot send from read-only address. Please provide private key or mnemonic'));
      process.exit(1);
    } else {
      loadWallet(globalOptions.mnemonic, globalOptions.privateKey);
      senderWallet = wallet;
      senderAddress = wallet.getAddress();
    }

    try {
      // 检查代币余额
      const tokenBalance = await client.getSplTokenBalance(
        senderWallet.keypair.publicKey,
        new PublicKey(mint)
      );

      if (tokenBalance < amountNum) {
        console.error(chalk.red(`❌ Insufficient token balance. Current: ${formatAmount(tokenBalance, 'tokens')}`));
        process.exit(1);
      }

      console.log(chalk.blue(`\n🪙 Sending ${formatAmount(amountNum, 'tokens')} from ${formatAddress(senderAddress)} to ${formatAddress(to)}...`));
      const signature = await client.sendSplToken(senderWallet, to, mint, amountNum);
      console.log(chalk.green(`✅ Token transfer successful!`));
      console.log(chalk.cyan(`📝 Transaction: ${signature}`));

      // 显示新余额
      const newBalance = await client.getSplTokenBalance(
        senderWallet.keypair.publicKey,
        new PublicKey(mint)
      );
      console.log(chalk.green(`🪙 New Token Balance: ${formatAmount(newBalance, 'tokens')}`));
    } catch (error) {
      console.error(chalk.red(`❌ Token transfer failed: ${error}`));
      process.exit(1);
    }
  });

// 网络状态
program
  .command('status')
  .description('Show network status')
  .action(async () => {
    const options = program.opts();
    initClient(options.network as NetworkType);

    try {
      const status = await client.getNetworkStatus();
      console.log(chalk.yellow('\n🌐 Network Status'));
      console.log(chalk.cyan('Network:'), status.network.toUpperCase());
      console.log(chalk.cyan('RPC URL:'), status.rpcUrl);
      console.log(chalk.cyan('Block Height:'), status.blockHeight);
      console.log(chalk.cyan('Slot:'), status.slot);
    } catch (error) {
      console.error(chalk.red(`❌ Failed to get network status: ${error}`));
      process.exit(1);
    }
  });

// 验证地址
program
  .command('validate')
  .description('Validate Solana address or mnemonic')
  .option('--addr <address>', 'Address to validate')
  .option('--phrase <mnemonic>', 'Mnemonic to validate')
  .action((options) => {
    if (options.addr) {
      const isValid = SolanaWallet.validateAddress(options.addr);
      console.log(chalk.cyan('Address:'), options.addr);
      console.log(chalk.cyan('Valid:'), isValid ? chalk.green('✅ Yes') : chalk.red('❌ No'));
    }

    if (options.phrase) {
      const isValid = SolanaWallet.validateMnemonic(options.phrase);
      console.log(chalk.cyan('Mnemonic:'), options.phrase);
      console.log(chalk.cyan('Valid:'), isValid ? chalk.green('✅ Yes') : chalk.red('❌ No'));
    }

    if (!options.addr && !options.phrase) {
      console.error(chalk.red('❌ Please provide either --addr or --phrase option'));
      process.exit(1);
    }
  });

// 交易查询
program
  .command('tx')
  .description('Get transaction details')
  .argument('<signature>', 'Transaction signature')
  .action(async (signature) => {
    const options = program.opts();
    initClient(options.network as NetworkType);

    try {
      console.log(chalk.blue(`\n🔍 Looking up transaction: ${signature}`));
      const tx = await client.getTransaction(signature);

      if (!tx) {
        console.log(chalk.yellow('❓ Transaction not found'));
        return;
      }

      console.log(chalk.yellow('\n📋 Transaction Details'));
      console.log(chalk.cyan('Signature:'), signature);
      console.log(chalk.cyan('Slot:'), tx.slot);
      console.log(chalk.cyan('Block Time:'), tx.blockTime ? new Date(tx.blockTime * 1000).toISOString() : 'Unknown');
      console.log(chalk.cyan('Fee:'), `${(tx.meta?.fee || 0) / LAMPORTS_PER_SOL} SOL`);
      console.log(chalk.cyan('Status:'), tx.meta?.err ? chalk.red('Failed') : chalk.green('Success'));

      if (tx.meta?.err) {
        console.log(chalk.red('Error:'), JSON.stringify(tx.meta.err));
      }
    } catch (error) {
      console.error(chalk.red(`❌ Failed to get transaction: ${error}`));
      process.exit(1);
    }
  });

// 解析命令行参数
program.parse();

export { program };
