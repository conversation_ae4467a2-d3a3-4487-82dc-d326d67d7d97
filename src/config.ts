import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import chalk from 'chalk';

export interface WalletRecord {
  id: string;
  address: string;
  mnemonic: string;
  privateKey: string;
  createdAt: string;
  network?: string;
  alias?: string;
}

export interface Config {
  wallets: WalletRecord[];
  currentWallet?: string; // wallet id
  defaultNetwork: string;
}

export class ConfigManager {
  private configDir: string;
  private configFile: string;
  private config!: Config;

  constructor() {
    this.configDir = path.join(os.homedir(), '.solana-wallet-cli');
    this.configFile = path.join(this.configDir, 'config.json');
    this.ensureConfigDir();
    this.loadConfig();
  }

  /**
   * 确保配置目录存在
   */
  private ensureConfigDir(): void {
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    if (fs.existsSync(this.configFile)) {
      try {
        const data = fs.readFileSync(this.configFile, 'utf8');
        this.config = JSON.parse(data);
      } catch (error) {
        console.warn(chalk.yellow('⚠️  Failed to load config, using defaults'));
        this.config = this.getDefaultConfig();
      }
    } else {
      this.config = this.getDefaultConfig();
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): Config {
    return {
      wallets: [],
      defaultNetwork: 'devnet'
    };
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error(chalk.red(`❌ Failed to save config: ${error}`));
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 添加新钱包
   */
  public addWallet(address: string, mnemonic: string, privateKey: string, network?: string, alias?: string): string {
    const id = this.generateId();
    const wallet: WalletRecord = {
      id,
      address,
      mnemonic,
      privateKey,
      createdAt: new Date().toISOString(),
      network,
      alias
    };

    this.config.wallets.push(wallet);
    this.config.currentWallet = id; // 设置为当前钱包
    this.saveConfig();

    console.log(chalk.green(`💾 Wallet saved with ID: ${id}`));
    if (alias) {
      console.log(chalk.cyan(`🏷️  Alias: ${alias}`));
    }

    return id;
  }

  /**
   * 获取当前钱包
   */
  public getCurrentWallet(): WalletRecord | null {
    if (!this.config.currentWallet) {
      return this.getLatestWallet();
    }

    const wallet = this.config.wallets.find(w => w.id === this.config.currentWallet);
    return wallet || this.getLatestWallet();
  }

  /**
   * 获取最新钱包
   */
  public getLatestWallet(): WalletRecord | null {
    if (this.config.wallets.length === 0) {
      return null;
    }

    return this.config.wallets.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];
  }

  /**
   * 获取所有钱包
   */
  public getAllWallets(): WalletRecord[] {
    return this.config.wallets.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  /**
   * 根据ID获取钱包
   */
  public getWalletById(id: string): WalletRecord | null {
    return this.config.wallets.find(w => w.id === id) || null;
  }

  /**
   * 根据地址获取钱包
   */
  public getWalletByAddress(address: string): WalletRecord | null {
    return this.config.wallets.find(w => w.address === address) || null;
  }

  /**
   * 根据别名获取钱包
   */
  public getWalletByAlias(alias: string): WalletRecord | null {
    return this.config.wallets.find(w => w.alias === alias) || null;
  }

  /**
   * 设置当前钱包
   */
  public setCurrentWallet(id: string): boolean {
    const wallet = this.getWalletById(id);
    if (wallet) {
      this.config.currentWallet = id;
      this.saveConfig();
      console.log(chalk.green(`✅ Current wallet set to: ${wallet.address}`));
      return true;
    }
    return false;
  }

  /**
   * 删除钱包
   */
  public deleteWallet(id: string): boolean {
    const index = this.config.wallets.findIndex(w => w.id === id);
    if (index !== -1) {
      const wallet = this.config.wallets[index];
      this.config.wallets.splice(index, 1);
      
      // 如果删除的是当前钱包，重置当前钱包
      if (this.config.currentWallet === id) {
        this.config.currentWallet = this.config.wallets.length > 0 ? this.config.wallets[0].id : undefined;
      }
      
      this.saveConfig();
      console.log(chalk.green(`🗑️  Wallet deleted: ${wallet.address}`));
      return true;
    }
    return false;
  }

  /**
   * 设置钱包别名
   */
  public setWalletAlias(id: string, alias: string): boolean {
    const wallet = this.getWalletById(id);
    if (wallet) {
      wallet.alias = alias;
      this.saveConfig();
      console.log(chalk.green(`🏷️  Alias set for ${wallet.address}: ${alias}`));
      return true;
    }
    return false;
  }

  /**
   * 清除所有钱包
   */
  public clearAllWallets(): void {
    this.config.wallets = [];
    this.config.currentWallet = undefined;
    this.saveConfig();
    console.log(chalk.green('🧹 All wallets cleared'));
  }

  /**
   * 获取配置文件路径
   */
  public getConfigPath(): string {
    return this.configFile;
  }

  /**
   * 导出钱包数据
   */
  public exportWallets(): Config {
    return { ...this.config };
  }

  /**
   * 导入钱包数据
   */
  public importWallets(data: Config): void {
    this.config = data;
    this.saveConfig();
    console.log(chalk.green(`📥 Imported ${data.wallets.length} wallets`));
  }

  /**
   * 获取统计信息
   */
  public getStats(): { total: number; current: string | null; latest: string | null } {
    const current = this.getCurrentWallet();
    const latest = this.getLatestWallet();
    
    return {
      total: this.config.wallets.length,
      current: current?.address || null,
      latest: latest?.address || null
    };
  }
}
