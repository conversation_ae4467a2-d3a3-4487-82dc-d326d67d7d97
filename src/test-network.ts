import { Connection, clusterApiUrl, PublicKey } from '@solana/web3.js';
import { SolanaWallet } from './wallet';
import { SolanaOperations } from './solana-operations';

async function testNetworkConnection() {
  console.log('🌐 测试网络连接');
  console.log('===============\n');

  // 测试不同的RPC端点
  const rpcEndpoints = [
    { name: 'Solana官方DevNet', url: 'https://api.devnet.solana.com' },
    { name: 'Helius DevNet', url: 'https://devnet.helius-rpc.com' },
    { name: 'QuickNode DevNet', url: clusterApiUrl('devnet') },
  ];

  for (const endpoint of rpcEndpoints) {
    console.log(`测试 ${endpoint.name}...`);
    try {
      const connection = new Connection(endpoint.url, 'confirmed');
      
      // 测试获取最新区块高度
      const blockHeight = await connection.getBlockHeight();
      console.log(`   ✅ 连接成功，当前区块高度: ${blockHeight}`);
      
      // 测试获取一个已知账户的余额（Solana官方账户）
      const solanaAccount = new PublicKey('********************************');
      const balance = await connection.getBalance(solanaAccount);
      console.log(`   ✅ 系统账户余额: ${balance / 1e9} SOL\n`);
      
      return connection; // 返回第一个成功的连接
    } catch (error) {
      console.log(`   ❌ 连接失败: ${error}\n`);
    }
  }
  
  throw new Error('所有RPC端点都无法连接');
}

async function testSolanaOperations() {
  console.log('🔧 测试Solana操作');
  console.log('================\n');

  try {
    // 创建钱包
    const wallet = new SolanaWallet();
    console.log(`创建测试钱包: ${wallet.getAddress()}\n`);

    // 创建Solana操作实例
    const solanaOps = new SolanaOperations('devnet');

    // 测试余额查询
    console.log('1. 测试余额查询...');
    const balance = await solanaOps.getBalance(wallet.keypair.publicKey);
    console.log(`   ✅ 钱包余额: ${balance} SOL\n`);

    // 测试空投（小额）
    console.log('2. 测试空投 0.1 SOL...');
    try {
      const airdropSignature = await solanaOps.requestAirdrop(wallet.keypair.publicKey, 0.1);
      console.log(`   ✅ 空投交易签名: ${airdropSignature}`);
      
      // 等待确认
      console.log('   等待交易确认...');
      await solanaOps.waitForConfirmation(airdropSignature);
      
      // 检查新余额
      const newBalance = await solanaOps.getBalance(wallet.keypair.publicKey);
      console.log(`   ✅ 空投后余额: ${newBalance} SOL\n`);
      
      return wallet;
    } catch (error) {
      console.log(`   ⚠️ 空投失败（这在DevNet上很常见）: ${error}\n`);
      return wallet;
    }

  } catch (error) {
    console.error('❌ Solana操作测试失败:', error);
    throw error;
  }
}

async function runNetworkTests() {
  try {
    // 测试网络连接
    await testNetworkConnection();
    
    // 测试Solana操作
    await testSolanaOperations();
    
    console.log('✅ 所有网络测试完成！');
  } catch (error) {
    console.error('❌ 网络测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  runNetworkTests().catch(console.error);
}

export { testNetworkConnection, testSolanaOperations };
