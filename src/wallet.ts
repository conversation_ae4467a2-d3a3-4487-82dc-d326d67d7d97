import { Keypair, PublicKey } from '@solana/web3.js';
import * as bip39 from 'bip39';
import { derivePath } from 'ed25519-hd-key';

export class SolanaWallet {
  public keypair: Keypair;
  public mnemonic: string;

  constructor(mnemonic?: string) {
    if (mnemonic) {
      this.mnemonic = mnemonic;
      this.keypair = this.deriveKeypairFromMnemonic(mnemonic);
    } else {
      this.mnemonic = this.generateMnemonic();
      this.keypair = this.deriveKeypairFromMnemonic(this.mnemonic);
    }
  }

  /**
   * 生成新的助记词
   */
  private generateMnemonic(): string {
    return bip39.generateMnemonic();
  }

  /**
   * 从助记词派生密钥对
   */
  private deriveKeypairFromMnemonic(mnemonic: string): Keypair {
    if (!bip39.validateMnemonic(mnemonic)) {
      throw new Error('Invalid mnemonic');
    }

    const seed = bip39.mnemonicToSeedSync(mnemonic);
    const derivedSeed = derivePath("m/44'/501'/0'/0'", seed.toString('hex')).key;
    return Keypair.fromSeed(derivedSeed);
  }

  /**
   * 获取公钥地址
   */
  public getAddress(): string {
    return this.keypair.publicKey.toBase58();
  }

  /**
   * 获取助记词
   */
  public getMnemonic(): string {
    return this.mnemonic;
  }

  /**
   * 获取私钥（Base58格式）
   */
  public getPrivateKey(): string {
    return Buffer.from(this.keypair.secretKey).toString('base64');
  }

  /**
   * 从私钥创建钱包
   */
  public static fromPrivateKey(privateKey: string): SolanaWallet {
    const secretKey = Buffer.from(privateKey, 'base64');
    const keypair = Keypair.fromSecretKey(secretKey);
    
    // 创建一个空的助记词，因为我们是从私钥恢复的
    const wallet = Object.create(SolanaWallet.prototype);
    wallet.keypair = keypair;
    wallet.mnemonic = '';
    return wallet;
  }

  /**
   * 从助记词创建钱包
   */
  public static fromMnemonic(mnemonic: string): SolanaWallet {
    return new SolanaWallet(mnemonic);
  }
}
