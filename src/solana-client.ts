import {
  Connection,
  PublicKey,
  LAMPORTS_PER_SOL,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
  clusterApiUrl,
  Cluster,
} from '@solana/web3.js';
import {
  getOrCreateAssociatedTokenAccount,
  transfer,
  getMint,
  getAccount,
} from '@solana/spl-token';
import {
  ProxyAgent,
  setGlobalDispatcher,
} from 'undici';
import { SolanaWallet } from './wallet';

// Configure proxy
const proxyAgent = new ProxyAgent('http://127.0.0.1:7897');
setGlobalDispatcher(proxyAgent);

export type NetworkType = 'devnet' | 'testnet' | 'mainnet-beta';

export interface TokenBalance {
  mint: string;
  amount: number;
  decimals: number;
  uiAmount: number;
}

export class SolanaClient {
  private connection: Connection;
  private network: NetworkType;

  constructor(network: NetworkType = 'devnet') {
    this.network = network;
    this.connection = this.createConnection(network);
  }

  /**
   * 创建网络连接
   */
  private createConnection(network: NetworkType): Connection {
    const rpcUrls = {
      'devnet': 'https://api.devnet.solana.com',
      'testnet': 'https://api.testnet.solana.com',
      'mainnet-beta': 'https://api.mainnet-beta.solana.com'
    };

    return new Connection(rpcUrls[network], 'confirmed');
  }

  /**
   * 获取当前网络
   */
  public getNetwork(): NetworkType {
    return this.network;
  }

  /**
   * 切换网络
   */
  public switchNetwork(network: NetworkType): void {
    this.network = network;
    this.connection = this.createConnection(network);
  }

  /**
   * 获取SOL余额
   */
  public async getSolBalance(publicKey: PublicKey): Promise<number> {
    try {
      const balance = await this.connection.getBalance(publicKey);
      return balance / LAMPORTS_PER_SOL;
    } catch (error) {
      throw new Error(`Failed to get SOL balance: ${error}`);
    }
  }

  /**
   * 获取SPL代币余额
   */
  public async getSplTokenBalance(
    walletPublicKey: PublicKey,
    mintAddress: PublicKey
  ): Promise<number> {
    try {
      const tokenAccounts = await this.connection.getTokenAccountsByOwner(
        walletPublicKey,
        { mint: mintAddress }
      );

      if (tokenAccounts.value.length === 0) {
        return 0;
      }

      const tokenAccount = tokenAccounts.value[0];
      const accountInfo = await this.connection.getTokenAccountBalance(
        tokenAccount.pubkey
      );

      return parseFloat(accountInfo.value.uiAmount?.toString() || '0');
    } catch (error) {
      console.error('Error getting SPL token balance:', error);
      return 0;
    }
  }

  /**
   * 获取所有SPL代币余额
   */
  public async getAllTokenBalances(walletPublicKey: PublicKey): Promise<TokenBalance[]> {
    try {
      const tokenAccounts = await this.connection.getTokenAccountsByOwner(
        walletPublicKey,
        { programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') }
      );

      const balances: TokenBalance[] = [];

      for (const tokenAccount of tokenAccounts.value) {
        try {
          const accountInfo = await this.connection.getTokenAccountBalance(
            tokenAccount.pubkey
          );

          if (accountInfo.value.uiAmount && accountInfo.value.uiAmount > 0) {
            const accountData = await getAccount(this.connection, tokenAccount.pubkey);
            
            balances.push({
              mint: accountData.mint.toBase58(),
              amount: parseInt(accountInfo.value.amount),
              decimals: accountInfo.value.decimals,
              uiAmount: accountInfo.value.uiAmount
            });
          }
        } catch (error) {
          // 跳过无法读取的代币账户
          continue;
        }
      }

      return balances;
    } catch (error) {
      throw new Error(`Failed to get token balances: ${error}`);
    }
  }

  /**
   * 请求空投（仅限devnet和testnet）
   */
  public async requestAirdrop(publicKey: PublicKey, amount: number): Promise<string> {
    if (this.network === 'mainnet-beta') {
      throw new Error('Airdrop is not available on mainnet');
    }

    try {
      const signature = await this.connection.requestAirdrop(
        publicKey,
        amount * LAMPORTS_PER_SOL
      );
      
      await this.connection.confirmTransaction(signature);
      return signature;
    } catch (error) {
      throw new Error(`Airdrop failed: ${error}`);
    }
  }

  /**
   * 发送SOL
   */
  public async sendSol(
    fromWallet: SolanaWallet,
    toAddress: string,
    amount: number
  ): Promise<string> {
    try {
      const toPublicKey = new PublicKey(toAddress);
      
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: fromWallet.keypair.publicKey,
          toPubkey: toPublicKey,
          lamports: amount * LAMPORTS_PER_SOL,
        })
      );

      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [fromWallet.keypair]
      );

      return signature;
    } catch (error) {
      throw new Error(`SOL transfer failed: ${error}`);
    }
  }

  /**
   * 发送SPL代币
   */
  public async sendSplToken(
    fromWallet: SolanaWallet,
    toAddress: string,
    mintAddress: string,
    amount: number
  ): Promise<string> {
    try {
      const toPublicKey = new PublicKey(toAddress);
      const mintPublicKey = new PublicKey(mintAddress);

      // 获取或创建发送方的关联代币账户
      const fromTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        fromWallet.keypair,
        mintPublicKey,
        fromWallet.keypair.publicKey
      );

      // 获取或创建接收方的关联代币账户
      const toTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        fromWallet.keypair,
        mintPublicKey,
        toPublicKey
      );

      // 获取代币信息以确定小数位数
      const mintInfo = await getMint(this.connection, mintPublicKey);
      const transferAmount = amount * Math.pow(10, mintInfo.decimals);

      // 执行转账
      const signature = await transfer(
        this.connection,
        fromWallet.keypair,
        fromTokenAccount.address,
        toTokenAccount.address,
        fromWallet.keypair,
        transferAmount
      );

      return signature;
    } catch (error) {
      throw new Error(`SPL token transfer failed: ${error}`);
    }
  }

  /**
   * 获取交易详情
   */
  public async getTransaction(signature: string) {
    try {
      const transaction = await this.connection.getTransaction(signature);
      return transaction;
    } catch (error) {
      throw new Error(`Failed to get transaction: ${error}`);
    }
  }

  /**
   * 等待交易确认
   */
  public async waitForConfirmation(signature: string): Promise<void> {
    await this.connection.confirmTransaction(signature);
  }

  /**
   * 获取网络状态
   */
  public async getNetworkStatus() {
    try {
      const blockHeight = await this.connection.getBlockHeight();
      const slot = await this.connection.getSlot();
      
      return {
        network: this.network,
        blockHeight,
        slot,
        rpcUrl: this.connection.rpcEndpoint
      };
    } catch (error) {
      throw new Error(`Failed to get network status: ${error}`);
    }
  }
}
