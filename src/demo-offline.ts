import { PublicKey, Keypair, SystemProgram, Transaction } from '@solana/web3.js';
import { SolanaWallet } from './wallet';

/**
 * 离线演示 - 展示Solana基本功能（不需要网络连接）
 */
async function offlineDemo() {
  console.log('🚀 Solana DevNet 离线演示');
  console.log('========================\n');

  // 1. 钱包生成和管理
  console.log('1. 钱包生成和管理');
  console.log('------------------');
  
  // 生成新钱包
  const wallet1 = new SolanaWallet();
  console.log(`✅ 钱包1地址: ${wallet1.getAddress()}`);
  console.log(`✅ 助记词: ${wallet1.getMnemonic()}`);
  console.log(`✅ 私钥: ${wallet1.getPrivateKey()}\n`);

  // 从助记词恢复钱包
  const recoveredWallet = SolanaWallet.fromMnemonic(wallet1.getMnemonic());
  console.log(`✅ 从助记词恢复的地址: ${recoveredWallet.getAddress()}`);
  console.log(`✅ 地址匹配验证: ${wallet1.getAddress() === recoveredWallet.getAddress()}\n`);

  // 2. 创建多个钱包
  console.log('2. 创建多个钱包');
  console.log('----------------');
  
  const wallet2 = new SolanaWallet();
  const wallet3 = new SolanaWallet();
  
  console.log(`✅ 钱包2地址: ${wallet2.getAddress()}`);
  console.log(`✅ 钱包3地址: ${wallet3.getAddress()}`);
  console.log(`✅ 所有地址都不同: ${new Set([
    wallet1.getAddress(), 
    wallet2.getAddress(), 
    wallet3.getAddress()
  ]).size === 3}\n`);

  // 3. 交易构建演示（离线）
  console.log('3. 交易构建演示（离线）');
  console.log('----------------------');
  
  // 构建SOL转账交易
  const transferAmount = 0.1; // 0.1 SOL
  const lamports = transferAmount * 1e9; // 转换为lamports
  
  const transferInstruction = SystemProgram.transfer({
    fromPubkey: wallet1.keypair.publicKey,
    toPubkey: wallet2.keypair.publicKey,
    lamports: lamports,
  });

  const transaction = new Transaction().add(transferInstruction);
  
  console.log(`✅ 创建转账交易:`);
  console.log(`   发送方: ${wallet1.getAddress()}`);
  console.log(`   接收方: ${wallet2.getAddress()}`);
  console.log(`   金额: ${transferAmount} SOL (${lamports} lamports)`);
  console.log(`   交易指令数: ${transaction.instructions.length}\n`);

  // 4. 地址验证和格式化
  console.log('4. 地址验证和格式化');
  console.log('------------------');
  
  const addresses = [wallet1.getAddress(), wallet2.getAddress(), wallet3.getAddress()];
  
  addresses.forEach((address, index) => {
    try {
      const publicKey = new PublicKey(address);
      console.log(`✅ 钱包${index + 1}地址有效: ${address}`);
      console.log(`   Base58长度: ${address.length}`);
      console.log(`   PublicKey对象: ${publicKey.toBase58()}\n`);
    } catch (error) {
      console.log(`❌ 钱包${index + 1}地址无效: ${error}\n`);
    }
  });

  // 5. 助记词分析
  console.log('5. 助记词分析');
  console.log('-------------');
  
  const mnemonic = wallet1.getMnemonic();
  const words = mnemonic.split(' ');
  
  console.log(`✅ 助记词: ${mnemonic}`);
  console.log(`✅ 单词数量: ${words.length}`);
  console.log(`✅ 前3个单词: ${words.slice(0, 3).join(', ')}`);
  console.log(`✅ 后3个单词: ${words.slice(-3).join(', ')}\n`);

  // 6. 密钥对信息
  console.log('6. 密钥对信息');
  console.log('-------------');
  
  console.log(`✅ 公钥 (Base58): ${wallet1.keypair.publicKey.toBase58()}`);
  console.log(`✅ 公钥字节长度: ${wallet1.keypair.publicKey.toBytes().length}`);
  console.log(`✅ 私钥字节长度: ${wallet1.keypair.secretKey.length}`);
  console.log(`✅ 私钥前8字节: ${Array.from(wallet1.keypair.secretKey.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ')}\n`);

  // 7. 网络配置信息
  console.log('7. 网络配置信息');
  console.log('---------------');
  
  console.log('✅ 支持的网络:');
  console.log('   - devnet (开发网络)');
  console.log('   - testnet (测试网络)');
  console.log('   - mainnet-beta (主网)');
  console.log('\n✅ 推荐的RPC端点:');
  console.log('   - https://api.devnet.solana.com (官方DevNet)');
  console.log('   - https://api.testnet.solana.com (官方TestNet)');
  console.log('   - https://api.mainnet-beta.solana.com (官方MainNet)\n');

  // 8. 使用说明
  console.log('8. 使用说明');
  console.log('-----------');
  
  console.log('✅ 要在真实网络上测试，你需要:');
  console.log('   1. 确保网络连接正常');
  console.log('   2. 使用有效的RPC端点');
  console.log('   3. 在DevNet上请求空投获取测试SOL');
  console.log('   4. 注意交易费用（通常很小，约0.000005 SOL）');
  console.log('\n✅ 获取测试代币:');
  console.log('   - SOL: https://faucet.solana.com/');
  console.log('   - SPL代币: https://spl-token-faucet.com/');
  console.log('\n✅ 安全提醒:');
  console.log('   - 永远不要分享你的私钥');
  console.log('   - 在生产环境中使用硬件钱包');
  console.log('   - 定期备份助记词');
  console.log('   - 在MainNet上操作前充分测试\n');

  console.log('🎉 离线演示完成！所有基本功能都正常工作。');
  console.log('💡 当网络连接可用时，可以运行完整的在线示例。');
}

// 运行演示
if (require.main === module) {
  offlineDemo().catch(console.error);
}

export { offlineDemo };
