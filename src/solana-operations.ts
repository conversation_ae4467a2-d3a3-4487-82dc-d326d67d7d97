import {
  Connection,
  PublicKey,
  LAMPORTS_PER_SOL,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
  Keypair,
  clusterApiUrl,
} from '@solana/web3.js';
import {
  createTransferInstruction,
  getOrCreateAssociatedTokenAccount,
  transfer,
  TOKEN_PROGRAM_ID,
  ASSOCIATED_TOKEN_PROGRAM_ID,
  getMint,
} from '@solana/spl-token';
import { SolanaWallet } from './wallet';

export class SolanaOperations {
  private connection: Connection;

  constructor(cluster: 'devnet' | 'testnet' | 'mainnet-beta' = 'devnet') {
    // 使用更稳定的RPC端点
    const rpcUrl = cluster === 'devnet'
      ? 'https://api.devnet.solana.com'
      : clusterApiUrl(cluster);

    this.connection = new Connection(rpcUrl, 'confirmed');
  }

  /**
   * 获取连接对象
   */
  public getConnection(): Connection {
    return this.connection;
  }

  /**
   * 获取账户余额（SOL）
   */
  public async getBalance(publicKey: PublicKey): Promise<number> {
    try {
      const balance = await this.connection.getBalance(publicKey);
      return balance / LAMPORTS_PER_SOL;
    } catch (error) {
      console.error('获取余额失败，尝试使用备用RPC...');
      // 尝试使用备用RPC
      const backupConnection = new Connection('https://devnet.helius-rpc.com', 'confirmed');
      const balance = await backupConnection.getBalance(publicKey);
      return balance / LAMPORTS_PER_SOL;
    }
  }

  /**
   * 请求空投（仅限devnet和testnet）
   */
  public async requestAirdrop(publicKey: PublicKey, amount: number): Promise<string> {
    try {
      const signature = await this.connection.requestAirdrop(
        publicKey,
        amount * LAMPORTS_PER_SOL
      );
      
      // 等待交易确认
      await this.connection.confirmTransaction(signature);
      return signature;
    } catch (error) {
      throw new Error(`Airdrop failed: ${error}`);
    }
  }

  /**
   * 发送SOL
   */
  public async sendSol(
    fromWallet: SolanaWallet,
    toPublicKey: PublicKey,
    amount: number
  ): Promise<string> {
    try {
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: fromWallet.keypair.publicKey,
          toPubkey: toPublicKey,
          lamports: amount * LAMPORTS_PER_SOL,
        })
      );

      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [fromWallet.keypair]
      );

      return signature;
    } catch (error) {
      throw new Error(`SOL transfer failed: ${error}`);
    }
  }

  /**
   * 获取SPL代币余额
   */
  public async getSplTokenBalance(
    walletPublicKey: PublicKey,
    mintAddress: PublicKey
  ): Promise<number> {
    try {
      const tokenAccounts = await this.connection.getTokenAccountsByOwner(
        walletPublicKey,
        { mint: mintAddress }
      );

      if (tokenAccounts.value.length === 0) {
        return 0;
      }

      const tokenAccount = tokenAccounts.value[0];
      const accountInfo = await this.connection.getTokenAccountBalance(
        tokenAccount.pubkey
      );

      return parseFloat(accountInfo.value.uiAmount?.toString() || '0');
    } catch (error) {
      console.error('Error getting SPL token balance:', error);
      return 0;
    }
  }

  /**
   * 发送SPL代币
   */
  public async sendSplToken(
    fromWallet: SolanaWallet,
    toPublicKey: PublicKey,
    mintAddress: PublicKey,
    amount: number
  ): Promise<string> {
    try {
      // 获取或创建发送方的关联代币账户
      const fromTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        fromWallet.keypair,
        mintAddress,
        fromWallet.keypair.publicKey
      );

      // 获取或创建接收方的关联代币账户
      const toTokenAccount = await getOrCreateAssociatedTokenAccount(
        this.connection,
        fromWallet.keypair,
        mintAddress,
        toPublicKey
      );

      // 获取代币信息以确定小数位数
      const mintInfo = await getMint(this.connection, mintAddress);
      const transferAmount = amount * Math.pow(10, mintInfo.decimals);

      // 执行转账
      const signature = await transfer(
        this.connection,
        fromWallet.keypair,
        fromTokenAccount.address,
        toTokenAccount.address,
        fromWallet.keypair,
        transferAmount
      );

      return signature;
    } catch (error) {
      throw new Error(`SPL token transfer failed: ${error}`);
    }
  }

  /**
   * 获取交易详情
   */
  public async getTransaction(signature: string) {
    try {
      const transaction = await this.connection.getTransaction(signature);
      return transaction;
    } catch (error) {
      throw new Error(`Failed to get transaction: ${error}`);
    }
  }

  /**
   * 等待交易确认
   */
  public async waitForConfirmation(signature: string): Promise<void> {
    await this.connection.confirmTransaction(signature);
  }
}
