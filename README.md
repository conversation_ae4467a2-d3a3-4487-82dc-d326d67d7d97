# Solana Wallet CLI

一个功能完整的Solana钱包命令行工具，使用TypeScript构建。

## 功能特性

- 🔑 **钱包管理**: 创建新钱包、从助记词/私钥导入钱包
- 🌐 **多网络支持**: 支持DevNet、TestNet和MainNet
- 💰 **余额查询**: 查询SOL和SPL代币余额
- 🚁 **空投功能**: 在测试网络上请求SOL空投
- 💸 **转账功能**: 发送SOL和SPL代币
- 📊 **网络状态**: 查看网络状态和区块信息
- 🔍 **交易查询**: 查看交易详情
- ✅ **地址验证**: 验证Solana地址和助记词

## 安装

```bash
# 安装依赖
npm install

# 编译TypeScript
npm run build
```

## 使用方法

### 全局选项

```bash
-n, --network <network>     选择网络 (devnet, testnet, mainnet-beta) [默认: devnet]
-m, --mnemonic <mnemonic>   使用助记词导入钱包
-k, --private-key <key>     使用私钥导入钱包
-a, --address <address>     使用地址进行只读操作
```

### 基本命令

#### 1. 创建新钱包

```bash
npm run dev create
```

#### 2. 显示钱包信息

```bash
# 使用助记词
npm run dev -- -m "your twelve word mnemonic phrase here" info

# 使用私钥
npm run dev -- -k "your-private-key" info

# 使用地址（只读模式）
npm run dev -- -a "wallet-address" info
```

#### 3. 查询余额

```bash
# 查询当前钱包SOL和所有代币余额
npm run dev -- -m "your mnemonic" balance

# 使用地址查询余额（只读）
npm run dev -- -a "address-to-check" balance

# 查询特定地址的余额（命令行选项）
npm run dev -- balance --addr "address-to-check"

# 查询特定SPL代币余额
npm run dev -- -m "your mnemonic" balance -t "token-mint-address"

# 使用地址查询特定代币余额
npm run dev -- -a "address" balance -t "token-mint-address"
```

#### 4. 请求空投 (仅限DevNet/TestNet)

```bash
# 请求1 SOL空投到当前钱包
npm run dev -- -m "your mnemonic" airdrop 1

# 请求空投到指定地址
npm run dev -- airdrop 1 --to "target-address"

# 使用地址参数请求空投
npm run dev -- -a "target-address" airdrop 1

# 在TestNet上请求空投
npm run dev -- -n testnet -m "your mnemonic" airdrop 0.5
```

#### 5. 发送SOL

```bash
# 从当前钱包发送
npm run dev -- -m "your mnemonic" send-sol "recipient-address" 0.1

# 指定发送方地址（需要对应的私钥/助记词）
npm run dev -- -m "your mnemonic" send-sol "recipient-address" 0.1 --from "sender-address"
```

#### 6. 发送SPL代币

```bash
# 从当前钱包发送代币
npm run dev -- -m "your mnemonic" send-token "recipient-address" "token-mint-address" 10

# 指定发送方地址（需要对应的私钥/助记词）
npm run dev -- -m "your mnemonic" send-token "recipient-address" "token-mint-address" 10 --from "sender-address"
```

#### 7. 查看网络状态

```bash
npm run dev -- -n mainnet-beta status
```

#### 8. 验证地址或助记词

```bash
# 验证地址
npm run dev validate -a "solana-address"

# 验证助记词
npm run dev validate -m "your twelve word mnemonic"
```

#### 9. 查询交易详情

```bash
npm run dev tx "transaction-signature"
```

## 使用示例

### 完整工作流程示例

```bash
# 1. 创建新钱包
npm run dev create

# 2. 使用生成的助记词查看钱包信息
npm run dev -- -m "protect carry venue hover cart beach faculty jump network burger want alter" info

# 3. 请求空投
npm run dev -- -m "protect carry venue hover cart beach faculty jump network burger want alter" airdrop 1

# 4. 查询余额
npm run dev -- -m "protect carry venue hover cart beach faculty jump network burger want alter" balance

# 5. 发送SOL
npm run dev -- -m "protect carry venue hover cart beach faculty jump network burger want alter" send-sol "recipient-address" 0.1

# 6. 在MainNet上查看余额
npm run dev -- -n mainnet-beta -m "your mnemonic" balance
```

### 不同网络的使用

```bash
# DevNet (默认)
npm run dev -- -m "your mnemonic" balance

# TestNet
npm run dev -- -n testnet -m "your mnemonic" balance

# MainNet
npm run dev -- -n mainnet-beta -m "your mnemonic" balance
```

## 项目结构

```
src/
├── wallet.ts          # 钱包管理类
├── solana-client.ts   # Solana网络客户端
├── cli.ts            # 命令行界面
└── index.ts          # 入口文件
```

## 安全注意事项

⚠️ **重要安全提醒**:

1. **私钥安全**: 永远不要分享你的私钥或助记词
2. **测试优先**: 在MainNet上操作前，先在DevNet/TestNet上充分测试
3. **小额测试**: 首次使用时建议先进行小额转账测试
4. **备份重要**: 安全备份你的助记词和私钥
5. **环境变量**: 在生产环境中使用环境变量存储敏感信息

## 网络信息

### DevNet
- 用途: 开发和测试
- 特点: 免费空投、快速重置
- 空投限制: 每次最多2 SOL

### TestNet  
- 用途: 接近生产环境的测试
- 特点: 更稳定、接近MainNet环境
- 空投限制: 有限制

### MainNet
- 用途: 生产环境
- 特点: 真实SOL、需要支付真实费用
- 注意: 无空投功能

## 常见问题

### Q: 空投失败怎么办？
A: 
- 检查网络是否为DevNet或TestNet
- 尝试更小的金额
- 等待一段时间后重试
- 使用在线水龙头: https://faucet.solana.com/

### Q: 如何获取SPL代币进行测试？
A: 访问 https://spl-token-faucet.com/ 获取测试代币

### Q: 交易失败的常见原因？
A:
- 余额不足
- 网络拥堵
- 地址格式错误
- 代币账户不存在

## 开发

```bash
# 开发模式运行
npm run dev

# 编译
npm run build

# 清理编译文件
npm run clean
```

## 许可证

ISC
