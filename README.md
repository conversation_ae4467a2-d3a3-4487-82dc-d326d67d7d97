# Solana Wallet CLI

一个功能完整的Solana钱包命令行工具，使用TypeScript构建。

## 功能特性

- 🔑 **钱包管理**: 创建新钱包、从助记词/私钥导入钱包
- 🌐 **多网络支持**: 支持DevNet、TestNet和MainNet
- 💰 **余额查询**: 查询SOL和SPL代币余额
- 🚁 **空投功能**: 在测试网络上请求SOL空投
- 💸 **转账功能**: 发送SOL和SPL代币
- 📊 **网络状态**: 查看网络状态和区块信息
- 🔍 **交易查询**: 查看交易详情
- ✅ **地址验证**: 验证Solana地址和助记词

## 安装

```bash
# 安装依赖
npm install

# 编译TypeScript
npm run build
```

## 使用方法

### 全局选项

```bash
-n, --network <network>     选择网络 (devnet, testnet, mainnet-beta) [默认: devnet]
-m, --mnemonic <mnemonic>   使用助记词导入钱包
-k, --private-key <key>     使用私钥导入钱包
-a, --address <address>     使用地址进行只读操作
```

### 基本命令

#### 1. 创建新钱包

```bash
npm run dev create
```

#### 2. 显示钱包信息

```bash
# 使用助记词
npm run dev -- -m "your twelve word mnemonic phrase here" info

# 使用私钥
npm run dev -- -k "your-private-key" info

# 使用地址（只读模式）
npm run dev -- -a "wallet-address" info
```

#### 3. 查询余额

```bash
# 查询当前钱包SOL和所有代币余额
npm run dev -- -m "your mnemonic" balance

# 使用地址查询余额（只读）
npm run dev -- -a "address-to-check" balance

# 查询特定地址的余额（命令行选项）
npm run dev -- balance --addr "address-to-check"

# 查询特定SPL代币余额
npm run dev -- -m "your mnemonic" balance -t "token-mint-address"

# 使用地址查询特定代币余额
npm run dev -- -a "address" balance -t "token-mint-address"
```

#### 4. 请求空投 (仅限DevNet/TestNet)

```bash
# 请求1 SOL空投到当前钱包
npm run dev -- -m "your mnemonic" airdrop 1

# 请求空投到指定地址
npm run dev -- airdrop 1 --to "target-address"

# 使用地址参数请求空投
npm run dev -- -a "target-address" airdrop 1

# 在TestNet上请求空投
npm run dev -- -n testnet -m "your mnemonic" airdrop 0.5
```

#### 5. 发送SOL

```bash
# 从当前钱包发送
npm run dev -- -m "your mnemonic" send-sol "recipient-address" 0.1

# 指定发送方地址（需要对应的私钥/助记词）
npm run dev -- -m "your mnemonic" send-sol "recipient-address" 0.1 --from "sender-address"
```

#### 6. 发送SPL代币

```bash
# 从当前钱包发送代币
npm run dev -- -m "your mnemonic" send-token "recipient-address" "token-mint-address" 10

# 指定发送方地址（需要对应的私钥/助记词）
npm run dev -- -m "your mnemonic" send-token "recipient-address" "token-mint-address" 10 --from "sender-address"
```

#### 7. 查看网络状态

```bash
npm run dev -- -n mainnet-beta status
```

#### 8. 验证地址或助记词

```bash
# 验证地址
npm run dev validate -a "solana-address"

# 验证助记词
npm run dev validate -m "your twelve word mnemonic"
```

#### 9. 查询交易详情

```bash
npm run dev tx "transaction-signature"
```

## 使用示例

### 完整工作流程示例

```bash
# 1. 创建新钱包
npm run dev create

# 2. 使用生成的助记词查看钱包信息
npm run dev -- -m "protect carry venue hover cart beach faculty jump network burger want alter" info

# 3. 请求空投
npm run dev -- -m "protect carry venue hover cart beach faculty jump network burger want alter" airdrop 1

# 4. 查询余额
npm run dev -- -m "protect carry venue hover cart beach faculty jump network burger want alter" balance

# 5. 发送SOL
npm run dev -- -m "protect carry venue hover cart beach faculty jump network burger want alter" send-sol "recipient-address" 0.1

# 6. 在MainNet上查看余额
npm run dev -- -n mainnet-beta -m "your mnemonic" balance
```

### 不同网络的使用

```bash
# DevNet (默认)
npm run dev -- -m "your mnemonic" balance

# TestNet
npm run dev -- -n testnet -m "your mnemonic" balance

# MainNet
npm run dev -- -n mainnet-beta -m "your mnemonic" balance
```

## 地址参数使用指南

### 概述

CLI工具支持三种使用方式：

1. **只读操作**：使用 `-a, --address` 参数进行查询操作，无需私钥
2. **指定发送方**：在转账时使用 `--from` 参数指定发送方地址
3. **指定接收方**：在空投时使用 `--to` 参数指定接收方地址

### 只读操作

#### 查看地址信息

```bash
# 查看指定地址的基本信息（只读模式）
npm run dev -- -a "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG" info
```

输出示例：
```
🌐 Connected to DEVNET

💼 Address Information
📍 Address: 9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG
ℹ️  Read-only mode: No private key or mnemonic available
```

#### 查询余额

```bash
# 查询指定地址的SOL和所有代币余额
npm run dev -- -a "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG" balance

# 查询指定地址的特定代币余额
npm run dev -- -a "address" balance -t "token-mint-address"

# 在不同网络上查询
npm run dev -- -n mainnet-beta -a "address" balance

# 使用命令行选项查询特定地址
npm run dev -- balance --addr "address"
```

### 空投操作

```bash
# 空投到指定地址（不需要该地址的私钥）
npm run dev -- airdrop 1 --to "target-address"

# 使用全局地址参数
npm run dev -- -a "target-address" airdrop 1

# 在TestNet上空投
npm run dev -- -n testnet airdrop 0.5 --to "target-address"
```

### 转账操作

#### 发送SOL

```bash
# 基本转账（从当前钱包发送）
npm run dev -- -m "your mnemonic" send-sol "recipient-address" 0.1

# 指定发送方地址（验证地址匹配）
npm run dev -- -m "your mnemonic" send-sol "recipient-address" 0.1 --from "sender-address"

# 使用私钥进行转账
npm run dev -- -k "your-private-key" send-sol "recipient-address" 0.1
```

#### 发送SPL代币

```bash
# 基本代币转账
npm run dev -- -m "your mnemonic" send-token "recipient-address" "token-mint" 10

# 指定发送方地址
npm run dev -- -m "your mnemonic" send-token "recipient-address" "token-mint" 10 --from "sender-address"
```

### 实际使用场景

#### 场景1：监控多个地址

```bash
#!/bin/bash

# 定义要监控的地址列表
ADDRESSES=(
    "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"
    "8ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEH"
    "7ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEI"
)

echo "=== 监控地址余额 ==="
for addr in "${ADDRESSES[@]}"; do
    echo "地址: $addr"
    npm run dev -- -a "$addr" balance
    echo "---"
done
```

#### 场景2：批量空投

```bash
#!/bin/bash

# 批量空投到多个地址
RECIPIENTS=(
    "9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"
    "8ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEH"
)

for recipient in "${RECIPIENTS[@]}"; do
    echo "空投到: $recipient"
    npm run dev -- airdrop 0.1 --to "$recipient"
    sleep 2  # 避免请求过快
done
```

#### 场景3：地址验证和信息查看

```bash
#!/bin/bash

ADDRESS="9ALjZrG6grE9dVC94gSip2yqL3JSrTktzD5msWuiagEG"

# 1. 验证地址格式
echo "=== 验证地址 ==="
npm run dev -- validate --addr "$ADDRESS"

# 2. 查看地址信息
echo "=== 地址信息 ==="
npm run dev -- -a "$ADDRESS" info

# 3. 查看余额
echo "=== 余额信息 ==="
npm run dev -- -a "$ADDRESS" balance

# 4. 在不同网络上查看
echo "=== MainNet余额 ==="
npm run dev -- -n mainnet-beta -a "$ADDRESS" balance
```

### 命令对比

| 操作 | 传统方式 | 地址参数方式 |
|------|----------|--------------|
| 查看余额 | `npm run dev -- -m "mnemonic" balance` | `npm run dev -- -a "address" balance` |
| 查看信息 | `npm run dev -- -m "mnemonic" info` | `npm run dev -- -a "address" info` |
| 空投 | `npm run dev -- -m "mnemonic" airdrop 1` | `npm run dev -- airdrop 1 --to "address"` |
| 转账 | `npm run dev -- -m "mnemonic" send-sol "to" 0.1` | `npm run dev -- -m "mnemonic" send-sol "to" 0.1 --from "from"` |

## 项目结构

```
src/
├── wallet.ts          # 钱包管理类
├── solana-client.ts   # Solana网络客户端
├── cli.ts            # 命令行界面
└── index.ts          # 入口文件
```

## 安全注意事项

⚠️ **重要安全提醒**:

1. **私钥安全**: 永远不要分享你的私钥或助记词
2. **测试优先**: 在MainNet上操作前，先在DevNet/TestNet上充分测试
3. **小额测试**: 首次使用时建议先进行小额转账测试
4. **备份重要**: 安全备份你的助记词和私钥
5. **环境变量**: 在生产环境中使用环境变量存储敏感信息

### 地址参数安全说明

#### 只读操作安全
- 使用 `-a` 参数进行只读操作是安全的，不会暴露私钥或助记词
- 适合监控和查询场景，可以安全地在脚本中使用

#### 转账操作安全
- 转账仍需要私钥或助记词，`--from` 参数用于验证地址匹配
- 如果提供的私钥/助记词与 `--from` 地址不匹配会报错
- 不能使用只读地址进行转账操作

#### 空投操作安全
- 可以空投到任何有效地址，不需要目标地址的私钥
- 适合测试和开发场景，但请注意空投限制

## 网络信息

### DevNet
- 用途: 开发和测试
- 特点: 免费空投、快速重置
- 空投限制: 每次最多2 SOL

### TestNet  
- 用途: 接近生产环境的测试
- 特点: 更稳定、接近MainNet环境
- 空投限制: 有限制

### MainNet
- 用途: 生产环境
- 特点: 真实SOL、需要支付真实费用
- 注意: 无空投功能

## 常见问题

### Q: 空投失败怎么办？
A: 
- 检查网络是否为DevNet或TestNet
- 尝试更小的金额
- 等待一段时间后重试
- 使用在线水龙头: https://faucet.solana.com/

### Q: 如何获取SPL代币进行测试？
A: 访问 https://spl-token-faucet.com/ 获取测试代币

### Q: 交易失败的常见原因？
A:
- 余额不足
- 网络拥堵
- 地址格式错误
- 代币账户不存在

### Q: 使用地址参数时出现错误怎么办？
A: 常见错误和解决方案：

1. **地址格式错误**
   ```
   ❌ Invalid address format
   ```
   解决：使用 `validate --addr` 命令验证地址格式

2. **只读地址尝试转账**
   ```
   ❌ Cannot send from read-only address. Please provide private key or mnemonic
   ```
   解决：提供对应的私钥或助记词

3. **地址不匹配**
   ```
   ❌ Provided private key/mnemonic does not match sender address
   ```
   解决：确保私钥/助记词对应正确的发送方地址

### Q: 什么时候使用地址参数？
A:
- **监控脚本**：使用地址参数创建监控脚本，避免在脚本中存储私钥
- **批量查询**：利用地址参数批量查询多个地址的余额
- **测试环境**：在测试环境中使用地址参数进行空投和测试
- **安全分离**：将查询操作和转账操作分离，提高安全性

## 开发

```bash
# 开发模式运行
npm run dev

# 编译
npm run build

# 清理编译文件
npm run clean
```

## 许可证

ISC
