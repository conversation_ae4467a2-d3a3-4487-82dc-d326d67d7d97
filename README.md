# Solana DevNet 基本示例

这个项目展示了如何使用TypeScript在Solana DevNet上进行基本操作，包括：

- 🔑 地址生成和钱包管理
- 📝 助记词生成和恢复
- 💰 空投SOL代币
- 💸 发送SOL
- 🪙 发送SPL代币

## 安装依赖

```bash
npm install
```

## 运行示例

### 运行基本示例
```bash
npm run dev
```

### 运行完整示例
```bash
npx ts-node src/examples.ts
```

### 编译并运行
```bash
npm run build
npm start
```

## 项目结构

```
src/
├── wallet.ts           # 钱包管理类
├── solana-operations.ts # Solana操作类
├── index.ts            # 基本示例
└── examples.ts         # 完整示例
```

## 主要功能

### 1. 钱包管理 (wallet.ts)

- `SolanaWallet` 类提供钱包创建和管理功能
- 支持从助记词恢复钱包
- 支持从私钥恢复钱包

```typescript
// 创建新钱包
const wallet = new SolanaWallet();

// 从助记词恢复
const wallet = SolanaWallet.fromMnemonic(mnemonic);

// 从私钥恢复
const wallet = SolanaWallet.fromPrivateKey(privateKey);
```

### 2. Solana操作 (solana-operations.ts)

- `SolanaOperations` 类提供与Solana网络交互的功能
- 支持余额查询、空投、SOL转账、SPL代币转账

```typescript
const solanaOps = new SolanaOperations('devnet');

// 查询余额
const balance = await solanaOps.getBalance(publicKey);

// 请求空投
const signature = await solanaOps.requestAirdrop(publicKey, 1);

// 发送SOL
const signature = await solanaOps.sendSol(fromWallet, toPublicKey, 0.1);

// 发送SPL代币
const signature = await solanaOps.sendSplToken(fromWallet, toPublicKey, mintAddress, 10);
```

## 示例说明

### 基本示例 (index.ts)
演示了所有基本功能的使用方法，包括：
1. 生成新钱包和打印助记词
2. 从助记词恢复钱包
3. 检查余额
4. 请求空投
5. SOL转账
6. SPL代币操作

### 完整示例 (examples.ts)
提供了更详细的分步示例：
1. `walletExample()` - 钱包创建和管理
2. `balanceAndAirdropExample()` - 余额查询和空投
3. `solTransferExample()` - SOL转账
4. `splTokenExample()` - SPL代币操作

## 注意事项

1. **网络选择**: 默认使用DevNet，适合测试和开发
2. **空投限制**: DevNet每次最多可以空投2 SOL，每天有限制
3. **SPL代币**: 示例中使用USDC测试代币，你可能需要从水龙头获取测试代币
4. **私钥安全**: 请妥善保管私钥和助记词，不要在生产环境中硬编码

## 获取测试代币

- **SOL**: 使用代码中的空投功能或访问 [Solana Faucet](https://faucet.solana.com/)
- **SPL代币**: 访问 [SPL Token Faucet](https://spl-token-faucet.com/)

## 依赖包说明

- `@solana/web3.js`: Solana JavaScript SDK
- `@solana/spl-token`: SPL代币操作库
- `bip39`: 助记词生成和验证
- `ed25519-hd-key`: HD钱包密钥派生

## 开发建议

1. 在开发过程中始终使用DevNet或TestNet
2. 实际部署前在MainNet上进行充分测试
3. 实现适当的错误处理和重试机制
4. 考虑交易费用和网络拥堵情况

## 故障排除

如果遇到问题：
1. 检查网络连接
2. 确认使用的是DevNet
3. 检查账户是否有足够的SOL支付交易费用
4. 查看控制台错误信息

## 许可证

ISC
